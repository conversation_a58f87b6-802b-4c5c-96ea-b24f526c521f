#include "bsp_pc_uart.h"
#include "bsp_mcu_uart.h"

AT_NONCACHEABLE_SECTION_ALIGN_INIT(static uint8_t s_pcUartLBufTx[PC_UART_LBUF_TX_LEN], sizeof(uint8_t));
AT_NONCACHEABLE_SECTION_ALIGN_INIT(static uint8_t s_pcUartLBufRx[PC_UART_LBUF_RX_LEN], sizeof(uint8_t));

/**
 * @brief  pc 模块串口协议解析
 * @param  cmd
 * @param  data
 * @param  len
 */
__weak void pc_uart_protocol_analysis(uint8_t cmd, uint8_t *data, uint8_t len)
{
    
}

/**
 * @brief  缓冲数据校验和
 * @param  data 数据指针
 * @param  len  校验数据长度
 * @return unsigned char
 */
static unsigned char get_check_sum_val(unsigned char *data, unsigned short len)
{
    unsigned char checksum = 0;
    unsigned int i;
    if(data != NULL)
    {
        for(i = 0; i < len; i++)
        {
            checksum += data[i];
        }
    }
    return checksum;
}

/**
 * @brief PC串口事件轮询
 * @return int 0数据长度不够，继续轮询等待 1解析成功 -1解析失败（需要消耗1个byte）
 */
int task_pc_event_poll(RingBuffer_t *pRbBuff)
{
    PcProtocol_t pcMsgInfo;
    //PC接收帧长度
    if(ring_buffer_used_space(pRbBuff) < PC_DECODE_PROTOCOL_LEN)
    {
        return 0;
    }

    ring_buffer_get_offset_data(pRbBuff, 0,
                                (uint8_t *)&pcMsgInfo, sizeof(PcProtocol_t));
    if(pcMsgInfo.bLen > PC_UART_FRAME_MAX_LEN)
    {
        ring_buffer_consume(pRbBuff, 1);
        return -1;
    }
    if(ring_buffer_used_space(pRbBuff) >= (PC_DECODE_PROTOCOL_LEN + pcMsgInfo.bLen))
    {
        ring_buffer_get_offset_data(pRbBuff, 0, s_pcUartLBufRx, PC_DECODE_PROTOCOL_LEN + pcMsgInfo.bLen);

        uint8_t bCmpCkSum = s_pcUartLBufRx[PC_DECODE_PROTOCOL_LEN + pcMsgInfo.bLen - 1];
        uint8_t bCurCkSum = get_check_sum_val(&s_pcUartLBufRx[1], PC_DECODE_PROTOCOL_LEN + pcMsgInfo.bLen - 2);
        //如果校验失败
        if(bCurCkSum != bCmpCkSum)
        {
            ring_buffer_consume(pRbBuff, 1);
            DEBUG_INFO("PC Uart CKSum Error! %d %d", bCurCkSum, bCmpCkSum);
            return -1;
        }
        //协议解析
        pc_uart_protocol_analysis(pcMsgInfo.bCmdCode, &s_pcUartLBufRx[3], pcMsgInfo.bLen);

        ring_buffer_consume(pRbBuff, PC_DECODE_PROTOCOL_LEN + pcMsgInfo.bLen);
        return 1;
    }
    return 0;
}


/**
 * @brief  缓冲数据校验和
 * @param  bCmd 指令
 * @param  pData 数据指针
 * @param  hLen  数据长度
 * @return int 0：返回成功 -1返回失败
 */
int pc_uart_write_frame_data(uint8_t bCmd, uint8_t *pData, uint16_t hLen)
{
    uint16_t cnt = 0;
    s_pcUartLBufTx[cnt++] = PC_UART_HEADER;
    s_pcUartLBufTx[cnt++] = 0xf0;
    s_pcUartLBufTx[cnt++] = hLen + 1;
    s_pcUartLBufTx[cnt++] = bCmd;

    for(int n = 0; n < hLen; n++)
    {
        s_pcUartLBufTx[cnt++] = pData[n];
    }
    s_pcUartLBufTx[cnt++] = get_check_sum_val(&s_pcUartLBufTx[1], 3 + hLen);

    mcu_uart_send_buff(s_pcUartLBufTx, cnt);
    return 0;
}

