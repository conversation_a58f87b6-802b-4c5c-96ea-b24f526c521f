# 设备安全配置与固件升级操作手册

## 一、前提条件
在进行操作之前，请确保电脑已安装以下软件：
1. **Python 3.13**：确保安装了 Python 3.13 版本。
2. **Python 库**：通过命令行工具（CMD）运行以下命令安装必要的 Python 库：
   ```
   pip install cryptography intelhex ecdsa pycryptodome
   ```

## 二、操作步骤

### （一）生产端操作

1. **生成设备配置文件**
   - 打开命令提示符（CMD），切换到脚本所在目录：
     ```
     cd E:\code\American_FDA\SleepRes0307\SleepRes\security\scripts
     ```
   - 运行以下命令生成设备配置文件：
     ```
     python generate_leaf.py -d 88660001
     python python generate_wifi_device_config_v2.py -d 88660001
     ```
   - 该命令将生成以下文件：
     1. **私钥（PEM 格式）**：
        ```
        E:\code\American_FDA\SleepRes0307\SleepRes\security\scripts\../assets\device\88660001\device_private_key-88660001.key
        ```
        - 用于安全操作的私钥文件。
     2. **私钥（DER 格式）**：
        ```
        E:\code\American_FDA\SleepRes0307\SleepRes\security\scripts\../assets\device\88660001\device_private_key-88660001.der
        ```
        - 与设备配置脚本兼容的私钥文件。
     3. **证书签名请求（CSR）**：
        ```
        E:\code\American_FDA\SleepRes0307\SleepRes\security\scripts\../assets\device\88660001\device-88660001.csr
        ```
        - 用于向证书签发者请求证书的文件。
     4. **证书（PEM 格式）**：
        ```
        E:\code\American_FDA\SleepRes0307\SleepRes\security\scripts\../assets\device\88660001\device_cert-88660001.pem
        ```
        - 用于身份验证的签名证书。
     5. **证书（DER 格式）**：
        
        ```
        E:\code\American_FDA\SleepRes0307\SleepRes\security\scripts\../assets\device\88660001\device_cert-88660001.der
        ```
        - 与二进制工具或设备兼容的签名证书。
     6. **设备配置文件（Intel HEX 格式）**：
        ```
        E:\code\American_FDA\SleepRes0307\SleepRes\security\scripts\../assets\device\88660001\device_config-88660001.hex
        ```
        - 用于设备配置的 HEX 文件。
   
2. **烧写设备配置文件**
   - 使用 **J-Flash** 工具将生成的 `device_config-88660001.hex` 文件烧写到设备的 Flash 中。

### （二）研发端操作

1. **生成加密固件**
   - 打开命令提示符（CMD），切换到固件工具脚本所在目录：
     ```
     cd E:\code\American_FDA\SleepRes0307\SleepRes\security\file_utilities
     ```
   - 运行以下命令生成加密固件：
     ```
     python .\file_utilities.py prepare -i .\RES_VENTI.bin -k .\RES_VENTI_ENCRY.bin
     ```
   - 该命令将执行以下操作：
     - 使用标准密钥生成签名。
     - 生成签名后的固件文件：`.\RES_VENTI.signed.bin`。
     - 使用加密密钥对固件进行加密，生成加密固件文件：`.\RES_VENTI_ENCRY.bin`。

2. **固件升级**
   - 将生成的加密固件文件（如 `RES_VENTI_ENCRY.bin`）拷贝到 SD 卡中。
   - 将 SD 卡插入设备，按照设备的升级流程完成固件升级。

### （三）WIFI设备证书配置

1. **准备WIFI证书文件**
   - 确保在 `assets/wifi_device/<设备序列号>/` 目录下准备好以下文件：
     - `ca.cer`: CA证书（外部服务公钥），用于验证服务器
     - `client.key`: 客户端私钥，用于设备身份验证
     - `client.cer`: 客户端证书，包含设备公钥

2. **生成WIFI设备配置文件**
   - 打开命令提示符（CMD），切换到脚本所在目录：
     ```
     cd E:\code\American_FDA\SleepRes0307\SleepRes\security\scripts
     ```
   - 运行以下命令生成WIFI设备配置文件：
     ```
     python generate_wifi_device_config_v2.py -d <设备序列号>
     ```
     例如：
     ```
     python generate_wifi_device_config_v2.py -d 88660001
     ```
   
   - 该脚本执行以下操作：
     1. 读取原始设备配置文件 `assets/device/<设备序列号>/device_config-<设备序列号>.hex`
     2. 将WIFI证书文件写入特定的Flash地址：
        - 0x080FE000：CA证书长度（2字节）
        - 0x080FE002：CA证书数据（`ca.cer`内容）
        - 0x080FE800：设备私钥长度（2字节）
        - 0x080FE802：设备私钥数据（`client.key`内容）
        - 0x080FF000：设备证书长度（2字节）
        - 0x080FF002：设备证书数据（`client.cer`内容）
     3. 生成新的HEX文件：`assets/wifi_device/<设备序列号>/device_config-<设备序列号>.hex`

3. **高级参数选项**
   如果证书文件存储在非默认位置，可以使用以下可选参数：
   ```
   python generate_wifi_device_config_v2.py -d <设备序列号> [--assets-dir <资产目录路径>] [--input-dir <输入目录路径>] [--output-dir <输出目录路径>]
   ```
   - `--assets-dir`：指定包含device和wifi_device目录的资产目录路径
   - `--input-dir`：指定包含原始HEX文件的输入目录路径
   - `--output-dir`：指定生成的HEX文件的输出目录路径

4. **烧写WIFI设备配置文件**
   - 使用 **J-Flash** 工具将生成的 `assets/wifi_device/<设备序列号>/device_config-<设备序列号>.hex` 文件烧写到设备的Flash中。
   - 重启设备后，设备将自动从Flash加载WIFI证书，用于与服务器建立安全连接。

5. **注意事项**
   - 确保证书格式正确，为原始二进制格式（非PEM或其他编码格式）
   - CA证书大小不应超过1.9KB (0x080FE800-0x080FE002)
   - 设备私钥大小不应超过2KB (0x080FF000-0x080FE802)
   - 设备证书大小不应超过2KB (0x080FF800-0x080FF002)
   - 若烧写后WIFI无法连接，请检查日志输出，确认证书是否正确加载

## 三、注意事项
1. 确保所有路径和文件名正确无误，避免因路径错误导致操作失败。
2. 在烧写设备配置文件时，确保设备处于可烧写状态，并按照 J-Flash 的提示完成操作。
3. 在生成加密固件时，确保输入的固件文件和密钥文件路径正确。
4. 如果在操作过程中遇到问题，请检查 Python 环境和相关库是否安装正确，或联系技术支持人员。
5. WIFI证书配置需要在标准设备配置之后进行，确保设备已有基本配置文件。

## 四、技术支持
如在操作过程中遇到任何问题，请及时联系技术支持团队，提供详细的错误信息以便快速解决问题。

---

**版本**：1.1  
**日期**：20250315  
**编写人**：fukangjian
