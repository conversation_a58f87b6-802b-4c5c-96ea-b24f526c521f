#include "main.h"
#include "bsp_stmflash.h"
#include "stm32F4xx_hal_flash.h"
#if RESHOPE_MODEL_2_8
	#include "ResHope_2_8.c"
#elif RESHOPE_MODEL_2_8_ST7789
	#include "ResHope_2_8_ST7789.c"
#elif RESHOPE_MODEL_3_5
	#include "ResHope_3_5.c"
#elif RESFREE_MODEL_3_5
	#include "ResFree_3_5.c"
#elif RESFREE_MODEL_5_0
	#include "ResFree_5_0.c"
#endif

/*
 * @ crc硬件接口
 */
void stm32_crc32_reset(void)
{
	hcrc.Instance->CR = 1;  //复位CRC寄存器
}

uint32_t stm32_crc32_read(void)
{
	return hcrc.Instance->DR;
}

void const_crc32_byte(const unsigned char *pBuff, unsigned int len)
{
    unsigned char buff[4] = {0XFF, 0XFF, 0XFF, 0XFF};
    unsigned int *p32 = (unsigned int *)buff;

    for(int i = 0; i < len; i++)
    {
        buff[3] = pBuff[i];
        hcrc.Instance->DR = *p32;
    }
}

/*
*********************************************************************************************************
*	函 数 名: uint32_t stm_flash_read_word(uint32_t faddr)
*	功能说明: 读取数据
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
uint32_t stm_flash_read_word(uint32_t faddr)
{
    return *(__IO uint32_t *)faddr;
}
/*
*********************************************************************************************************
*	函 数 名: uint32_t stm_flash_addr_to_id(uint32_t addr)
*	功能说明: 写入数据
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
uint32_t stm_flash_addr_to_id(uint32_t Address)
{
      uint32_t sector = 0;
  
      if((Address < ADDR_FLASH_SECTOR_1_BANK1) && (Address >= ADDR_FLASH_SECTOR_0_BANK1))
      {
        sector = FLASH_SECTOR_0;  
      }
      else if((Address < ADDR_FLASH_SECTOR_2_BANK1) && (Address >= ADDR_FLASH_SECTOR_1_BANK1))
      {
        sector = FLASH_SECTOR_1;  
      }
      else if((Address < ADDR_FLASH_SECTOR_3_BANK1) && (Address >= ADDR_FLASH_SECTOR_2_BANK1)) 
      {
        sector = FLASH_SECTOR_2;  
      }
      else if((Address < ADDR_FLASH_SECTOR_4_BANK1) && (Address >= ADDR_FLASH_SECTOR_3_BANK1))  
      {
        sector = FLASH_SECTOR_3;  
      }
      else if((Address < ADDR_FLASH_SECTOR_5_BANK1) && (Address >= ADDR_FLASH_SECTOR_4_BANK1)) 
      {
        sector = FLASH_SECTOR_4;  
      }
      else if((Address < ADDR_FLASH_SECTOR_6_BANK1) && (Address >= ADDR_FLASH_SECTOR_5_BANK1))
      {
        sector = FLASH_SECTOR_5;  
      }
      else if((Address < ADDR_FLASH_SECTOR_7_BANK1) && (Address >= ADDR_FLASH_SECTOR_6_BANK1))
      {
        sector = FLASH_SECTOR_6;  
      }
      else if((Address < ADDR_FLASH_SECTOR_8_BANK1) && (Address >= ADDR_FLASH_SECTOR_7_BANK1))
      {
         sector = FLASH_SECTOR_7;  
      }
	  else if((Address < ADDR_FLASH_SECTOR_9_BANK1) && (Address >= ADDR_FLASH_SECTOR_8_BANK1))
      {
         sector = FLASH_SECTOR_8;  
      }
	  else if((Address < ADDR_FLASH_SECTOR_10_BANK1) && (Address >= ADDR_FLASH_SECTOR_9_BANK1))
      {
         sector = FLASH_SECTOR_9;  
      }
	  else if((Address < ADDR_FLASH_SECTOR_11_BANK1) && (Address >= ADDR_FLASH_SECTOR_10_BANK1))
      {
         sector = FLASH_SECTOR_10;  
      }
	  else if((Address < FLASH_END_ADDR) && (Address >= ADDR_FLASH_SECTOR_11_BANK1))
      {
         sector = FLASH_SECTOR_11;  
      }
      else
      {
        sector = FLASH_SECTOR_11;
      }

      return sector;
}

/*
*********************************************************************************************************
*	函 数 名: void stm_flash_erase(uint32_t write_addr, uint32_t nwrite)
*	功能说明: 擦除FLASH
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void stm_flash_erase(uint32_t write_addr, uint32_t nwrite)
{
    uint32_t UserStartSector;
    uint32_t SectorError;
    FLASH_EraseInitTypeDef pEraseInit;

    HAL_FLASH_Unlock(); 
    __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_OPERR | FLASH_FLAG_WRPERR | 
                         FLASH_FLAG_PGSERR | FLASH_FLAG_WRPERR);
    
    UserStartSector = stm_flash_addr_to_id(write_addr);
 
    pEraseInit.TypeErase = FLASH_TYPEERASE_SECTORS;
    pEraseInit.Sector = UserStartSector;
    pEraseInit.NbSectors = stm_flash_addr_to_id(write_addr + nwrite) - UserStartSector + 1;
    pEraseInit.VoltageRange = FLASH_VOLTAGE_RANGE_3;
    
    if (HAL_FLASHEx_Erase(&pEraseInit, &SectorError) != HAL_OK)
    {
        /* Error occurred while page erase */

    }
    HAL_FLASH_Lock();
}
/*
*********************************************************************************************************
*	函 数 名: void stm_flash_write(uint32_t write_addr, uint8_t *pbuffer, uint32_t nwrite)
*	功能说明: 写入数据
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void stm_flash_write(uint32_t write_addr, uint8_t *pbuffer, uint32_t nwrite)
{
    HAL_StatusTypeDef status;
    unsigned int endaddr = 0;
    if(write_addr < STM32_FLASH_BASE)
    {
        return;
    }

    HAL_FLASH_Unlock(); 

    endaddr = write_addr + nwrite;

    while(write_addr < endaddr)
    {
        uint32_t writeData = *(uint32_t*)pbuffer;
        if(HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, write_addr, writeData) != HAL_OK)
        {
            break;
        }
        status = FLASH_WaitForLastOperation(50000);
        if (*(uint32_t*)write_addr != *(uint32_t*)(pbuffer))
        {
            /* Flash content doesn't match SRAM content */
            return;
        }
        
        write_addr += 4;
        pbuffer += 4;
    }

    HAL_FLASH_Lock();
}

void upgrade_boot_by_app(void)
{
	uint32_t old_crc32 = 0;
	uint8_t bCheckCnt = 3;
#if RESHOPE_MODEL_2_8
	uint32_t new_crc32 = 0xA59E7BFB;
#elif RESHOPE_MODEL_2_8_ST7789
	uint32_t new_crc32 = 0x8A553C30;
#elif RESHOPE_MODEL_3_5
	uint32_t new_crc32 = 0x1BE4B95C;
#elif RESFREE_MODEL_3_5
	uint32_t new_crc32 = 0xD97EFDFB;
#elif RESFREE_MODEL_5_0
	uint32_t new_crc32 = 0x0BE1B811;
#endif
	//检查boot地址区域crc32
	stm32_crc32_reset();
	const_crc32_byte((const uint8_t*)ADDR_FLASH_SECTOR_0_BANK1, sizeof(bootloardData));
	old_crc32 = stm32_crc32_read();

	stm32_crc32_reset();
	const_crc32_byte((const uint8_t*)bootloardData, sizeof(bootloardData));
	new_crc32 = stm32_crc32_read();
	
	if(old_crc32 != new_crc32)
	{
		HAL_Delay(2000); //上电等待2秒稳定稳定
		uint8_t w_buffer[1024];
		int offset = 0, objsize = sizeof(bootloardData);
		int w_len = 0;
		while(bCheckCnt --)
		{
			stm_flash_erase(ADDR_FLASH_SECTOR_0_BANK1, sizeof(bootloardData));
			
			while(objsize)
			{
				w_len = objsize > sizeof(w_buffer) ? sizeof(w_buffer) : objsize;
				memset(w_buffer, 0xff, sizeof(w_buffer));
				memcpy(w_buffer, bootloardData + offset, w_len);
				stm_flash_write(ADDR_FLASH_SECTOR_0_BANK1 + offset, w_buffer, w_len);

				offset += w_len;
				objsize -= w_len;
			}
			
			stm32_crc32_reset();
			const_crc32_byte((const uint8_t*)ADDR_FLASH_SECTOR_0_BANK1, sizeof(bootloardData));
			old_crc32 = stm32_crc32_read();
			
			if(old_crc32 == new_crc32)
			{
				NVIC_SystemReset(); //软复位，以便重新进行程序引导
			}
		}
	}
}




