/*********************************************************************
*                                                                    *
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
*                                                                    *
**********************************************************************
*                                                                    *
* C-file generated by:                                               *
*                                                                    *
*        GUI_Builder for emWin version 5.32                          *
*        Compiled Oct  8 2015, 11:59:02                              *
*        (c) 2015 Segger Microcontroller GmbH & Co. KG               *
*                                                                    *
**********************************************************************
*                                                                    *
*        Internet: www.segger.com  Support: <EMAIL>       *
*                                                                    *
**********************************************************************
*/

// USER START (Optionally insert additional includes)
#include "StaterBar.h"
#include "Key.h"
#include "GlobalVariable.h"
#include "Humidi.h"
#include "PipeAndHumCtr.h"
#include "ConfigSave.h"
#include "FlowDispose.h"
#include "stack.h"
#include "FlowSensorType.h"
// USER END

#include "DIALOG.h"


/*********************************************************************
*
*       Defines
*
**********************************************************************
*/
#define ID_WINDOW_0    (GUI_ID_USER + 0x00)
#define ID_TEXT_0    (GUI_ID_USER + 0x01)
#define ID_TEXT_1    (GUI_ID_USER + 0x02)
#define ID_TEXT_2    (GUI_ID_USER + 0x03)
#define ID_BUTTON_0    (GUI_ID_USER + 0x04)
#define ID_BUTTON_1    (GUI_ID_USER + 0x05)
#define ID_BUTTON_2    (GUI_ID_USER + 0x06)
#define ID_BUTTON_3    (GUI_ID_USER + 0x07)
#define ID_BUTTON_4    (GUI_ID_USER + 0x08)
#define ID_BUTTON_5    (GUI_ID_USER + 0x09)
#define ID_BUTTON_6    (GUI_ID_USER + 0x0A)
#define ID_BUTTON_7    (GUI_ID_USER + 0x0B)
#define ID_BUTTON_8    (GUI_ID_USER + 0x0C)
#define ID_BUTTON_9    (GUI_ID_USER + 0x0D)
#define ID_BUTTON_10    (GUI_ID_USER + 0x0E)
#define ID_BUTTON_11    (GUI_ID_USER + 0x0F)
#define ID_BUTTON_12    (GUI_ID_USER + 0x10)
#define ID_BUTTON_13    (GUI_ID_USER + 0x11)
#define ID_BUTTON_14    (GUI_ID_USER + 0x12)
#define ID_BUTTON_15    (GUI_ID_USER + 0x13)
#define ID_BUTTON_16    (GUI_ID_USER + 0x14)
#define ID_BUTTON_17    (GUI_ID_USER + 0x15)
#define ID_BUTTON_18    (GUI_ID_USER + 0x16)
#define ID_BUTTON_19    (GUI_ID_USER + 0x17)
#define ID_BUTTON_20    (GUI_ID_USER + 0x18)
#define ID_BUTTON_21    (GUI_ID_USER + 0x19)
#define ID_BUTTON_22    (GUI_ID_USER + 0x1A)
#define ID_BUTTON_23    (GUI_ID_USER + 0x1B)
#define ID_BUTTON_24    (GUI_ID_USER + 0x1C)
#define ID_TEXT_3    (GUI_ID_USER + 0x1D)
#define ID_TEXT_4    (GUI_ID_USER + 0x1E)
#define ID_TEXT_5    (GUI_ID_USER + 0x1F)
#define ID_TEXT_6    (GUI_ID_USER + 0x20)
#define ID_TEXT_7    (GUI_ID_USER + 0x21)
#define ID_TEXT_8    (GUI_ID_USER + 0x22)
#define ID_TEXT_9    (GUI_ID_USER + 0x23)
#define ID_TEXT_10    (GUI_ID_USER + 0x24)

void SetFlowOffset(uint8_t u8Type,int s16SetValue,uint8_t OffsetType);
// USER START (Optionally insert additional defines)
// USER END

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/

// USER START (Optionally insert additional static data)
WM_HWIN	g_DebugSetOffsetHwin = NULL;
static void ShowProcess(void);
WINDOWINFO g_DebugSetOffsetWInfo = {NULL, NULL, NULL, NULL, ShowProcess, NULL, 0, 0, DEBUGSETOFFSET_ID};
// USER END
U8 GetFocus(U8* pKeyValue)//获取焦点变动
{
    if(WM_HasFocus(WM_GetDialogItem(g_DebugSetOffsetHwin,ID_BUTTON_0)) && *pKeyValue == GUI_KEY_BACKTAB)
    {
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_8));
        return 0;
    }
    if(WM_HasFocus(WM_GetDialogItem(g_DebugSetOffsetHwin,ID_BUTTON_8)) && *pKeyValue == GUI_KEY_TAB)
    {
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_0));
        return 0;
    }
    if(WM_HasFocus(WM_GetDialogItem(g_DebugSetOffsetHwin,ID_BUTTON_9)) && *pKeyValue == GUI_KEY_BACKTAB)
    {
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_16));
        return 0;
    }
    if(WM_HasFocus(WM_GetDialogItem(g_DebugSetOffsetHwin,ID_BUTTON_16)) && *pKeyValue == GUI_KEY_TAB)
    {
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_9));
        return 0;
    }
    if(WM_HasFocus(WM_GetDialogItem(g_DebugSetOffsetHwin,ID_BUTTON_17)) && *pKeyValue == GUI_KEY_BACKTAB)
    {
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_24));
        return 0;
    }
    if(WM_HasFocus(WM_GetDialogItem(g_DebugSetOffsetHwin,ID_BUTTON_24)) && *pKeyValue == GUI_KEY_TAB)
    {
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_17));
        return 0;
    }
    return 1;
}

/*********************************************************************
*
*       _aDialogCreate
*/
#if (LCD_TYPE == LCD_5_TFT)
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] = {
  { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 70, 800, 410, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_0, 0, 0, 800, 48, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_1, 85, 60, 170, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_2, 85, 210, 170, 24, 0, 0x64, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_0, 85, 90, 170, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_1, 85, 120, 170, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_2, 85, 150, 170, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_3, 85, 180, 170, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_4, 85, 240, 170, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_5, 85, 270, 170, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_6, 85, 300, 170, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_7, 85, 330, 170, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_8, 85, 360, 170, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_9, 370, 120, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_10, 370, 150, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_11, 370, 180, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_12, 370, 210, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_13, 370, 240, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_14, 370, 270, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_15, 370, 300, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_16, 370, 330, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_17, 600, 120, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_18, 600, 150, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_19, 600, 180, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_20, 600, 210, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_21, 600, 240, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_22, 600, 270, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_23, 600, 300, 120, 24, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_24, 600, 330, 120, 24, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_3, 370, 60, 180, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_4, 600, 60, 180, 24, 0, 0x64, 0},
  { TEXT_CreateIndirect, "Text", ID_TEXT_5, 260, 120, 80, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_6, 260, 150, 80, 16, 0, 0x64, 0},
  { TEXT_CreateIndirect, "Text", ID_TEXT_7, 260, 180, 80, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_8, 260, 270, 80, 16, 0, 0x64, 0},
  { TEXT_CreateIndirect, "Text", ID_TEXT_9, 260, 300, 80, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_10, 260, 330, 80, 16, 0, 0x64, 0},
  // USER START (Optionally insert additional widgets)
  // USER END
};
#else
#if (LCD_TYPE == LCD_28_TFT)
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] = {
  { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 46, 240, 320 - 46, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_0, 0, 0, 240, 48, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_1, 0, 40, 100, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_2, 0, 140, 100, 16, 0, 0x64, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_0, 0, 60, 70, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_1, 0, 80, 70, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_2, 0, 100, 70, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_3, 0, 120, 70, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_4, 0, 160, 70, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_5, 0, 180, 70, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_6, 0, 200, 70, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_7, 0, 220, 70, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_8, 0, 240, 70, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_9, 110, 80, 60, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_10, 110, 100, 60, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_11, 110, 120, 60, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_12, 110, 140, 60, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_13, 110, 160, 60, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_14, 110, 180, 60, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_15, 110, 200, 60, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_16, 110, 220, 60, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_17, 172, 80, 68, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_18, 172, 100, 68, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_19, 172, 120, 68, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_20, 172, 140, 68, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_21, 172, 160, 68, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_22, 172, 180, 68, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_23, 172, 200, 68, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_24, 172, 220, 68, 16, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_3, 110, 60, 65, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_4, 172, 60, 68, 16, 0, 0x64, 0},
  { TEXT_CreateIndirect, "Text", ID_TEXT_5, 70, 80, 50, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_6, 70, 100, 50, 16, 0, 0x64, 0},
  { TEXT_CreateIndirect, "Text", ID_TEXT_7, 70, 120, 50, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_8, 70, 180, 50, 16, 0, 0x64, 0},
  { TEXT_CreateIndirect, "Text", ID_TEXT_9, 70, 200, 50, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_10, 70, 220, 50, 16, 0, 0x64, 0},
  // USER START (Optionally insert additional widgets)
  // USER END
#else
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] = {
  { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 46, 480, 320 - 46, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_0, 0, 0, 480, 48, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_1, 51, 40, 110, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_2, 51, 140, 110, 16, 0, 0x64, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_0, 51, 60, 110, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_1, 51, 80, 110, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_2, 51, 100, 110, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_3, 51, 120, 110, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_4, 51, 160, 110, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_5, 51, 180, 110, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_6, 51, 200, 110, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_7, 51, 220, 110, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_8, 51, 240, 110, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_9, 240, 80, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_10, 240, 100, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_11, 240, 120, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_12, 240, 140, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_13, 240, 160, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_14, 240, 180, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_15, 240, 200, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_16, 240, 220, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_17, 360, 80, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_18, 360, 100, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_19, 360, 120, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_20, 360, 140, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_21, 360, 160, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_22, 360, 180, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_23, 360, 200, 72, 16, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_24, 360, 220, 72, 16, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_3, 240, 60, 70, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_4, 360, 60, 70, 16, 0, 0x64, 0},
  { TEXT_CreateIndirect, "Text", ID_TEXT_5, 165, 80, 50, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_6, 165, 100, 50, 16, 0, 0x64, 0},
  { TEXT_CreateIndirect, "Text", ID_TEXT_7, 165, 120, 50, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_8, 165, 180, 50, 16, 0, 0x64, 0},
  { TEXT_CreateIndirect, "Text", ID_TEXT_9, 165, 200, 50, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_10, 165, 220, 50, 16, 0, 0x64, 0},
  // USER START (Optionally insert additional widgets)
  // USER END
#endif
};
#endif

/*********************************************************************
*
*       Static code
*
**********************************************************************
*/

// USER START (Optionally insert additional static code)
// USER END

/*********************************************************************
*
*       _cbDialog
*/
static void _cbDialog(WM_MESSAGE * pMsg) {
  WM_HWIN hItem;
  int     NCode;
  int     Id;
  char TmpTextStr[8];
  U8 i;
  static U8 u8TitType = 0,u8TitOffsetType = 0;
  static int s16OffsetValue = 0;
  const TITCONFIG* TitConfig = g_ConfigSave.GetTitConfig();    

  switch (pMsg->MsgId) {
  case WM_INIT_DIALOG:
    //
    // Initialization of 'Window'
    //
    hItem = pMsg->hWin;
    WINDOW_SetBkColor(hItem, GUI_MAKE_COLOR(0x00000000));
    //
    // Initialization of 'Text'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_0);
    TEXT_SetTextAlign(hItem, GUI_TA_HCENTER | GUI_TA_VCENTER);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetText(hItem, GetMultiLanguageString((ESTRINGIDX)DEBUG_OFFSET_IDX));
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_1);
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    TEXT_SetText(hItem,GetMultiLanguageString((ESTRINGIDX)DEBUG_FLOWOFFSET_IDX));

    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_2);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetText(hItem, GetMultiLanguageString((ESTRINGIDX)DEBUG_PRESSOFFSET_IDX));
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_3);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    TEXT_SetText(hItem, "0L/min");

    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_4);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    TEXT_SetText(hItem, "0cmH2o");

    for (i = 0; i < 3; i++)
    {
        hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_5 + i);
        TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
        #if (LCD_TYPE == LCD_5_TFT)
            TEXT_SetFont(hItem, FONT_24);
        #else
            TEXT_SetFont(hItem, FONT_18);
        #endif
        TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
        if ((GetSensorType() == ADP800) || (GetSensorType() == HDP800))
        {
            sprintf(TmpTextStr, "%d", (int16_t)TitConfig->Adp801TitFlow[1 + i]);
        }
        else
        {
            sprintf(TmpTextStr, "%d", TitConfig->TitFlow[0 + i]);
        }
        TEXT_SetText(hItem, TmpTextStr);
    }
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_8);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    sprintf(TmpTextStr, "%d", TitConfig->TitPress[1]);
    TEXT_SetText(hItem, TmpTextStr);;
    
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_9);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    sprintf(TmpTextStr, "%d", TitConfig->TitPress[2]);
    TEXT_SetText(hItem, TmpTextStr);
    
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_10);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    sprintf(TmpTextStr, "%d", TitConfig->TitPress[3]);
    TEXT_SetText(hItem, TmpTextStr);
    // USER START (Optionally insert additional code for further widget initialization)
    for (i = 0; i < 25; i++)
    {
            if(i < 3)
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(DEBUG_ALLOFFSET_IDX + i)));
            }
            else if (i == 3)
            {
                if ((GetSensorType() == ADP800) || (GetSensorType() == HDP800))
                {
                    BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(DEBUG_F140OFFSET_IDX)));
                }
                else
                {
                    BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(DEBUG_F120OFFSET_IDX)));
                }
            }
            else if(i == 4)
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(DEBUG_ALLOFFSET_IDX)));
            }
            else if(i < 8)
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(DEBUG_P4OFFSET_IDX+ i-5)));
            }
            else if(i == 8)
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(DEBUG_BUZZEREXIT_IDX)));
            }
            else if(i < 12)
            {
                sprintf(TmpTextStr, "+%dL/min", 12-i);
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), TmpTextStr);
            }
            else if(i < 15)
            {
                sprintf(TmpTextStr, "-%dL/min", 15-i);
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), TmpTextStr);
            }
            else if(i == 15)
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(CALI_OK_IDX)));
            }
            else if(i == 16)
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(DEBUG_EXIT_IDX)));
            }
            else if(i < 20)
            {
                sprintf(TmpTextStr, "+%dcmH2o", 20-i);
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), TmpTextStr);
            }
            else if(i < 23)
            {
                sprintf(TmpTextStr, "-%dcmH2o", 23-i);
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), TmpTextStr);
            }
            else if(i == 23)
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(CALI_OK_IDX)));
            }
            else
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), GetMultiLanguageString((ESTRINGIDX)(DEBUG_EXIT_IDX)));
            }
            #if (LCD_TYPE == LCD_5_TFT)
                BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), FONT_24);
            #else
                BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), FONT_18);
            #endif
            _SetButtonSkin(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i));
    }
    // USER END
    break;
  case WM_NOTIFY_PARENT:
    Id    = WM_GetId(pMsg->hWinSrc);
    NCode = pMsg->Data.v;
    switch(Id) {
    case ID_BUTTON_0: //流量整体偏移
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        u8TitType = 0;
        u8TitOffsetType = 0;
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_9));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
     case ID_BUTTON_1: //30L/min
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
          u8TitType = 1;
        u8TitOffsetType = 0;
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_9));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
     case ID_BUTTON_2: //80L/min
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        u8TitType = 2;
        u8TitOffsetType = 0;
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_9));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
     case ID_BUTTON_3: //120L/min
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        u8TitType = 3;
        u8TitOffsetType = 0;
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_9));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
    case ID_BUTTON_4: //压力整体偏移
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        u8TitType = 0;
        u8TitOffsetType = 1;
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_17));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_5: // 4cmH2o
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        u8TitType = 1;
        u8TitOffsetType = 1;
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_17));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_6: // 10cmH2o
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        u8TitType = 2;
        u8TitOffsetType = 1;
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_17));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_7: //20cmH2o
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        u8TitType = 3;
        u8TitOffsetType = 1;
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_17));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_8: // 退出
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        ShowDebugSetOffset(0);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_9: // +3
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue += 3;
        sprintf(TmpTextStr, "%dL/min", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_3), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_10: // +2
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue += 2;
        sprintf(TmpTextStr, "%dL/min", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_3), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_11: // +1
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue += 1;
        sprintf(TmpTextStr, "%dL/min", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_3), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_12: // -3
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue -= 3;
        sprintf(TmpTextStr, "%dL/min", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_3), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_13: // -2
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue -= 2;
        sprintf(TmpTextStr, "%dL/min", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_3), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_14: // -1
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue -= 1;
        sprintf(TmpTextStr, "%dL/min", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_3), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_15: // 确认偏移
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        SetFlowOffset(u8TitType, s16OffsetValue, u8TitOffsetType);          //计算偏移与存储偏移后的流量
        s16OffsetValue = 0;
        sprintf(TmpTextStr, "%dL/min", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_3), TmpTextStr);
        for (i = 0; i < 3; i++)
        {
            if ((GetSensorType() == ADP800) || (GetSensorType() == HDP800))
            {
                sprintf(TmpTextStr, "%d", TitConfig->Adp801TitFlow[1 + i]);
            }
            else
            {
                sprintf(TmpTextStr, "%d", TitConfig->TitFlow[i]);
            }
            TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_5 + i), TmpTextStr);
        }
		CalcADCodeToFlow();     //偏移后，实时更新曲线，方便复测
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_0));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_16: // 返回
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue = 0;
        sprintf(TmpTextStr, "%dL/min", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_3), TmpTextStr);
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_0));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_17: // +3
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue += 3;
        sprintf(TmpTextStr, "%dcmH2o", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_4), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_18: // +2
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue += 2;
        sprintf(TmpTextStr, "%dcmH2o", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_4), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_19: // +1
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue += 1;
        sprintf(TmpTextStr, "%dcmH2o", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_4), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_20: // -3
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue -= 3;
        sprintf(TmpTextStr, "%dcmH2o", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_4), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_21: // -2
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue -= 2;
        sprintf(TmpTextStr, "%dcmH2o", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_4), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_22: // -1
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue -= 1;
        sprintf(TmpTextStr, "%dcmH2o", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_4), TmpTextStr);
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_23: // 确认偏移
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        SetFlowOffset(u8TitType, s16OffsetValue, u8TitOffsetType);          //计算流量偏移与存储偏移后的流量
        s16OffsetValue = 0;
        sprintf(TmpTextStr, "%dcmH2o", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_4), TmpTextStr);
        sprintf(TmpTextStr, "%d", TitConfig->TitPress[1]);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_8), TmpTextStr);
        sprintf(TmpTextStr, "%d", TitConfig->TitPress[2]);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_9), TmpTextStr);
        sprintf(TmpTextStr, "%d", TitConfig->TitPress[3]);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_10), TmpTextStr);
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_4));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
      case ID_BUTTON_24: // 返回
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        s16OffsetValue = 0;
        sprintf(TmpTextStr, "%dcmH2o", s16OffsetValue);
        TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_4), TmpTextStr);
        WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_4));
        break;
      case WM_NOTIFICATION_RELEASED:
        break;
      }
      break;
    }
    break;
  default:
    WM_DefaultProc(pMsg);
    break;
  }
}

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/

// USER START (Optionally insert additional public code)
void ShowDebugSetOffset(U8 Flag)
{
    if (Flag)
    {
        if (g_DebugSetOffsetHwin == NULL)
        {        
            g_DebugSetOffsetHwin = GUI_CreateDialogBox(_aDialogCreate, GUI_COUNTOF(_aDialogCreate), _cbDialog, WM_HBKWIN, 0, 0);
        }
        ShowProcess();
        g_WindowDrv.PushWindow(&g_DebugSetOffsetWInfo);        
    }
    else
    {
        WM_HideWindow(g_DebugSetOffsetHwin);
        g_WindowDrv.PopWindow(&g_DebugSetOffsetWInfo);
    }	
}

static void ShowProcess(void)
{
    WM_SetFocus(WM_GetDialogItem(g_DebugSetOffsetHwin, ID_BUTTON_0));
    WM_ShowWindow(g_DebugSetOffsetHwin);
}

/*******************************************************************************
*   函数名: SetFlowTotalOffse
*   功  能：流量偏移值计算与存储
*   参  数: i8Focus 当前光标,  u8MaxValue: 最大光标
*   返  回: 无
*/
void  SetFlowTotalOffset(uint16_t u16StorageFlow,uint16_t u16frontTitFlow,uint16_t u16BackTitFlow,uint16_t diff,int16_t s16OffseValue)
{
    const TITCONFIG* TitConfig = g_ConfigSave.GetTitConfig();
    uint16_t u16Tmp;
    u16Tmp =  u16BackTitFlow + ((u16BackTitFlow - u16frontTitFlow) * s16OffseValue  / diff);
    g_ConfigSave.SetParameter((ECONFIGTYPE)u16StorageFlow, u16Tmp);

}

/*******************************************************************************
*   函数名: SetFlowOffset
*   功  能：SetFlowOffset  流量偏移值调转
*   参  数: u8Type 偏移类型0，总体偏移，1:30流量偏移,2:80流量偏移 3:120L流量偏移  buff: 缓冲区
*   返  回: 无
*/

void SetFlowOffset(uint8_t u8Type,int s16SetValue,uint8_t OffsetType)
{
    uint16_t u16StorageData = 0, u16step = 0, u16FrontData = 0, u16BackData = 0;
    const TITCONFIG* TitConfig = g_ConfigSave.GetTitConfig();
    if(OffsetType == 0)
    {
        if ((GetSensorType() == ADP800) || (GetSensorType() == HDP800))
        {
            switch(u8Type)
            {
                case 0: //总体偏移
                {
                    SetFlowTotalOffset(TIT30FLOW, 0, TitConfig->Adp801TitFlow[1],30,s16SetValue);
                    SetFlowTotalOffset(TIT140FLOW, TitConfig->Adp801TitFlow[2],TitConfig->Adp801TitFlow[2],40, s16SetValue);
                    SetFlowTotalOffset(TIT80FLOW, TitConfig->Adp801TitFlow[1],TitConfig->Adp801TitFlow[1],50, s16SetValue);
                } break;
                case 1: //30L/min偏移
                {
                    u16step = 30; //设置步长
                    u16FrontData = 0;
                    u16BackData = TitConfig->Adp801TitFlow[1]; //设置偏移的定标数据
                    u16StorageData = TIT30FLOW; //设置偏移的定标点的变量名
                } break;
                case 2: //80L/min偏移
                {
                    u16FrontData = TitConfig->Adp801TitFlow[1];
                    u16BackData = TitConfig->Adp801TitFlow[2];
                    u16step = 50;
                    u16StorageData = TIT80FLOW;
                } break;
                case 3: //140L/min偏移, ADP流量传感器为140L/min偏移
                {
                    u16FrontData = TitConfig->Adp801TitFlow[2];
                    u16BackData = TitConfig->Adp801TitFlow[3];
                    u16step = 40; 
                    u16StorageData = TIT140FLOW;
                } break;
                default:
                    return;
            }
        }
        else
        {
            switch(u8Type)
            {
                case 0: //总体偏移
                {
                    SetFlowTotalOffset(TIT30FLOW, 0, TitConfig->TitFlow[0],30,s16SetValue);
                    SetFlowTotalOffset(TIT120FLOW, TitConfig->TitFlow[1],TitConfig->TitFlow[2],40, s16SetValue);
                    SetFlowTotalOffset(TIT80FLOW, TitConfig->TitFlow[0],TitConfig->TitFlow[1],50, s16SetValue);
                } break;
                case 1: //30L/min偏移
                {
                    u16step = 30; //设置步长
                    u16FrontData = 0;
                    u16BackData = TitConfig->TitFlow[0]; //设置偏移的定标数据
                    u16StorageData = TIT30FLOW; //设置偏移的定标点的变量名
                } break;
                case 2: //80L/min偏移
                {
                    u16FrontData = TitConfig->TitFlow[0];
                    u16BackData = TitConfig->TitFlow[1];
                    u16step = 50;
                    u16StorageData = TIT80FLOW;
                } break;
                case 3: //120L/min偏移, ADP流量传感器为140L/min偏移
                {
                    u16FrontData = TitConfig->TitFlow[1];
                    u16BackData = TitConfig->TitFlow[2];
                    u16step = 40; 
                    u16StorageData = TIT120FLOW;
                } break;
                default:
                    return;
            }
        }
    }
    else
    {
        switch(u8Type)
        {
            case  0:
                SetFlowTotalOffset(TIT4PRESS, TitConfig->TitPress[0], TitConfig->TitPress[1],40,s16SetValue);
                SetFlowTotalOffset(TIT20PRESS, TitConfig->TitPress[2],TitConfig->TitPress[3],100, s16SetValue);
                SetFlowTotalOffset(TIT10PRESS, TitConfig->TitPress[1],TitConfig->TitPress[2],60, s16SetValue);
            break;
            case 1:
                u16step = 40;
                u16FrontData = TitConfig->TitPress[0];
                u16BackData = TitConfig->TitPress[1];
                u16StorageData = TIT4PRESS;
                break;
            case 2:
                u16step = 60;
                u16FrontData = TitConfig->TitPress[1];
                u16BackData = TitConfig->TitPress[2];
                u16StorageData = TIT10PRESS;
                break;
            case 3:
                u16step = 100;
                u16FrontData = TitConfig->TitPress[2];
                u16BackData = TitConfig->TitPress[3];
                u16StorageData = TIT20PRESS;
                break;
            default:
                return;
        }
    }
    SetFlowTotalOffset(u16StorageData, u16FrontData, u16BackData, u16step, s16SetValue);
}
// USER END

/*************************** End of file ****************************/


