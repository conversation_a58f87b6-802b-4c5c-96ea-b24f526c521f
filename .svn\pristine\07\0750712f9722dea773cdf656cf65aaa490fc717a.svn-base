#include "WifiSelectedScreen.h"
#include "Led.h"
#include <stdio.h>
#include "Key.h"
#include "ConfigSave.h"
#include "MultiLanguage.h"
#include "GlobalVariable.h"
#include "StaterBar.h"
#include "bsp_bldcm_control.h"
#include "WifiUartModule.h"
#include "AppBMP.h"
#include "stack.h"

void RepaintWifiSelectedScreen(void);
static void WifiSelectedScreenProcess(U8 Key);
static void ExitWifiTimer();
static void WifiSelectedScreenSinkProcess();
static void ShowProcess();
WINDOWINFO g_WifiSelectedScreenWInfo = {NULL, WifiSelectedScreenProcess, ExitWifiTimer, RepaintWifiSelectedScreen, ShowProcess, WifiSelectedScreenSinkProcess, 0, 0, WIFISELECTED_ID};

const U16 g_U16WifiSignalQualityBmpIdx[][4] = {{BMP_WIFINOLOCKICON1, BMP_WIFINOLOCKICON2, BMP_WIFINOLOCKICON3, BMP_WIFINOLOCKICON4},
                                                {BMP_WIFILOCKICON1, BMP_WIFILOCKICON2, BMP_WIFILOCKICON3, BMP_WIFILOCKICON4}};

const U16 g_U16FocusWifiSignalQualityBmpIdx[][4] = {{BMP_FOCUSWIFINOLOCKICON1, BMP_FOCUSWIFINOLOCKICON2, BMP_FOCUSWIFINOLOCKICON3, BMP_FOCUSWIFINOLOCKICON4},
                                                {BMP_FOCUSWIFILOCKICON1, BMP_FOCUSWIFILOCKICON2, BMP_FOCUSWIFILOCKICON3, BMP_FOCUSWIFILOCKICON4}};

//static const GUI_BITMAP* g_pBitmap_Wifi[5] = {&bmWifiLockIcon3, &bmWifiLockIcon1, &bmWifiNoLockIcon3, &bmWifiNoLockIcon3, &bmWifiNoLockIcon3};

WM_HWIN	g_WifiSelectedScreenHwin = NULL;

static GUI_TIMER_HANDLE g_RefreshWifiTimer = NULL;
static PAPLIST g_pAPList = NULL;
static U8 g_U8WifiScanTicket = 0;   //ÿ30Sɨһ
static U8 g_U8WifiScanStatus = 0;   //0:δɨ 1:ɨ 2:ɨ
static U8 g_U8CanScanStatus = 0;    //0��������ɨ�� 1:����ɨ�� �ڵ�������
                                                            
static uint8_t g_sU8LastWifiSelectedScreenFocus = 0;

#if (LCD_TYPE == LCD_28_TFT)
static void DrawNewWifiSelectedScreen(uint8_t u8Page, uint8_t u8IsFirst);
#else
static void ChangeFocusNoPageTurning_WifiSelected(uint8_t u8CurrentFocus, uint8_t u8OldFocus);
static void DrawNewWifiSelectedScreen(uint8_t u8CurrentFocus);
#endif
static void ChangeFocusWifiSetting_Left(void);
static void ChangeFocusWifiSetting_Right(void);
static void ChangeFocusWifiSelectedScreen(U8 UpFlag);
static void wifi_SSID_EachRow(uint8_t u8Row);

static void OnEnterClick_WifiSelectedScreen(void);
static void DrawScanStatus(U8 bFlag);

static void WifiSelectedScreenProcess(U8 Key)
{
    switch (Key)
    {
        case GUI_KEY_BACKTAB:            
            ChangeFocusWifiSelectedScreen(1);
            break;
        case GUI_KEY_TAB:
            ChangeFocusWifiSelectedScreen(0);
            break;
        case GUI_KEY_ENTER:             
        case GUI_KEY_F1:
            OnEnterClick_WifiSelectedScreen();
            break;
        default:
            break;
        }       
}

static void ExitWifiTimer()
{
    GUI_TIMER_DELETE(g_RefreshWifiTimer);
}

static void WifiSelectedScreenSinkProcess()
{
    g_U8CanScanStatus = 0;
}

static void ShowProcess()
{
    g_U8CanScanStatus = 1;
    g_U8WifiScanTicket = 30;
}

char* GetCurruentSelectApName(void)
{
    return g_pAPList->ApInfo[g_sU8LastWifiSelectedScreenFocus].ApName;
}

static void RefreshWifiTimerTask(void)
{
	GUI_TIMER_Restart(g_RefreshWifiTimer);
    if (g_U8CanScanStatus == 0)
    {
        return;
    }
    if (g_WifiUartModule.GetUpdateApListStatus())
    {
        g_U8WifiScanStatus = 2;
        RepaintWifiSelectedScreen();
    }
    else
    {
        if (++g_U8WifiScanTicket >= 60)
        {
            g_U8WifiScanStatus = 1;
            g_U8WifiScanTicket = 0;
            g_WifiUartModule.ScanAP();
            DrawScanStatus(0);
        }
    }
}

static void CreateRefreshWifiTimer(void)
{	
	GUI_TIMER_CREATE(g_RefreshWifiTimer, RefreshWifiTimerTask, 10008, 0, 0);
    GUI_TIMER_SetPeriod(g_RefreshWifiTimer, (GUI_TIMER_TIME)(500 / (1000 / OS_CFG_TICK_RATE_HZ)));
    GUI_TIMER_Restart(g_RefreshWifiTimer);
}

#if (LCD_TYPE == LCD_28_TFT)
static void wifi_SSID_EachRow(uint8_t u8Row)
{
    GUI_RECT rect;
    char ApNameTmp[20] = "";
    char DotBuff[] = "...";
    uint8_t u8Tmp = 0;

    GUI_SetColor(GUI_WHITE);
   
    GUI_SetFont(FONT_18);
    
    rect.x0 = 15;
    rect.y0 = 28 + u8Row % MAX_PAGE_LINE * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
    rect.x1 = rect.x0 + PARALIST_TITLE_LEN+100;
    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
    
    u8Tmp = g_pAPList->ApInfo[u8Row].U8EncryptionType > 0 ? 1: 0;

    if (strlen(g_pAPList->ApInfo[u8Row].ApName) > MAX_LEN_WIFISSID)
    {
        memcpy(ApNameTmp, g_pAPList->ApInfo[u8Row].ApName, MAX_LEN_WIFISSID);
        strcat(ApNameTmp, DotBuff);
        GUI_DispStringInRect(ApNameTmp, &rect, GUI_TA_LEFT | GUI_TA_TOP);
    }
    else
        GUI_DispStringInRect(g_pAPList->ApInfo[u8Row].ApName, &rect, GUI_TA_LEFT | GUI_TA_TOP);

   
    rect.x0 = 15 + PARALIST_UINT_LEN_X0 +50;
    rect.y0 = 28 + u8Row % MAX_PAGE_LINE * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
    rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 - 1;
    rect.y1 = rect.y0 + LIST_HEIGHT - 1;

    const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
    if (strcmp(g_pAPList->ApInfo[u8Row].ApName, pWifiConfig->SSID) == 0 && g_WifiUartModule.GetConnectStatus())
        GUI_DispStringInRect(GetMultiLanguageString(WIFI_CONNECTED_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP);
    else
    {
        if (g_pAPList->ApInfo[u8Row].U8EncryptionType)
        {
            GUI_DispStringInRect(GetMultiLanguageString(ENCRYPT_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP);
        }
        else
        {
            GUI_DispStringInRect(GetMultiLanguageString(UNENCRYPT_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP);
        }
    }

    rect.x0 = 15 + PARALIST_VALUE_LEN_X0; //X:376
    rect.y0 = 28 + u8Row % MAX_PAGE_LINE * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
    rect.x1 = rect.x0 + PARALIST_VALUE_LEN_X1 - 1;
    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
    if (g_WifiSelectedScreenWInfo.U8CurFocusID == u8Row)
    {
        DrawBMP(g_U16FocusWifiSignalQualityBmpIdx[u8Tmp][g_pAPList->ApInfo[u8Row].U8SignalQuality - 1], 44 + PARALIST_VALUE_LEN_X0, 2 + u8Row % MAX_PAGE_LINE * EACHLIST_HEIGHT + 20 + STATEBAR_TITLE_HEIGHT);
    }
    else
    {
        DrawBMP(g_U16WifiSignalQualityBmpIdx[u8Tmp][g_pAPList->ApInfo[u8Row].U8SignalQuality - 1], 44 + PARALIST_VALUE_LEN_X0, 2 + u8Row % MAX_PAGE_LINE * EACHLIST_HEIGHT + 20 + STATEBAR_TITLE_HEIGHT);
    }
}
#else
static void wifi_SSID_EachRow(uint8_t u8Row)
{
    GUI_RECT rect;
    char ApNameTmp[20] = "";
    char DotBuff[] = "...";
    uint8_t u8Tmp = 0;

    GUI_SetColor(GUI_WHITE);
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
        
        rect.x0 = 74;
        rect.y0 = 3 + u8Row % 5 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
        rect.x1 = rect.x0 + 301;
        rect.y1 = rect.y0 + LIST_HEIGHT - 1;
    #else
        GUI_SetFont(FONT_24);

        rect.x0 = 44;
        rect.y0 = 2 + u8Row % 5 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
        rect.x1 = rect.x0 + PARALIST_TITLE_LEN;
        rect.y1 = rect.y0 + LIST_HEIGHT - 1;
    #endif

    u8Tmp = g_pAPList->ApInfo[u8Row].U8EncryptionType > 0 ? 1: 0;

    if (strlen(g_pAPList->ApInfo[u8Row].ApName) > MAX_LEN_WIFISSID)
    {
        memcpy(ApNameTmp, g_pAPList->ApInfo[u8Row].ApName, MAX_LEN_WIFISSID);
        strcat(ApNameTmp, DotBuff);
        GUI_DispStringInRect(ApNameTmp, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    }
    else
        GUI_DispStringInRect(g_pAPList->ApInfo[u8Row].ApName, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);

    #if (LCD_TYPE == LCD_5_TFT)
        rect.x0 = 74 + 302;
        rect.y0 = 3 + u8Row % 5 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
        rect.x1 = rect.x0 + 257 - 1;
        rect.y1 = rect.y0 + LIST_HEIGHT - 1;

        const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
        if (strcmp(g_pAPList->ApInfo[u8Row].ApName, pWifiConfig->SSID) == 0 && g_WifiUartModule.GetConnectStatus())
            GUI_DispStringInRectWrap(GetMultiLanguageString(WIFI_CONNECTED_IDX), &rect, GUI_TA_LEFT | GUI_TA_VCENTER, GUI_WRAPMODE_CHAR);
        else
        {
            if (g_pAPList->ApInfo[u8Row].U8EncryptionType)
            {
                GUI_DispStringInRectWrap(GetMultiLanguageString(ENCRYPT_IDX), &rect, GUI_TA_LEFT | GUI_TA_VCENTER, GUI_WRAPMODE_CHAR);
            }
            else
            {
                GUI_DispStringInRectWrap(GetMultiLanguageString(UNENCRYPT_IDX), &rect, GUI_TA_LEFT | GUI_TA_VCENTER, GUI_WRAPMODE_CHAR);            
            }
        }

        rect.x0 = 74 + 559; //X:376
        rect.y0 = 3 + u8Row % 5 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
        rect.x1 = rect.x0 + 156 - 1;
        rect.y1 = rect.y0 + LIST_HEIGHT - 1;
        
        if (g_WifiSelectedScreenWInfo.U8CurFocusID == u8Row)
        {
            DrawBMP(g_U16FocusWifiSignalQualityBmpIdx[u8Tmp][g_pAPList->ApInfo[u8Row].U8SignalQuality - 1], 74 + PARALIST_VALUE_LEN_X0, 3 + u8Row % 5 * EACHLIST_HEIGHT + 10 + STATEBAR_TITLE_HEIGHT);
        }
        else
        {
            DrawBMP(g_U16WifiSignalQualityBmpIdx[u8Tmp][g_pAPList->ApInfo[u8Row].U8SignalQuality - 1], 74 + PARALIST_VALUE_LEN_X0, 3 + u8Row % 5 * EACHLIST_HEIGHT + 10 + STATEBAR_TITLE_HEIGHT);
        }
    #else
        rect.x0 = 30 + PARALIST_UINT_LEN_X0;
        rect.y0 = 2 + u8Row % 5 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;//420
        rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 + 13;
        rect.y1 = rect.y0 + LIST_HEIGHT - 1;

        const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
        if (strcmp(g_pAPList->ApInfo[u8Row].ApName, pWifiConfig->SSID) == 0 && g_WifiUartModule.GetConnectStatus())
            GUI_DispStringInRect(GetMultiLanguageString(WIFI_CONNECTED_IDX), &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
        else
        {
            if (g_pAPList->ApInfo[u8Row].U8EncryptionType)
            {
                GUI_DispStringInRectWrap(GetMultiLanguageString(ENCRYPT_IDX), &rect, GUI_TA_LEFT | GUI_TA_VCENTER, GUI_WRAPMODE_CHAR);
            }
            else
            {
                GUI_DispStringInRectWrap(GetMultiLanguageString(UNENCRYPT_IDX), &rect, GUI_TA_LEFT | GUI_TA_VCENTER, GUI_WRAPMODE_CHAR);            
            }
        }

        rect.x0 = 44 + PARALIST_VALUE_LEN_X0; //X:421
        rect.y0 = 2 + u8Row % 5 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
        rect.x1 = rect.x0 + PARALIST_VALUE_LEN_X1 - 1;
        rect.y1 = rect.y0 + LIST_HEIGHT - 1;
        if (g_WifiSelectedScreenWInfo.U8CurFocusID == u8Row)
        {
            DrawBMP(g_U16FocusWifiSignalQualityBmpIdx[u8Tmp][g_pAPList->ApInfo[u8Row].U8SignalQuality - 1], 44 + PARALIST_VALUE_LEN_X0, 2 + u8Row % 5 * EACHLIST_HEIGHT + 10 + STATEBAR_TITLE_HEIGHT);
        }
        else
        {
            DrawBMP(g_U16WifiSignalQualityBmpIdx[u8Tmp][g_pAPList->ApInfo[u8Row].U8SignalQuality - 1], 44 + PARALIST_VALUE_LEN_X0, 2 + u8Row % 5 * EACHLIST_HEIGHT + 10 + STATEBAR_TITLE_HEIGHT);
        }
    #endif
}
#endif

#if (LCD_TYPE != LCD_28_TFT)
void WifiScreen_DrawExitIcon(U8 u8Focus)
{
    U16 sU16ExitBmpIdx[2] = {BMP_EXITICON1, BMP_EXITICON2};
    GUI_MEMDEV_Handle hMem = 0;

#if (LCD_TYPE == LCD_5_TFT)
    hMem = GUI_MEMDEV_Create(655 - 33, 408, 167, 66);
    GUI_MEMDEV_Select(hMem);
    
    GUI_SetColor(BACKCOLOR_DEFAULT);//���ñ����ɫ
    GUI_FillRect(655 - 33, 408, 655, 408 + 33 * 2 - 1); //����Բ��ɫ�������ڽ����л�ʱ����Բ����ɫ��ë��
#else
    hMem = GUI_MEMDEV_Create(393 - 22, 272, 103, 44);
    GUI_MEMDEV_Select(hMem);
    
    GUI_SetColor(BACKCOLOR_DEFAULT);//���ñ����ɫ
    GUI_FillRect(393 - 22, 272, 393 + 80, 272 + 22 * 2 - 1); //����Բ��ɫ�������ڽ����л�ʱ����Բ����ɫ��ë��
#endif    
    if (u8Focus)
        GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
    else
        GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
    
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_AA_FillCircle(655, 408 + 33, 33);
        GUI_FillRect(655, 408, 655 + 133, 408 + 33 * 2 - 1);
    #else
        GUI_AA_FillCircle(393, 272 + 22, 22);
        GUI_FillRect(393, 272, 393 + 80, 272 + 22 * 2 - 1);
    #endif
    
    GUI_SetColor(GUI_WHITE);
    GUI_DispStringInRect(GetMultiLanguageString(MASKTEST_EXIT_IDX), &g_MenuIconRect, GUI_TA_HCENTER | GUI_TA_VCENTER);

    #if (LCD_TYPE == LCD_5_TFT)
        DrawBMP(sU16ExitBmpIdx[u8Focus], 652, 356 + STATEBAR_TITLE_HEIGHT);
    #else
        DrawBMP(sU16ExitBmpIdx[u8Focus], 381, 232 + STATEBAR_TITLE_HEIGHT);
    #endif
    
    GUI_MEMDEV_Select(0);
#if (LCD_TYPE == LCD_5_TFT)
    GUI_MEMDEV_CopyToLCDAt(hMem, 655 - 33, 408);
#else
    GUI_MEMDEV_CopyToLCDAt(hMem, 393 - 22, 272);
#endif
    GUI_MEMDEV_Delete(hMem);
}
#endif
/*************************************************
Function: DrawScanStatus
Description: ����ɨ״̬
Input: bFlag 1:Ҫѡ�񴰿������˫���� 0:��Ҫѡ�񴰿������˫����
Output: void
Return: void
Others: 
*************************************************/
static void DrawScanStatus(U8 bFlag)
{
    if (g_U8WifiScanStatus == 0 || g_WindowDrv.GetTopWindow() != &g_WifiSelectedScreenWInfo)
    {
        return;
    }
    GUI_RECT rect;

#if (LCD_TYPE == LCD_28_TFT)
    rect.x0 = 10;
    rect.y0 = 272 - 60 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = 80;
    rect.y1 = 272 - 60 + 40 + STATEBAR_TITLE_HEIGHT;
#elif (LCD_TYPE == LCD_5_TFT)
    rect.x0 = 10;
    rect.y0 = 408 - 80 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = 250;
    rect.y1 = 408 - 15 + STATEBAR_TITLE_HEIGHT;        
#else
    rect.x0 = 10;
    rect.y0 = 272 - 50 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = 180;
    rect.y1 = 272 - 46 + 22 * 2 - 1 + STATEBAR_TITLE_HEIGHT;        
#endif    
    WM_SelectWindow(WM_HBKWIN);

    #if (LCD_TYPE == LCD_28_TFT)
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);    //���ñ���ɫ
    #else
        GUI_SetColor(BACKCOLOR_DEFAULT);    //���ñ���ɫ
    #endif
    GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
    GUI_SetColor(GUI_WHITE);
#if (LCD_TYPE == LCD_28_TFT)
    GUI_SetFont(FONT_18);
#elif (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_32);    
#else
    GUI_SetFont(FONT_24);   
#endif
    if (g_U8WifiScanStatus == 1)
    {
        GUI_DispStringInRectWrap(GetMultiLanguageString(SCANNING_IDX), &rect, GUI_TA_LEFT | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
    }
    else
    {
        GUI_DispStringInRectWrap(GetMultiLanguageString(SCAN_FINISH_IDX), &rect, GUI_TA_LEFT | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
    }   
}

void RepaintWifiSelectedScreen(void)
{
    g_WifiUartModule.SetApListStatus(0);
    WM_SelectWindow(WM_HBKWIN);
    GUI_SetTextMode(GUI_TM_TRANS);  //����??��3?�㨪??��
    if (g_WifiSelectedScreenWInfo.U8CurFocusID > g_pAPList->ApNum)
        g_WifiSelectedScreenWInfo.U8CurFocusID = g_pAPList->ApNum;
#if (LCD_TYPE == LCD_28_TFT)
    DrawNewWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID / MAX_PAGE_LINE,TRUE);
    DrawScanStatus(1);
#else
    DrawNewWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID);
    DrawScanStatus(1);
#endif
}

#if (LCD_TYPE == LCD_28_TFT)
static void DrawNewWifiSelectedScreen(uint8_t u8Page, uint8_t u8IsFirst)
{
	uint8_t i;
    static uint8_t Disp[4]={0,0,0,0};
    static uint8_t LastPage = 0;
    GUI_SetTextMode(GUI_TM_TRANS);  //���ñ���͸��
    
    GUI_SetFont(FONT_18);

    if (g_pAPList->ApNum == 0)
    {
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);//���ý��汳��ɫ-��ɫ(0, 0, 0)
        GUI_FillRect(0, STATEBAR_TITLE_HEIGHT - 5, SCREEN_WIDTH, MAINSCREEN_HEIGHT + STATEBAR_TITLE_HEIGHT);
        GUI_SetColor(GREEN);
        GUI_AA_FillCircle(Return_X,Return_Y + STATEBAR_TITLE_HEIGHT,20);
        DrawBMP(BMP_RETURNFOCU, Return_X-12, Return_Y-10 + STATEBAR_TITLE_HEIGHT);
        return;
    }
    if(u8Page > (g_pAPList->ApNum - 1) /MAX_PAGE_LINE)
    {
        u8Page = (g_pAPList->ApNum - 1) /MAX_PAGE_LINE;
    }
    if(u8IsFirst)
    {
        for(i = 0;i<4;i++)
        {
            Disp[i] = 0;
        }
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);//���ý��汳��ɫ-��ɫ(0, 0, 0)
        if(u8IsFirst == 1)
        {
            LastPage = u8Page;
            GUI_FillRect(0, STATEBAR_TITLE_HEIGHT-8, SCREEN_WIDTH, MAINSCREEN_HEIGHT + STATEBAR_TITLE_HEIGHT);
        }
        else
            GUI_FillRect(0, STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, Return_Y-25 + STATEBAR_TITLE_HEIGHT);
    }
    if(LastPage != u8Page)
    {
        u8IsFirst = 2;//��ҳ��ˢ�·���
        LastPage = u8Page;
    }
    //g_WifiSelectedScreenWInfo.U8CurFocusID����g_pAPList->ApNum���򽹵��ڲ˵�
    //�˴�����ѡ���е����±߿�
    if (g_pAPList->ApNum % MAX_PAGE_LINE == 0)
    {
        if(u8Page < g_pAPList->ApNum / MAX_PAGE_LINE + 1)
        for(i=0;i<3;i++)
        {
            if(u8IsFirst 
                || (i == g_WifiSelectedScreenWInfo.U8CurFocusID % MAX_PAGE_LINE && g_WifiSelectedScreenWInfo.U8CurFocusID < g_pAPList->ApNum) 
                || Disp[i] == 1)
            {
                if(i == g_WifiSelectedScreenWInfo.U8CurFocusID % MAX_PAGE_LINE && g_WifiSelectedScreenWInfo.U8CurFocusID < g_pAPList->ApNum)
                {
                    if(u8IsFirst == 1)
                        GUI_SetColor(GREEN);
                    else
                        DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                    Disp[i] = 1;
                }
                else
                {
                    if(u8IsFirst == 1)
                        GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                    else
                        DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);
                    Disp[i] = 0;
                }
                if(u8IsFirst == 1)
                    GUI_AA_FillRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,234,59+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED);
                wifi_SSID_EachRow(i+u8Page * MAX_PAGE_LINE);
            }
        }
    }
    else
    {
        if(u8Page == g_pAPList->ApNum / MAX_PAGE_LINE)
        {
            for(i=0;i<g_pAPList->ApNum % MAX_PAGE_LINE;i++)
            {
                if(u8IsFirst 
                || (i == g_WifiSelectedScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) 
                || Disp[i] == 1)
                {
                    if(i == g_WifiSelectedScreenWInfo.U8CurFocusID % MAX_PAGE_LINE)
                    {
                        if(u8IsFirst == 1)
                            GUI_SetColor(GREEN);
                        else
                            DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                        Disp[i] = 1;
                    }
                    else
                    {
                        if(u8IsFirst == 1)
                            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                        else
                            DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);
                        Disp[i] = 0;
                    }
                    if(u8IsFirst == 1)
                        GUI_AA_FillRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,234,59+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED);
                    wifi_SSID_EachRow(i+u8Page * MAX_PAGE_LINE);
                }
            }
        }
        else
        {
            for(i=0;i<3;i++)
            {
                if(u8IsFirst 
                || (i == g_WifiSelectedScreenWInfo.U8CurFocusID % MAX_PAGE_LINE && g_WifiSelectedScreenWInfo.U8CurFocusID < g_pAPList->ApNum) 
                || Disp[i] == 1)
                {
                    if(i == g_WifiSelectedScreenWInfo.U8CurFocusID % MAX_PAGE_LINE && g_WifiSelectedScreenWInfo.U8CurFocusID < g_pAPList->ApNum)
                    {
                        if(u8IsFirst == 1)
                            GUI_SetColor(GREEN);
                        else
                            DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                        Disp[i] = 1;
                    }
                    else
                    {
                        if(u8IsFirst == 1)
                            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                        else
                            DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);
                        Disp[i] = 0;
                    }
                    if(u8IsFirst == 1)
                        GUI_AA_FillRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,234,59+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED);
                    wifi_SSID_EachRow(i+u8Page * MAX_PAGE_LINE);
                }
            }
        }
    }
    
    if(u8IsFirst == 1|| (g_WifiSelectedScreenWInfo.U8CurFocusID == g_pAPList->ApNum) || Disp[3] == 1)
    {
        if(g_WifiSelectedScreenWInfo.U8CurFocusID == g_pAPList->ApNum)
        {
            GUI_SetColor(GREEN);
            Disp[3] = 1;
            GUI_AA_FillCircle(Return_X,Return_Y + STATEBAR_TITLE_HEIGHT,20);
            DrawBMP(BMP_RETURNFOCU, Return_X-12, Return_Y-10 + STATEBAR_TITLE_HEIGHT);
        }
        else
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            Disp[3] = 0;
            GUI_AA_FillCircle(Return_X,Return_Y + STATEBAR_TITLE_HEIGHT,20);
            DrawBMP(BMP_RETURN, Return_X-12, Return_Y-10 + STATEBAR_TITLE_HEIGHT);
        }
 
        DrawPageCircle(u8Page, (g_pAPList->ApNum - 1) / MAX_PAGE_LINE + 1);
    }
    if(u8IsFirst == 2)
    DrawPageCircle(u8Page, (g_pAPList->ApNum - 1) / MAX_PAGE_LINE + 1);
}
#else
static void DrawNewWifiSelectedScreen(uint8_t u8CurrentFocus)
{
	uint8_t i;
    uint8_t j;    

    GUI_SetTextMode(GUI_TM_TRANS);  //����??��3?�㨪??��
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
    #else
        GUI_SetFont(FONT_24);
    #endif
    
    if (g_pAPList->ApNum == 0)
    {
        DrawChildSettingScreenBackground();
        //��"����"����
//        GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
        WifiScreen_DrawExitIcon(1);

        return;
    }
    //���Ʊ������ƿ�
    DrawChildSettingScreenBackground();

    //g_WifiSelectedScreenWInfo.U8CurFocusID����g_pAPList->ApNum���򽹵��ڲ˵�
    //�˴�����ѡ���е����±߿�
    if (g_WifiSelectedScreenWInfo.U8CurFocusID != g_pAPList->ApNum)
    {        
        GUI_SetColor(CHILD_INTERFACE_LIST_BORDERCOLOR_SELECTED);
        for (j = 0; j < 2; j++)
        {
            GUI_FillRect(LIST_X_POS
                        , (g_WifiSelectedScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + EACHLIST_HEIGHT * j + STATEBAR_TITLE_HEIGHT
                        , SCREEN_WIDTH - LIST_X_POS
                        , (g_WifiSelectedScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + EACHLIST_HEIGHT * j + LIST_BORDER_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
        }
        GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
        GUI_FillRect(LIST_X_POS
                    , (g_WifiSelectedScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + STATEBAR_TITLE_HEIGHT
                    , SCREEN_WIDTH - LIST_X_POS
                    , (g_WifiSelectedScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
    }
    
    if (g_pAPList->ApNum % 5 > 0)
    {
        for (i = 0 + g_WifiSelectedScreenWInfo.U8CurFocusID / 5 * 5; i < g_pAPList->ApNum && i < (g_WifiSelectedScreenWInfo.U8CurFocusID / 5 + 1) * 5; i++)
            wifi_SSID_EachRow(i);
    }
    else //g_pAPList->ApNum ����5��10��15
    {
        if (g_WifiSelectedScreenWInfo.U8CurFocusID == g_pAPList->ApNum)
        {
            for (i = 0 + (g_WifiSelectedScreenWInfo.U8CurFocusID - 1) / 5 * 5; i < g_pAPList->ApNum && i < ((g_WifiSelectedScreenWInfo.U8CurFocusID - 1) / 5 + 1) * 5; i++)
                wifi_SSID_EachRow(i);
        }
        else
        {
            for (i = 0 + g_WifiSelectedScreenWInfo.U8CurFocusID / 5 * 5; i < g_pAPList->ApNum && i < (g_WifiSelectedScreenWInfo.U8CurFocusID / 5 + 1) * 5; i++)
                wifi_SSID_EachRow(i);
        }
    }
    
    //�˵�ͼ
    WifiScreen_DrawExitIcon(g_WifiSelectedScreenWInfo.U8CurFocusID == g_pAPList->ApNum);

    //���µ�ҳ��СԲ��
    //if (g_pAPList->ApNum >= 1)
    if (g_pAPList->ApNum > 0)
    {
        if (g_pAPList->ApNum % 5 > 0)
            DrawPageCircle(g_WifiSelectedScreenWInfo.U8CurFocusID / 5, g_pAPList->ApNum / 5 + 1);
        else //g_pAPList->ApNum ����5��10��15
        {
            if (g_WifiSelectedScreenWInfo.U8CurFocusID == g_pAPList->ApNum)
                DrawPageCircle(g_WifiSelectedScreenWInfo.U8CurFocusID / 5 - 1, g_pAPList->ApNum / 5);
            else
                DrawPageCircle(g_WifiSelectedScreenWInfo.U8CurFocusID / 5, g_pAPList->ApNum / 5);
        }
    }
}
#endif

static void ChangeFocusWifiSelectedScreen(U8 UpFlag)
{
    if (g_pAPList->ApNum == 0)
        return;
    
    WM_SelectWindow(WM_HBKWIN);
    if (UpFlag)
    {
        g_WifiSelectedScreenWInfo.U8CurFocusID = (g_WifiSelectedScreenWInfo.U8CurFocusID + (g_pAPList->ApNum + 1) - 1) 
                                          % (g_pAPList->ApNum + 1);
        ChangeFocusWifiSetting_Left();
    }
    else
    {
        g_WifiSelectedScreenWInfo.U8CurFocusID = (g_WifiSelectedScreenWInfo.U8CurFocusID + 1) % (g_pAPList->ApNum + 1);
        ChangeFocusWifiSetting_Right();
    }    
    DrawScanStatus(1);
#if (LCD_TYPE != LCD_28_TFT)
    g_WifiSelectedScreenWInfo.U8OldFocusID = g_WifiSelectedScreenWInfo.U8CurFocusID; 
#endif
}

#if (LCD_TYPE == LCD_28_TFT)
static void ChangeFocusWifiSetting_Left(void)
{
    if (g_WifiSelectedScreenWInfo.U8CurFocusID == g_pAPList->ApNum && (g_pAPList->ApNum)%MAX_PAGE_LINE != 0)
        DrawNewWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, 2);
    else
        DrawNewWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, FALSE);
    
}
#else
static void ChangeFocusWifiSetting_Left(void)
{
    if (g_WifiSelectedScreenWInfo.U8CurFocusID == (g_pAPList->ApNum + 1) - 1)
        DrawNewWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID);
        //DrawWifiSelectedScreen((g_WifiSelectedScreenWInfo.U8CurFocusID - 1) / 5, FALSE);
    else if (g_WifiSelectedScreenWInfo.U8CurFocusID % 5 == 4 && (g_WifiSelectedScreenWInfo.U8CurFocusID / 5 < (g_pAPList->ApNum - 1) / 5))    
        //DrawWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID / 5, FALSE);   //��-��?�̨�1��3,2��??????��3��?��?o����???
        DrawNewWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID);   //������1ҳ,��ѡ���ҳ�����һ��
    else
        ChangeFocusNoPageTurning_WifiSelected(g_WifiSelectedScreenWInfo.U8CurFocusID, g_WifiSelectedScreenWInfo.U8OldFocusID);
}
#endif

#if (LCD_TYPE == LCD_28_TFT)
static void ChangeFocusWifiSetting_Right(void)
{
     if (g_WifiSelectedScreenWInfo.U8CurFocusID == (g_pAPList->ApNum-1)/MAX_PAGE_LINE * MAX_PAGE_LINE && (g_pAPList->ApNum)%MAX_PAGE_LINE != 0)
        DrawNewWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, 2); //��ҳ��ˢ��
     else
        DrawNewWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, FALSE);
    
}
#else
static void ChangeFocusWifiSetting_Right(void)
{
    if (g_WifiSelectedScreenWInfo.U8CurFocusID % 5 == 0 && g_WifiSelectedScreenWInfo.U8CurFocusID < ((g_pAPList->ApNum + 1) - 1))
        //DrawWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID / 5, TRUE); //��-��?�̨�1��3��?2��???D??��3��?�̨�1DD
        DrawNewWifiSelectedScreen(g_WifiSelectedScreenWInfo.U8CurFocusID);
    else
        ChangeFocusNoPageTurning_WifiSelected(g_WifiSelectedScreenWInfo.U8CurFocusID, g_WifiSelectedScreenWInfo.U8OldFocusID);
}
#endif

#if (LCD_TYPE != LCD_28_TFT)
static void ChangeFocusNoPageTurning_WifiSelected(uint8_t u8CurrentFocus, uint8_t u8OldFocus)
{
    uint8_t i, j;
    uint8_t sU8Focus[2] = {0};

    sU8Focus[0] = u8CurrentFocus;
    sU8Focus[1] = u8OldFocus;

    for (i = 0; i < 2; i++)
    {
        if (i == 0)
        {
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);//���ñ����ɫ
            if (sU8Focus[i] == g_pAPList->ApNum)
            {
                WifiScreen_DrawExitIcon(1);
            }
            else
            {
                GUI_FillRect(LIST_X_POS
                            , (sU8Focus[i] % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + STATEBAR_TITLE_HEIGHT
                            , SCREEN_WIDTH - LIST_X_POS
                            , (sU8Focus[i] % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);

                GUI_SetColor(CHILD_INTERFACE_LIST_BORDERCOLOR_SELECTED);
                for (j = 0; j < 2; j++)
                    GUI_FillRect(LIST_X_POS
                                , (sU8Focus[i] % 5 + j) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                , SCREEN_WIDTH - LIST_X_POS
                                , (sU8Focus[i] % 5 + j) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);

                wifi_SSID_EachRow(sU8Focus[i]);
            }
        }
        else
        {
            GUI_SetColor(CHILD_INTERFACE_LIST_BORDERCOLOR_DEFAULT);;    //���ñ����ɫ
            if (sU8Focus[i] == g_pAPList->ApNum)
            {
                WifiScreen_DrawExitIcon(0);
            }
            else
            {
                //����������ת��Focus +1
                if (sU8Focus[0] > sU8Focus[1])
                {
                    GUI_FillRect(LIST_X_POS
                                , (sU8Focus[i] % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                , SCREEN_WIDTH - LIST_X_POS
                                , (sU8Focus[i] % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
                    if ((sU8Focus[i] == (g_pAPList->ApNum + 1) - 2) || (sU8Focus[i] == 0 && sU8Focus[i - 1] == (g_pAPList->ApNum + 1) - 1))
                        GUI_FillRect(LIST_X_POS
                                    , (sU8Focus[i] % 5 + 1) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                    , SCREEN_WIDTH - LIST_X_POS
                                    , (sU8Focus[i] % 5 + 1) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
                }
                else
                    GUI_FillRect(LIST_X_POS
                                , (sU8Focus[i] % 5 + 1) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                , SCREEN_WIDTH - LIST_X_POS
                                , (sU8Focus[i] % 5 + 1) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
                
                GUI_SetColor(BACKCOLOR_DEFAULT);
                GUI_FillRect(LIST_X_POS
                            , (sU8Focus[i] % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + STATEBAR_TITLE_HEIGHT
                            , SCREEN_WIDTH - LIST_X_POS
                            , (sU8Focus[i] % 5 + 1) * EACHLIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);

                wifi_SSID_EachRow(sU8Focus[i]);
            }
        }
    }
}
#endif

static void OnEnterClick_WifiSelectedScreen(void)
{
    if (g_WifiSelectedScreenWInfo.U8CurFocusID == g_pAPList->ApNum)
    {
        ShowWifiSelectedScreen(0);
    }
    else
    {
        if (GetBldcmStatus() != MOTOR_STOP_STATE)
        {
            return;
        }
        g_sU8LastWifiSelectedScreenFocus = g_WifiSelectedScreenWInfo.U8CurFocusID; 
        ShowWifiSSIDSettingScreen(1);
    }
}

void ShowWifiSelectedScreen(uint8_t Flag)
{
    g_WifiSelectedScreenWInfo.U8CurFocusID = 0;
    g_WifiSelectedScreenWInfo.U8OldFocusID = 0;
	if (Flag)
	{
        g_U8WifiScanStatus = 0;
        g_pAPList = g_WifiUartModule.GetAPList();        
        UpdateTopStateBarMenuString(GetMultiLanguageString(WLANTITLE_IDX));
        CreateRefreshWifiTimer();
        ShowProcess();
        g_U8WifiScanTicket = 60;    //��ʾ������ڶ�ʱ������������ˢ��
        g_WindowDrv.PushWindow(&g_WifiSelectedScreenWInfo);
	}
	else
	{
		UpdateTopStateBarMenuString(GetMultiLanguageString(SYSSETTING_IDX));
        //GUI_TIMER_Delete(g_RefreshWifiTimer);
        g_WindowDrv.PopWindow(&g_WifiSelectedScreenWInfo);
	}
    g_WifiSelectedScreenWInfo.U8CurFocusID = 0;
#if (LCD_TYPE != LCD_28_TFT)
    g_WifiSelectedScreenWInfo.U8OldFocusID = 0;
#endif    
}

