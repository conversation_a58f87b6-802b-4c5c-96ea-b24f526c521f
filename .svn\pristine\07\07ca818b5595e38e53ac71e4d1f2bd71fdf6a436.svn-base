#ifndef __BSP_MX25L256_H__
#define __BSP_MX25L256_H__

#include "stm32f4xx_hal.h"
#include "main.h"
#define FLASH_PAGE_SIZE                      256         //0x100
#define FLASH_SUBSECTOR_SIZE                 4096        //0x1000
#define FLASH_SECTOR_SIZE                    65536       //0x10000

#if (SPI_TYPE == 0) 
#define FLASH_SPI_HANDLE                         hspi2
#define FLASH_SPI_INSTANCE                       SPI2
#else
#define FLASH_SPI_HANDLE                         hspi1
#define FLASH_SPI_INSTANCE                       SPI1
#endif

#define  MX25L256_ID                         0xC22019

//Flash CS片选低电平有效      
#if (SPI_TYPE == 0) 
#define SPI_FLASH_CS_PORT                    GPIOB
#define SPI_FLASH_CS_PIN                     GPIO_PIN_12
#else
#define SPI_FLASH_CS_PORT                    GPIOA
#define SPI_FLASH_CS_PIN                     GPIO_PIN_15
#endif
#define SPI_FLASH_CS_LOW()                   do{HAL_GPIO_WritePin(SPI_FLASH_CS_PORT, SPI_FLASH_CS_PIN, GPIO_PIN_RESET);}while(0)
#define SPI_FLASH_CS_HIGH()                  do{HAL_GPIO_WritePin(SPI_FLASH_CS_PORT, SPI_FLASH_CS_PIN, GPIO_PIN_SET);}while(0) 

#define Dummy_Byte                           0xFF
#define WIP_Flag                             0x01 //Write In Progress (WIP) flag

/* Reset Operations */
#define RESET_ENABLE_CMD                     0x66
#define RESET_MEMORY_CMD                     0x99

/* Identification Operations */
#define READ_ID_CMD                          0x9F   //spi 模式读取 ID 指令
#define MULTIPLE_IO_READ_ID_CMD              0xAF   //qpi 模式读取 ID 指令
#define READ_SERIAL_FLASH_DISCO_PARAM_CMD    0x5A

/* Read Operations */
#define READ_CMD                             0x03
#define READ_4_BYTE_ADDR_CMD                 0x13

#define FAST_READ_CMD                        0x0B
#define FAST_READ_DTR_CMD                    0x0D
#define FAST_READ_4_BYTE_ADDR_CMD            0x0C

#define DUAL_OUT_FAST_READ_CMD               0x3B
#define DUAL_OUT_FAST_READ_4_BYTE_ADDR_CMD   0x3C

#define DUAL_INOUT_FAST_READ_CMD             0xBB
#define DUAL_INOUT_FAST_READ_DTR_CMD         0xBD
#define DUAL_INOUT_FAST_READ_4_BYTE_ADDR_CMD 0xBC

#define QUAD_OUT_FAST_READ_CMD               0x6B
#define QUAD_OUT_FAST_READ_4_BYTE_ADDR_CMD   0x6C

#define QUAD_INOUT_FAST_READ_CMD             0xEB
#define QUAD_INOUT_FAST_READ_DTR_CMD         0xED  
#define QPI_READ_4_BYTE_ADDR_CMD             0xEC

/* Write Operations */
#define WRITE_ENABLE_CMD                     0x06
#define WRITE_DISABLE_CMD                    0x04

/* Register Operations */
#define READ_STATUS_REG_CMD                  0x05
#define READ_CFG_REG_CMD                     0x15   
#define WRITE_STATUS_CFG_REG_CMD             0x01

#define READ_LOCK_REG_CMD                    0x2D
#define WRITE_LOCK_REG_CMD                   0x2C

#define READ_EXT_ADDR_REG_CMD                0xC8
#define WRITE_EXT_ADDR_REG_CMD               0xC5

/* Program Operations */
#define PAGE_PROG_CMD                        0x02   //  MX25L25635FMI
#define QPI_PAGE_PROG_4_BYTE_ADDR_CMD        0x12   //  MX25L25635FMI、 MX25L51245G 芯片使用

#define QUAD_IN_FAST_PROG_CMD                0x38
#define EXT_QUAD_IN_FAST_PROG_CMD            0x38 
#define QUAD_IN_FAST_PROG_4_BYTE_ADDR_CMD    0x3E

/* Erase Operations */
#define SUBSECTOR_ERASE_CMD                  0x20
#define SUBSECTOR_ERASE_4_BYTE_ADDR_CMD      0x21
   
#define SECTOR_ERASE_CMD                     0xD8
#define SECTOR_ERASE_4_BYTE_ADDR_CMD         0xDC

#define BULK_ERASE_CMD                       0xC7   // 擦除整片芯片

#define PROG_ERASE_RESUME_CMD                0x30
#define PROG_ERASE_SUSPEND_CMD               0xB0

/* 4-byte Address Mode Operations */
#define ENTER_4_BYTE_ADDR_MODE_CMD           0xB7
#define EXIT_4_BYTE_ADDR_MODE_CMD            0xE9

/* Quad Operations */
#define ENTER_QUAD_CMD                       0x35
#define EXIT_QUAD_CMD                        0xF5

#define W25X_POWERDOWN_CMD                   0xB9
#define W25X_RELEASEPOWERDOWN_CMD            0xAB

#pragma pack(push,1)
typedef struct
{
    char version[20];//资源版本号
    unsigned int bmpStartAdd;
    unsigned int languageStartAdd;//utf8编码
    unsigned int fontStartAdd[10];//字体
    char  reserve[44];//保留
} RESOURCE_HEAD;
#pragma pack(pop)

char* GetResourceVersion(void);

extern RESOURCE_HEAD gResourceHead;

//#define SPI_FLASH_MACHINE_INFO_ADDR             (0x00000000)
//#define SPI_FLASH_PARAMETER_CONFIG_ADDR1        (0x00001000)
//#define SPI_FLASH_PARAMETER_CONFIG_ADDR2        (0x00002000)

//#define SPI_FLASH_TIT_CONFIG_ADDR1              (0x00003000)
//#define SPI_FLASH_TIT_CONFIG_ADDR2              (0x00004000)

//#define SPI_FLASH_MACHINE_CONFIG_ADDR1          (0x00005000)
//#define SPI_FLASH_MACHINE_CONFIG_ADDR2          (0x00006000)

//#define SPI_FLASH_RUNTIME_DATA_ADDR1            (0x00007000)
//#define SPI_FLASH_RUNTIME_DATA_ADDR2            (0x00008000)

//#define  EDF_HEAD_FLASH_ADDRR                   0x10000     //EDF信息头的起始地址
//#define  EDF_HEAD_LASH_LEN                      0x1000     //EDF信息头的长度
//#define  EDF_HEAD_FLSH_END_ADDR                 (EDF_HEAD_FLASH_ADDRR + EDF_HEAD_LASH_LEN)  //EDF信息头的结束地址 0x11000

//#define  EDF_DTAT_START_LASH_ADDR               EDF_HEAD_FLSH_END_ADDR      //EDF数据的起始地址 0x11000
//#define  EDF_ONEC_DTAT_LASH_MAX_LEN             64                          //EDF数据单次数据的最大长度
//#define  EDF_DTAT_LASH_LEN                      0x6000                      //EDF数据存储的长度
//#define  EDF_DATA_LASH_END_ADDR                 (EDF_DTAT_START_LASH_ADDR + EDF_DTAT_LASH_LEN)//EDF数据存储结束地址 0x17000

//#define EDF_BACK_DATA_STATRT_ADDR                   EDF_DATA_LASH_END_ADDR         //0x17000
//#define EDF_BACK_DATA_LEN                       0x120000 
//#define EDF_BACK_DATA_END_ADDR                  (EDF_BACK_DATA_STATRT_ADDR + EDF_BACK_DATA_LEN)    //0x137000

//#define SPI_FLASH_WIFI_DATA_INFO_ADDR           0x137000
////每组数据最大存储96K，估算时间大概可存储30分钟，则需要0x18000字节空间，最大可存储3组数据，故3块数据分别对应地址0x138000,0x149000,0x161000,结束地址0x179000
//#define SPI_FLASH_WIFI_PKG_SAVE_ADDR            0x138000     

//#define MX2_RESOURCE_ADDR                       (0x01800000)//(0x00200000 + 4)
//#define MX25_BMP_ADDR                           gResourceHead.bmpStartAdd + MX2_RESOURCE_ADDR
//#define MX25_LANGUAGE_LIB_ADDR                  gResourceHead.languageStartAdd + MX2_RESOURCE_ADDR

/* 等待超时时间 */
#define SPIT_FLAG_TIMEOUT                       ((uint32_t)0x1000)
#define SPIT_LONG_TIMEOUT                       ((uint32_t)(10 * SPIT_FLAG_TIMEOUT))

void SPI_FLASH_Init(void);
void SPI_FLASH_SectorErase(uint32_t SectorAddr);
void SPI_FLASH_BulkErase(void);
void SPI_FLASH_PageWrite(uint8_t *pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite);
void SPI_FLASH_BufferWrite(uint8_t *pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite);
void SPI_FLASH_BufferRead(uint8_t *pBuffer, uint32_t ReadAddr, uint16_t NumByteToRead);
uint32_t SPI_FLASH_ReadID(void);
uint32_t SPI_FLASH_ReadDeviceID(void);
void SPI_FLASH_StartReadSequence(uint32_t ReadAddr);
void SPI_Flash_PowerDown(void);
void SPI_Flash_WAKEUP(void);
uint8_t SPI_FLASH_ReadByte(void);
uint8_t SPI_FLASH_SendByte(uint8_t byte);
uint16_t SPI_FLASH_SendHalfWord(uint16_t HalfWord);
void SPI_FLASH_WriteEnable(void);
void SPI_FLASH_WriteDisable(void);
void SPI_FLASH_WaitForWriteEnd(void);
void SPI2_SetSpeed(uint8_t SPI_BaudRatePrescaler);
void SPI1_SetSpeed(uint8_t SPI_BaudRatePrescaler);
void SPI_Flash_Enter4BMode(void);

#define MACHINE_INFO_ADDR              (0x00000000)
#define MX2_RESOURCE_ADDR              (0x01800000)//(0x00200000 + 4)

extern uint32_t g_U32FlashID;

#endif







