#include "SIM7600Module.h"
#include "GlobalVariable.h"
#include "ConfigSave.h"
#include "CalcDateTime.h"
#include <string.h>
#include "StaterBar.h"

extern UART_HandleTypeDef huart7;
extern UART_HandleTypeDef huart3;

#define BUF_SIZE    1500
U8 Data[BUF_SIZE];


OS_TCB   AppTaskSimUartTCB;
CPU_STK  AppTaskSimUartStk[APP_CFG_TASK_SIMUART_STK_SIZE];

U8 g_U8HaveSendPowerOnOff = 0; //1：已发送开机 2:已发送关机包

const char* g_SIMCmd[] = {"NETOPEN", "NETCLOSE", "CIPOPEN", "CIPCLOSE"};

#define ARGC_LIMIT  5

#define AT_RX_TIMEOUT	200	/* ms */
#define AT_RX_BUF_SIZE	512	/* bytes */

//AT+CGPS=1,1\r\n 打开GPS，单机模式
//AT+CGPSINFO\r\n 查询CGPS信息

static uint8_t cmdbuf[AT_RX_BUF_SIZE];

#define QUERY_CMD		0x01 	/* 查询命令 */
#define EXECUTE_CMD		0x02	/* 执行命令 */
#define SET_CMD			0x03	/* 设置命令 */

typedef struct {
    char *cmd;	/* AT指令 */
    int U8CmdType;
    int (*deal_func)(int opt, int argc, char *argv[]);
}at_cmd_t;

int deal_uart_func(int cmdtype, int argc, char *argv[])
{
    return g_Sim7600UartModule.DealUartPkg(cmdtype, argc, argv);
}

at_cmd_t at_table[] = {
    {"ATE", SET_ECHO, deal_uart_func},      //0
    {"NETOPEN",  NET_OPEN, deal_uart_func},
    {"NETCLOSE", NET_CLOSE,  deal_uart_func},
    {"CIPOPEN", IP_OPEN, deal_uart_func},
    {"CIPCLOSE", IP_STOP, deal_uart_func},
    {"OK",  CMD_OK, deal_uart_func},        //5
    {"ERROR", CMD_ERROR,  deal_uart_func},
    {">", READY_SEND_DATA, deal_uart_func},
    {"RECV FROM", READY_RCV_DATALEN, deal_uart_func}, //RECV FROM:***************:39723 +IPD4
    {"+IPD", READY_RCV_DATA, deal_uart_func},
    {"SMS DONE", SMS_DONE, deal_uart_func}, //10
    {"PB DONE", PB_DONE, deal_uart_func},
    {"+CIPSEND", SEND_DATA, deal_uart_func},
    {"+CPIN", CPIN, deal_uart_func},
    {"+CSQ", QUERY_SIGNAL_QUALITY, deal_uart_func},    
    {"+IPCLOSE", IP_CLOSE, deal_uart_func},  
};

#define AT_TABLE_SIZE	(sizeof(at_table) / sizeof(at_cmd_t))

/*
 * @brief 字符串拆分解析处理
 * @return 检测归类的参数个数
 **/
int string_split(char *strp, uint32_t strsize, char ch, char *argv[], uint32_t argcM)
{
    int ch_index = 0;
    int argc_index = 0;
    uint8_t splitflag = 0;

    if ((!strsize) || (!argcM))
    {
        return 0;
    }

    argv[argc_index++] = &strp[ch_index];
    for (ch_index = 0; ch_index < strsize; ch_index++)
    {
        if (strp[ch_index] == ch)
        {
            strp[ch_index] = '\0';
#if 0	/* 处理连续出现ch情况 */
            if (1 == splitflag)
            {
                argv[argc_index++] = &strp[ch_index];
            }
#endif
            splitflag = 1;
        }
        else if (splitflag == 1)
        {
            splitflag = 0;
            argv[argc_index++] = &strp[ch_index];
            if (argc_index >= argcM)
            {
                break;
            }
        }
        else
        {
            splitflag = 0;
        }
    }

    return argc_index;
}

#define respond_error()	printf("ERROR\r\n")
#define respond_ok()	printf("OK\r\n");

int CSIM7600Module::at_cmd_parse(uint8_t *pdata, uint16_t size, U8 RcvType)
{
    int ret  = -1;
    char *ptr = NULL;
    int argc = ARGC_LIMIT;
    uint16_t offset = 0;
    int index = 0;
    char *argv[ARGC_LIMIT] = { (char *)0 };
    
    if (RcvType == 1)       //网络数据
    {
        for (index = 0; index < size; index++)
        {
            AnalyseTcpData(pdata[index]);
        }
        return 0;
    }    

    for (index = 0; index < AT_TABLE_SIZE; index++)
    {
        ptr = (char*)strstr((const char *)pdata, at_table[index].cmd);
        if (ptr != NULL)
        {
            ptr += strlen(at_table[index].cmd);
            offset = ptr - (char *)pdata;
            break;
        }
    }
    if (index < AT_TABLE_SIZE)
    {
//        printf("index=%d cmdtype=%d\n", index, at_table[index].U8CmdType);
        /* 解析查询命令 */
        if ((ptr[0] == '?') && (ptr[1] == '\r') && (ptr[2] == '\n'))
        {
            argc = 0;
        }
        else if ((ptr[0] == '\r') && (ptr[1] == '\n'))
        { /* 解析执行命令 */
            argc = 1;
            argv[0] = at_table[index].cmd;
        }
        else if (ptr[0] == ':')
        { /* 解析设置命令 */
            ptr += 1;
            argc = string_split((char*)ptr, size - offset, ',', argv, argc);
        }
        else
        {
            argc = 0;
        }
        if (at_table[index].U8CmdType == READY_SEND_DATA)
        {
            at_table[index].U8CmdType = READY_SEND_DATA;
        }
        else if (at_table[index].U8CmdType == READY_RCV_DATA)
        {
            m_U16RcvDataLen = atoi(ptr);
        }
        ret = at_table[index].deal_func(at_table[index].U8CmdType, argc, argv);
    }

//    if (ret == -1)
//    {
//        printf("Package Error\n");
//    }

    return ret;
}



CSIM7600Module::CSIM7600Module()
{    
    memset((void*)m_U8RcvBuffer, 0, sizeof(m_U8RcvBuffer));
    m_U8ModuleInitFinish = 0;
    m_U16RcvDataLen = 0;
    
    m_pConfigData = NULL;
    m_pCurrWifiCmdPkg = NULL;
    m_NetDataSendBuf.InIdx = 0;
    m_NetDataSendBuf.BaseIdx = 0;
    m_NetDataHead.Head[0] = NET_PACKAGE_HEAD1;
    m_NetDataHead.Head[1] = NET_PACKAGE_HEAD2;
    m_U8SendIdx = 0;
    m_U16OverTime = 0;
    m_U16HeartTicket = 0;
    m_U8ServerConnectStatus = DISCONNECTED; 
    m_U8NeedDataSaveFlag = 0;
    
    m_WifiDataPkg.U16CmdLen = 0; 
    m_WifiDataPkg.U16SDataLen = 0;
    m_WifiDataPkg.RspWaitTime = 1000;
    m_WifiDataPkg.U8SendStep = NET_SEND_DATA_NONE;
    
    m_U16AtRspLen = 0;
    m_U8SignalQuality = 0;
    
    ConfigBaseInfo();
}

CSIM7600Module::~CSIM7600Module()
{
}

U16 RcvCmdList[16];
U8 RcvCmdIdx = 0;

int CSIM7600Module::DealUartPkg(int cmdtype, int argc, char *argv[])
{
    RcvCmdList[RcvCmdIdx] = cmdtype;
    RcvCmdIdx = (RcvCmdIdx + 1) & 0x0f;
//    printf("deal_uart_func 0x%02x\n", cmdtype);
    if (m_pCurrWifiCmdPkg)
    {
        if (m_pCurrWifiCmdPkg->CmdType == cmdtype)
        {
            m_pCurrWifiCmdPkg = NULL;
        }
    }
    else if (m_WifiDataPkg.U8SendStep == NET_WAIT_SEND1)
    {
        if (cmdtype == READY_SEND_DATA)
        {
            m_WifiDataPkg.U8SendStep = NET_SEND_DATA;
        }
    }
    
    switch (cmdtype)
    {
    case SMS_DONE:
        m_U8ModuleInitFinish = 1;
        break;
    case SEND_DATA:
        if (m_WifiDataPkg.U8SendStep == NET_WAIT_SEND2)
        {
            m_WifiDataPkg.U8SendStep = NET_SEND_DATA_NONE;//NET_WAIT_RSP;//NET_SEND_DATA_NONE;
        }
        break;
    case NET_OPEN:
        break;
    case IP_OPEN:
        UpdateServerConnectStatus(atoi(argv[0]) == 0 && atoi(argv[1]) == 0);
        break;
    case IP_CLOSE:
        UpdateServerConnectStatus(0);
        break;
    case QUERY_SIGNAL_QUALITY:
        m_U8SignalQuality = atoi(argv[0]);
        break;
    case PB_DONE:        
        break;
    case READY_RCV_DATALEN:     //接收数据长度，准备接收数据        
        break;
    case READY_RCV_DATA:
        break;
    default:
        break;
    }
    return 0;    
}

void CSIM7600Module::ConfigBaseInfo()
{
    SetEcho(0);
    GetSignalQuality();
    EnableNet(1);
        
    //ConnectServer("***************", 42232);
    ConnectServer("**************", 8282);
}

void CSIM7600Module::GetSignalQuality()
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+CSQ\r\n");        
    WifiCmdPkg.CmdType = QUERY_SIGNAL_QUALITY;
    WifiCmdPkg.CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);
}

void CSIM7600Module::EnableNet(U8 Flag)
{
    WIFICMDPKG WifiCmdPkg;
    if (Flag)
    {
        strcpy((char*)WifiCmdPkg.Pkg, "AT+NETOPEN\r\n");        
    }
    else
    {
        strcpy((char*)WifiCmdPkg.Pkg, "AT+NETCLOSE\r\n");        
    }
    WifiCmdPkg.CmdType = NET_OPEN;
    WifiCmdPkg.CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);
}

void CSIM7600Module::ConnectServer(char* IP, U32 Port)
{
//AT+CIPOPEN=<link_num>,”TCP”,<serverIP>,<serverPort>[,<localPort>]    
    WIFICMDPKG WifiCmdPkg;
//    sprintf((char*)WifiCmdPkg.Pkg, "AT+CIPOPEN=0,\"TCP\",\"%s\",%d\r\n", IP, Port);      
//    WifiCmdPkg.CmdType = IP_OPEN;
//    WifiCmdPkg.CmdLen = strlen((char*)WifiCmdPkg.Pkg);
//    WifiCmdPkg.RspWaitTime = 3000;
//    PushCmdPkg(&WifiCmdPkg);

    for (U8 i = 0; i < 10; i++)
    {
    sprintf((char*)WifiCmdPkg.Pkg, "AT+CIPOPEN=%d,\"TCP\",\"%s\",%d\r\n", i, IP, Port);      
    WifiCmdPkg.CmdType = IP_OPEN;
    WifiCmdPkg.CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.RspWaitTime = 3000;
    PushCmdPkg(&WifiCmdPkg);
    }
    
}

void CSIM7600Module::IPStop()
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+CIPCLOSE=0\r\n");    
    WifiCmdPkg.CmdType = IP_STOP;
    WifiCmdPkg.CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);    
}

void CSIM7600Module::SetEcho(U8 Flag)     //设置回显
{
    WIFICMDPKG WifiCmdPkg;
    sprintf((char*)WifiCmdPkg.Pkg, "ATE%d\r\n", Flag);    
    WifiCmdPkg.CmdType = SET_ECHO;
    WifiCmdPkg.CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);    
}

void CSIM7600Module::SendNetData(U8* pData, U16 DataLen)
{
    U16 U16DataIdx = 0;
    //AT+CIPSEND=<link_num>,<length>
    if (m_WifiDataPkg.U8SendStep != NET_SEND_DATA_NONE) //如果之前的未发送完，不再发送新的数据包
    {
        return;
    }
    if (DataLen >= MAX_SEND_DATA_LEN || DataLen == 0)
    {
        assert_param("Wifi Send Data Len too long or too short.");
        return;
    }
    sprintf((char*)m_WifiDataPkg.SendBuf, "AT+CIPSEND=0,%d\r\n", DataLen);    
    U16DataIdx = strlen((char*)m_WifiDataPkg.SendBuf);
         
    memcpy((char*)m_WifiDataPkg.SendBuf + U16DataIdx, pData, DataLen);  
    m_WifiDataPkg.U16CmdLen = U16DataIdx;    
    m_WifiDataPkg.U16SDataLen = DataLen;
    m_WifiDataPkg.U8SendStep = NET_START_SEND_DATA;
}

void CSIM7600Module::Init(CConfigSave* pConfig)
{
    const MACHINECONFIG* pMachineConfig = pConfig->GetMachineConfig();

    m_pConfigData = pConfig;
    
    m_DeviceInfo.Model = pMachineConfig->DType;
    m_DeviceInfo.ProNum = pMachineConfig->ProNum;
    m_DeviceInfo.ProduceYear = pMachineConfig->Year;
    m_DeviceInfo.ProduceMon = pMachineConfig->Month;
    m_DeviceInfo.ProductTailCode = pMachineConfig->TailCode;
    m_DeviceInfo.AppVersion = g_U32SoftVersion;
    m_DeviceInfo.FontLibVersion = g_U32SoftVersion;
    m_DeviceInfo.PicLibVersion = g_U32SoftVersion;
    m_DeviceInfo.Customized = 0;
    
    m_RealDataHead.Model = m_DeviceInfo.Model;
    m_RealDataHead.ProNum = m_DeviceInfo.ProNum;
    m_RealDataHead.ProduceYear = m_DeviceInfo.ProduceYear;
    m_RealDataHead.ProduceMon = m_DeviceInfo.ProduceMon;
    m_RealDataHead.ProductTailCode = m_DeviceInfo.ProductTailCode;    
    
    m_NetPowerOnOffInfo.Model = m_DeviceInfo.Model;
    m_NetPowerOnOffInfo.ProNum = m_DeviceInfo.ProNum;
    m_NetPowerOnOffInfo.ProduceYear = m_DeviceInfo.ProduceYear;
    m_NetPowerOnOffInfo.ProduceMon = m_DeviceInfo.ProduceMon;
    m_NetPowerOnOffInfo.ProductTailCode = m_DeviceInfo.ProductTailCode;    
    
    m_WifiDataManager.Init();       
}

void CSIM7600Module::AnalyseTcpDatas()
{
    //+CIPEVENT：SOCKET,id,len,data
    char* spI8 = FindContext((char*)m_U8PackageBuffer + 2, m_U16Count, ",", 1);     //查找第一个','
    U32 DataLen;
    U32 i;
    if (spI8 == NULL)
    {
        return;
    }
    spI8 = FindContext(spI8 + 1, m_U16Count - ((U32)spI8 - (U32)m_U8PackageBuffer), ",", 1);     //查找第一个','
    if (spI8 == NULL)
    {
        return;
    }
    spI8++;
    DataLen = atoi(spI8);
    spI8 = strstr(spI8, ",");
    if (spI8 == NULL)
    {
        return;
    }
    spI8++;
    for (i = 0; i < DataLen; i++)
    {
        AnalyseTcpData((U8)(*spI8));
        spI8++;
    }
}

void CSIM7600Module::ProcessTcpPack()
{
    m_U16HeartTicket = 0;
    m_U16OverTime = 0;
       
#if (NETWORK_REAL_SEND == 0)       
//    if (m_U8TcpRcvPkgData[6] == POWERON_CMD || m_U8TcpRcvPkgData[6] == POWEROFF_CMD || m_U8TcpRcvPkgData[6] == REISSUEPOWEROFF_CMD || m_U8TcpRcvPkgData[6] == REALDATAEVENT_CMD)
//    {        
//        HAL_UART_Transmit(&huart3, (uint8_t*)m_WifiDataPkg.SendBuf, m_WifiDataPkg.DataLen, 1000);  
//    }
        
    if (m_WifiDataManager.DelHaveSendData(m_U8TcpRcvPkgData[6], m_U8ReadBuf, &m_U16ReadBufLength))      //补发关机包
    {
        ReissuePowerOffData();
    }
    else
    {
        SendNetData(m_U8ReadBuf, m_U16ReadBufLength);        
    }
#else
    m_WifiDataPkg.U8SendStep =  NET_SEND_DATA_NONE;    
    if (m_U8TcpRcvPkgData[6] == POWERON_CMD)
    {
        g_U8HaveSendPowerOnOff = 2;
    }
    else if (m_U8TcpRcvPkgData[6] == POWEROFF_CMD)
    {
        g_U8HaveSendPowerOnOff = 0;
    }
    else if (g_U8HaveSendPowerOnOff == 3)
    {
        SendNetData(m_U8PowerOnOffPkgBuf, 18);
    }
    /*if (m_U8TcpRcvPkgData[6] == POWERON_CMD || m_U8TcpRcvPkgData[6] == POWEROFF_CMD)
 || m_U8TcpRcvPkgData[6] == REISSUEPOWEROFF_CMD || m_U8TcpRcvPkgData[6] == REALDATAEVENT_CMD)
    {
        m_U16ReadBufLength = 0;
    }*/
    else if (m_U16ReadBufLength)
    {
        SendNetData(m_U8ReadBuf, m_U16ReadBufLength);
    }
#endif    
}

void CSIM7600Module::AddTcpPackageData(U8 Data)
{
    if (m_U8TcpDataInIdx < MAX_TCP_PACKAGE_LEN)
    {
        m_U8TcpRcvPkgData[m_U8TcpDataInIdx] = Data;
        m_U8TcpDataInIdx++;
    }
}

void CSIM7600Module::AnalyseTcpData(U8 Data)
{
    U8 PkgTail[4] = {NET_PACKAGE_TAIL1, NET_PACKAGE_TAIL2, NET_PACKAGE_TAIL3, NET_PACKAGE_TAIL4};
	U8 sU8Flag = 1;

	while (sU8Flag)
	{
		sU8Flag = 0;
        switch (m_U8TcpAnalyseStep)
        {
            case 0: //0x77:
                if (Data == NET_PACKAGE_HEAD1)
                {
                    m_U8TcpDataInIdx = 0;
                    m_U8TcpAnalyseStep++;
                    AddTcpPackageData(Data);
                }
                break;
            case 1://0x66:
                if (Data == NET_PACKAGE_HEAD2)
                {
                    m_U8TcpAnalyseStep++;
                    AddTcpPackageData(Data);
                }
                else
                {
                    m_U8TcpAnalyseStep = 0;
                    sU8Flag = 1;
                }
                break;
            case 2://0x07:
                if (Data == 0xF0 || Data == 0xF1)
                {
                    m_U8TcpAnalyseStep++;
                    AddTcpPackageData(Data);
                }
                else
                {
                    m_U8TcpAnalyseStep = 0;
                    sU8Flag = 1;
                }
                break;
            case 3:
                m_U16ParameterLen = ((U16)Data) << 8;
                m_U8TcpAnalyseStep++;
                AddTcpPackageData(Data);
                break;
            case 4:
                m_U16ParameterLen |= Data;
                m_U8TcpAnalyseStep++;
                AddTcpPackageData(Data);
                break;
            case 5:
                AddTcpPackageData(Data);
                if (--m_U16ParameterLen == 0)
                {
                    m_U8TcpAnalyseStep++;
                }
                break;
            case 6:
                AddTcpPackageData(Data);
                m_U8TcpAnalyseStep++;
                break;
            case 7:
            case 8:
            case 9:
            case 10:
                if (Data == PkgTail[m_U8TcpAnalyseStep - 7])
                {
                    if (m_U8TcpAnalyseStep == 10)
                    {
                        m_U8TcpAnalyseStep = 0;
                        //Get Invalid Package
                        ProcessTcpPack();
                    }
                    else
                    {
                        m_U8TcpAnalyseStep++;
                    }
                }
                else
                {
                    m_U8TcpAnalyseStep = 0;
                    sU8Flag = 1;
                }
                break;
            default:
                break;
        }        
    }    
}

void CSIM7600Module::AddRealDataHead()
{
    U16 sU16Length;
    m_NetDataHead.Cmd = REALDATAEVENT_CMD;
    sU16Length = 1 + sizeof(REALDATAHEAD);
    m_NetDataHead.Len[0] = (sU16Length >> 8) & 0xff;
    m_NetDataHead.Len[1] = (sU16Length & 0xff);    
    m_NetDataHead.Index = m_U8SendIdx++;
    memcpy(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.BaseIdx], &m_NetDataHead, sizeof(NETDATAHEAD));
       
    m_RealDataHead.DataSerialID += 1;
    m_RealDataHead.DateTime.Year = g_SD1302RTCDateTime.year - CENTURY;
    m_RealDataHead.DateTime.Mon = g_SD1302RTCDateTime.mon;
    m_RealDataHead.DateTime.Day = g_SD1302RTCDateTime.day;
    m_RealDataHead.DateTime.Hour = g_SD1302RTCDateTime.hour;
    m_RealDataHead.DateTime.Min = g_SD1302RTCDateTime.minu;
    m_RealDataHead.DateTime.Sec = g_SD1302RTCDateTime.sec;
    
    memcpy(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.BaseIdx] + sizeof(NETDATAHEAD), &m_RealDataHead, sizeof(REALDATAHEAD));   
    m_NetDataSendBuf.InIdx = sizeof(NETDATAHEAD) + sizeof(REALDATAHEAD);    
}

void CSIM7600Module::ClearRealData()
{
    m_NetDataSendBuf.BaseIdx = 0;
    m_NetDataSendBuf.InIdx = 0;
}

void CSIM7600Module::AddRealData(U8* pData, U8 DataLen)
{
    if (m_U8NeedDataSaveFlag == 0)
    {
        return;
    }       
    memcpy(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.BaseIdx] + m_NetDataSendBuf.InIdx, pData, DataLen);
    m_NetDataSendBuf.InIdx += DataLen;
    if (m_NetDataSendBuf.InIdx >= 1000)
    {
        m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.BaseIdx][3] = ((m_NetDataSendBuf.InIdx - 5) >> 8);       //+5为包尾，-10(包头+包尾-Index)
        m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.BaseIdx][4] = ((m_NetDataSendBuf.InIdx - 5) & 0xff);
        AddNetPkgTail(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.BaseIdx], m_NetDataSendBuf.InIdx);
        m_NetDataSendBuf.InIdx = 0;
        m_NetDataSendBuf.BaseIdx ^= 1;
        AddRealDataHead();
       	g_SdCradDataSave.PostAddDataEvent(WIFI_REALDATA_SAVE);
    }
}

void CSIM7600Module::SaveWifiDataInfo()
{
#if (NETWORK_REAL_SEND == 0)       
    m_WifiDataManager.SaveWifiDataInfo();
#else    
#endif    
}

void CSIM7600Module::SaveWifiRealData()
{    
#if (NETWORK_REAL_SEND == 0)    
    m_WifiDataManager.SaveNetPackage(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.BaseIdx ^ 1], 1024);
#else
//    memcpy(m_U8ReadBuf, m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.BaseIdx ^ 1], 1024);
//    m_U16ReadBufLength = 1024;
#endif    
}

void CSIM7600Module::SaveWifiPowerOnData()
{
#if (NETWORK_REAL_SEND == 0)    
    m_WifiDataManager.SaveNetPackage(m_U8PowerOnOffPkgBuf, 256, 1);
#else    
    g_U8HaveSendPowerOnOff = 1;
    SendNetData(m_U8PowerOnOffPkgBuf, 256);
//    memcpy(m_U8ReadBuf, m_U8PowerOnOffPkgBuf, 256);
//    m_U16ReadBufLength = 256;
#endif    
}

void CSIM7600Module::SaveWifiPowerOffData()
{
#if (NETWORK_REAL_SEND == 0)        
    m_WifiDataManager.SaveNetPackage(m_U8PowerOnOffPkgBuf, 32, 2);
#else   
    g_U8HaveSendPowerOnOff = 3;
    SendNetData(m_U8PowerOnOffPkgBuf, 18);
    
//    memcpy(m_U8ReadBuf, m_U8PowerOnOffPkgBuf, 18);
//    m_U16ReadBufLength = 18;
#endif    
}

void CSIM7600Module::ReissuePowerOffData()
{
    U8 sU8DataPkg[32] = {NET_PACKAGE_HEAD1, NET_PACKAGE_HEAD2, REISSUEPOWEROFF_CMD, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, NET_PACKAGE_TAIL1, NET_PACKAGE_TAIL2, NET_PACKAGE_TAIL3, NET_PACKAGE_TAIL4};
   
    sU8DataPkg[3] = 0x00;    
    sU8DataPkg[4] = 7;
    sU8DataPkg[5] = m_U8SendIdx++;
    memcpy(&sU8DataPkg[6], &m_DeviceInfo, 6);
   
    AddNetPkgTail(sU8DataPkg + 2, 10);    
            
    SendNetData(sU8DataPkg, 17);         
}

void CSIM7600Module::UpdateServerConnectStatus(U8 Flag)
{
    if (m_U8ServerConnectStatus == Flag)
    {
        return;
    }
    if (Flag)
    {
        //HAL_UART_Transmit(&huart3, (uint8_t*)"connect", 10, 1000);       
        m_U8ServerConnectStatus = CONNECTED;
        UpdateIconStatus(WIFI_COMM_STATE_INDICATION_BIT, 1); 
        SendHandshake();                                               //与服务器连接后发送握手指令
    }
    else
    {
        m_U8NeedDataSaveFlag = 0;
        m_WifiDataManager.SetDataSaveFlag(0);        
        //HAL_UART_Transmit(&huart3, (uint8_t*)"disconnec", 10, 1000);       
        m_U8ServerConnectStatus = DISCONNECTED;
        UpdateIconStatus(WIFI_COMM_STATE_INDICATION_BIT, 0);       
    }
}

U8 CSIM7600Module::GetConnectStatus()
{
    return m_U8ServerConnectStatus;
}

void CSIM7600Module::ProcessPack()
{
    m_U32RightPkgCnt++;
    if (memcmp(m_U8PackageBuffer, "OK", 2) == 0)
    {
        m_pCurrWifiCmdPkg = NULL;   
        m_pCurrWifiCmdPkg->CmdLen = 0; 
    }
    else if (memcmp(m_U8PackageBuffer, "ERROR", 5) == 0)
    {        
    }
    else
    {
        char* spI8Buf = strstr((char*)m_U8PackageBuffer, ":");
        if (spI8Buf == NULL)
        {
            return;
        }
        if (memcmp(m_U8PackageBuffer + 1, (char*)m_pCurrWifiCmdPkg->Pkg + 3, (U32)spI8Buf - (U32)m_U8PackageBuffer - 3) == 0)
        {
            m_pCurrWifiCmdPkg = NULL;        
        }
    }
}

U8 CSIM7600Module::AnalyseData(U8 Data)
{	    
    int ret = -1;
    static uint16_t index = 0;
    static uint32_t tick = 0;
    static uint8_t flag = 0;    

    if (((HAL_GetTick() - tick) > AT_RX_TIMEOUT) || (index >= AT_RX_BUF_SIZE))    //超时处理，或者是数据包长度够长异常处理
    {
        index = 0;
        flag = 0;
    }
    tick = HAL_GetTick();
    cmdbuf[index++] = Data;
    if (m_U16RcvDataLen)
    {
        m_U16RcvDataLen--;
        if (m_U16RcvDataLen == 0)
        {
            at_cmd_parse(cmdbuf, index, 1);
            index = 0;
            flag = 0;
        }
    }
    
    if ((Data == '\n') && flag)        //收到\r\n解析数据包
    {
        ret = at_cmd_parse(cmdbuf, index);
        flag = 0;
        index = 0;
    }
    else if (Data == '\r')
    {
        flag = 1;
    }
    else if (Data == '>' && m_WifiDataPkg.U8SendStep == NET_WAIT_SEND1)
    {
        flag = 0;
        index = 0;
        cmdbuf[index] = Data;
        ret = at_cmd_parse(cmdbuf, index);
    }
    else
    {
        flag = 0;
    }

    return ret;

//    AddPackageData(Data);
//    if (m_U32RspTicket == 0)
//    {
//        m_U32RspTicket = HAL_GetTick();
//    }    
//    return 0;
}

char* CSIM7600Module::FindContext(char* pSrc, I16 SrcLen, const char* pContext, I16 ContextLen)
{
    I16 i;
    for (i = 0; i <= SrcLen - ContextLen; i++)
    {
        if (memcmp(pSrc + i, pContext, ContextLen) == 0)
        {
            return pSrc + i;
        }
    }
    return NULL;
}

void CSIM7600Module::AnalyseString()
{  
    U32 sU32Remain = 0;
    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+WEVENT:", 10))   //包头
    {
        //+WEVENT：STATION_UP
        //+WEVENT：STATION_DOWN
        if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "STATION_UP", strlen("STATION_UP")))                 //AP连接成功
        {
            //UpdateServerConnectStatus(0);
        }
        else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "STATION_DOWN", strlen("STATION_DOWN")))          //AP连接失败
        {   
            UpdateServerConnectStatus(0);
        }        
    }
    else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "+CIPEVENT:", strlen("+CIPEVENT:")))   //包头
    {
        //+CIPEVENT：id,SERVER,DISCONNECTED
        //+CIPEVENT：id,SERVER,CONNECTED
        //+CIPEVENT：id,SERVER,CLOSED
        //+CIPEVENT：SOCKET,id,len,data
        if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "SERVER,CONNECTED", strlen("SERVER,CONNECTED")))          //与服务器建立连接
        {
            UpdateServerConnectStatus(1);
        }
        else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "SERVER,DISCONNECTED", strlen("SERVER,DISCONNECTED")))   //与服务器断开连接
        {   
            UpdateServerConnectStatus(0);
        }
        else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "SERVER,CLOSED", strlen("SERVER,CLOSED")))        //关闭服务器连接
        {                    
            UpdateServerConnectStatus(0);
        }
        else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "SOCKET", strlen("SOCKET")))        //收到套接字发送来的数据
        {
            AnalyseTcpDatas();
        }        
    }         
    else if (m_pCurrWifiCmdPkg)
    {
        m_pCurrWifiCmdPkg->CmdLen = 0;
        m_pCurrWifiCmdPkg = NULL; 
    }    
    if (m_U16Count > m_U16AtRspLen)
    {
        sU32Remain = m_U16Count - m_U16AtRspLen;
        memcpy(m_U8PackageBuffer, m_U8PackageBuffer + m_U16AtRspLen, sU32Remain);
    }        
    Reset();
    m_U16Count = sU32Remain;
}

void CSIM7600Module::SendCmdPkg(PWIFICMDPKG pWifiCmdPkg)
{  
    Reset();
    m_U32RspTicket = 0;
    HAL_UART_Transmit(&huart7, (uint8_t*)pWifiCmdPkg->Pkg, pWifiCmdPkg->CmdLen, 1000);           
}

U32 g_U32SendTick = 0;
U32 g_U32TotalSendTick = 0;
U32 g_U32TotalSendDataCnt = 0;

void CSIM7600Module::SendDataPkg(PWIFIDATAPKG pWifiDataPkg)
{    
    if (pWifiDataPkg->U8SendStep != NET_START_SEND_DATA && pWifiDataPkg->U8SendStep != NET_SEND_DATA)
    {
        return;
    }
    m_U32RspTicket = 0;
        
        if (pWifiDataPkg->U8SendStep == NET_START_SEND_DATA)
        {
            pWifiDataPkg->U8SendStep = NET_WAIT_SEND1;
            HAL_UART_Transmit(&huart7, (uint8_t*)(pWifiDataPkg->SendBuf), pWifiDataPkg->U16CmdLen, 1000);      
        }
        else if (pWifiDataPkg->U8SendStep == NET_SEND_DATA)
        {
            if (g_U32SendTick == 0 && pWifiDataPkg->SendBuf[pWifiDataPkg->U16CmdLen + 2] == 0x10)
            {
                g_U32SendTick = HAL_GetTick();
            }
            else if (pWifiDataPkg->SendBuf[pWifiDataPkg->U16CmdLen + 2] == 0x05)
            {
                g_U32TotalSendTick = HAL_GetTick() - g_U32SendTick;                
            }
            if (pWifiDataPkg->SendBuf[pWifiDataPkg->U16CmdLen + 2] == 0x10)
            {
                g_U32TotalSendDataCnt += BUF_SIZE;
            }
        
            pWifiDataPkg->U8SendStep = NET_WAIT_SEND2;
            HAL_UART_Transmit(&huart7, (uint8_t*)(pWifiDataPkg->SendBuf) + pWifiDataPkg->U16CmdLen, pWifiDataPkg->U16SDataLen, 3000);     
//            HAL_UART_Transmit(&huart3, (uint8_t*)(pWifiDataPkg->SendBuf) + pWifiDataPkg->U16CmdLen, pWifiDataPkg->U16SDataLen, 3000);   
            if (HAL_GetTick() - g_U32SendTick >= 1000 * 30 && g_U32SendTick) //30S
            {
                g_U8HaveSendPowerOnOff = 3;
                g_U32TotalSendTick = HAL_GetTick() - g_U32SendTick;   
            }
        }
}

void CSIM7600Module::AddNetPkgTail(U8* pData, U16 DataLen)
{
    pData[DataLen] = CheckSum(pData, DataLen);
    pData[1 + DataLen] = NET_PACKAGE_TAIL1;
    pData[2 + DataLen] = NET_PACKAGE_TAIL2;
    pData[3 + DataLen] = NET_PACKAGE_TAIL3;
    pData[4 + DataLen] = NET_PACKAGE_TAIL4;    
}

void CSIM7600Module::SendHandshake()
{
    U8 sU8DataPkg[64];
    U16 sU16Length;
    m_NetDataHead.Cmd = HANDSHAKE_CMD;
    sU16Length = 1 + sizeof(NETDEVICEINFO);
    m_NetDataHead.Len[0] = ((sU16Length >> 8) & 0xff);
    m_NetDataHead.Len[1] = (sU16Length & 0xff);
    m_NetDataHead.Index = m_U8SendIdx++;
    memcpy(sU8DataPkg, &m_NetDataHead, sizeof(NETDATAHEAD));
    memcpy(sU8DataPkg + sizeof(NETDATAHEAD), &m_DeviceInfo, sizeof(m_DeviceInfo));
    AddNetPkgTail(sU8DataPkg + sizeof(NETDATAHEAD) - 1, sU16Length);    
    SendNetData(sU8DataPkg, sU16Length + 10);
}

void CSIM7600Module::SendHeartBeat()
{
//    U8 sU8DataPkg[18] = {NET_PACKAGE_HEAD1, NET_PACKAGE_HEAD2, 0x04, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, NET_PACKAGE_TAIL1, NET_PACKAGE_TAIL2, NET_PACKAGE_TAIL3, NET_PACKAGE_TAIL4};
//    if (m_U8ServerConnectStatus != CONNECTED)
//    {
//        return;
//    }            
//    if (m_U16HeartTicket >= (1000 / WIFI_SEND_TIME_CYCLE) * 3) //3S
//    {
//        m_U16HeartTicket = 0;
//        memcpy(sU8DataPkg + 5, &m_DeviceInfo.Model, 6);
//        sU8DataPkg[5] = m_U8SendIdx++;
//        AddNetPkgTail(sU8DataPkg + sizeof(NETDATAHEAD) - 1, 7);  

//        SendNetData(sU8DataPkg, 17);
//    }
//    else
//    {
//        m_U16HeartTicket++;
//    }
    
    if (g_U8HaveSendPowerOnOff != 2)
    {
        return;
    }
        
    Data[0] = NET_PACKAGE_HEAD1;
    Data[1] = NET_PACKAGE_HEAD2;
    Data[2] = REALDATAEVENT_CMD;
    Data[3] = ((BUF_SIZE - 10) >> 8);
    Data[4] = ((BUF_SIZE - 10) & 0xff);
    memcpy(Data + 5, &m_RealDataHead, sizeof(REALDATAHEAD)); 
    memset(Data + 5 + sizeof(REALDATAHEAD), 0xff, (BUF_SIZE - 10) - sizeof(REALDATAHEAD));
    Data[(BUF_SIZE - 10)] = 0x00;
    Data[(BUF_SIZE - 9)] = NET_PACKAGE_TAIL1;
    Data[(BUF_SIZE - 8)] = NET_PACKAGE_TAIL2;
    Data[(BUF_SIZE - 7)] = NET_PACKAGE_TAIL3;
    Data[(BUF_SIZE - 6)] = NET_PACKAGE_TAIL4;
    SendNetData(Data, BUF_SIZE);
}

void CSIM7600Module::OverTime()
{
    m_U16OverTime++;
    if (m_U16OverTime == OVER_TIME)
    {
        UpdateServerConnectStatus(0);
        return;
    }    
}

void CSIM7600Module::SendPowerOnOffNetData(U8 Flag/*, RTC_TimeTypeDef1* RtcTime*/)
{
    U16 sU16Length;
    
    if (m_U8ServerConnectStatus != CONNECTED)
    {        
        return;
    }
   
    m_NetDataHead.Cmd = Flag ? POWERON_CMD : POWEROFF_CMD;
    m_NetDataHead.Index = m_U8SendIdx++;
  
    if (Flag)
    {
        ClearRealData();
        m_RealDataHead.DataSerialID = 0;
        
        sU16Length = 1 + sizeof(m_NetPowerOnOffInfo);//1 + sizeof(m_NetPowerOnOffInfo)取消Flag标志
                     
        m_NetPowerOnOffInfo.DateTime.Year = g_SD1302RTCDateTime.year - CENTURY;
        m_NetPowerOnOffInfo.DateTime.Mon = g_SD1302RTCDateTime.mon;
        m_NetPowerOnOffInfo.DateTime.Day = g_SD1302RTCDateTime.day;
        m_NetPowerOnOffInfo.DateTime.Hour = g_SD1302RTCDateTime.hour;
        m_NetPowerOnOffInfo.DateTime.Min = g_SD1302RTCDateTime.minu;
        m_NetPowerOnOffInfo.DateTime.Sec = g_SD1302RTCDateTime.sec;
        m_pConfigData->UpdateNetSystemConfig(&m_NetPowerOnOffInfo);
    }
    else
    {
        sU16Length = 8;
    }
    m_NetDataHead.Len[0] = ((sU16Length >> 8) & 0xff);
    m_NetDataHead.Len[1] = (sU16Length & 0xff);
    m_NetDataHead.Index = m_U8SendIdx++;
    memcpy(m_U8PowerOnOffPkgBuf, &m_NetDataHead, sizeof(NETDATAHEAD));
    //m_U8PowerOnOffPkgBuf[6] = Flag;   //取消Flag标志
    if (Flag)
    {
        memcpy(m_U8PowerOnOffPkgBuf + 6, (void*)&m_NetPowerOnOffInfo, sizeof(NETPOWERONOFFINFO));//m_U8PowerOnOffPkgBuf + 7取消Flag标志
    }
    else
    {
        memcpy(m_U8PowerOnOffPkgBuf + 6, (void*)&m_DeviceInfo, 6);
    }
    AddNetPkgTail(m_U8PowerOnOffPkgBuf + 5, sU16Length);    
    if (Flag == 0)
    {
        SaveWifiPowerOffData();
    }
    else
    {
        SaveWifiPowerOnData();
        AddRealDataHead();
        m_U8NeedDataSaveFlag = 1;
        m_WifiDataManager.SetDataSaveFlag(1);        
    }    
}

void CSIM7600Module::PushCmdPkg(PWIFICMDPKG pWifiCmdPkg)
{
    if (((m_WifiCmdPkgList.InIdx + 1) % WIFI_MAX_PKG_CNT) == m_WifiCmdPkgList.OutIdx)
    {
        assert_param("Wifi Cmd List is Full!");
        return;
    }    
    memcpy(&m_WifiCmdPkgList.CmdPkg[m_WifiCmdPkgList.InIdx], pWifiCmdPkg, sizeof(WIFICMDPKG));
    m_WifiCmdPkgList.InIdx = (m_WifiCmdPkgList.InIdx + 1) % WIFI_MAX_PKG_CNT;
}

void CSIM7600Module::PopCmdPkg()
{
    if (m_WifiCmdPkgList.OutIdx != m_WifiCmdPkgList.InIdx)
    {        
        m_pCurrWifiCmdPkg = &m_WifiCmdPkgList.CmdPkg[m_WifiCmdPkgList.OutIdx];        
        m_WifiCmdPkgList.OutIdx = (m_WifiCmdPkgList.OutIdx + 1) % WIFI_MAX_PKG_CNT;       
    }
}

void CSIM7600Module::AutoSendWifiCmdPkg()
{    
    if (m_pCurrWifiCmdPkg)
    {
        m_U32SendIntervalTimes += WIFI_SEND_TIME_CYCLE;
        if (m_pCurrWifiCmdPkg->CmdLen)
        {
            //if (m_U32SendIntervalTimes >= MAX_SEND_INTERVAL)
            if (m_U32SendIntervalTimes >= m_pCurrWifiCmdPkg->RspWaitTime)
            {
                m_U32SendIntervalTimes = 0;

                m_U8CurrPkgSendTimes++;                
                if(m_U8CurrPkgSendTimes >= MAX_SEND_TIMES)
                {
                    m_pCurrWifiCmdPkg = NULL;
                }
                else
                {
                    SendCmdPkg(m_pCurrWifiCmdPkg);
                }
            }
        }
    }
    else
    {
        if (m_WifiCmdPkgList.OutIdx != m_WifiCmdPkgList.InIdx)
        {
            m_U32SendIntervalTimes = 0;
            PopCmdPkg();                   
            m_U8CurrPkgSendTimes = 0;
            SendCmdPkg(m_pCurrWifiCmdPkg);            
         }
         else
         {             
            SendDataPkg(&m_WifiDataPkg);                 
         }             
    }
}

void CSIM7600Module::WaitRsp()
{    
    U16 i;
    U16 sU16RDataLen = 0;    
    char* spI8[3] = {NULL, NULL, NULL};    
    const char* pI8Cmd = NULL;
    U8 sU8Data[2] = {0, 0};
    
    if (m_U8ModuleInitFinish == 0)
    {
        spI8[0] = FindContext((char*)m_U8PackageBuffer, m_U16Count, "SMS DONE", strlen("SMS DONE"));
        if (spI8[0])
        {
            m_U8ModuleInitFinish = 1;
            Reset();
        }
        return;
    }

    if (m_U16Count < m_U16MaxPkgSize)
    {
        m_U8PackageBuffer[m_U16Count] = 0;
    }
    
    if (m_pCurrWifiCmdPkg)
    {
        pI8Cmd = g_SIMCmd[m_pCurrWifiCmdPkg->CmdType - NET_OPEN];
        if (m_pCurrWifiCmdPkg->CmdType == SET_ECHO)
        {
            spI8[0] = FindContext((char*)m_U8PackageBuffer, m_U16Count, "OK", 2);
            if (spI8[0])
            {
                m_pCurrWifiCmdPkg = NULL;
            }
        }
        else
        {
            spI8[0] = FindContext((char*)m_U8PackageBuffer, m_U16Count, pI8Cmd, strlen(pI8Cmd));
            spI8[1] = FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n", 2);
            if (spI8[0] && spI8[1])
            {
                switch (m_pCurrWifiCmdPkg->CmdType)
                {
                    case SET_ECHO:
                        break;
                    case NET_OPEN:
                        sU8Data[0] = atoi(spI8[0] + 8);
                        break;
                    case NET_CLOSE:                    
                        break;
                    case IP_OPEN:
                        sU8Data[0] = atoi(spI8[0] + 9);
                        sU8Data[1] = atoi(FindContext(spI8[0] + 9, m_U16Count - (spI8[0] - (char*)m_U8PackageBuffer + 9), ",", 1));
                        UpdateServerConnectStatus(sU8Data[0] == 0 && sU8Data[1] == 0);
                        break;
                    case IP_STOP:
                        UpdateServerConnectStatus(0);
                        break;
                    default:
                        break;
                }
                m_pCurrWifiCmdPkg = NULL;
            }            
        }
    }
    else
    {
        if (m_WifiDataPkg.U16CmdLen && m_WifiDataPkg.U8SendStep == NET_START_SEND_DATA)
        {
            spI8[0] = FindContext((char*)m_U8PackageBuffer, m_U16Count, ">", 1);
            if (spI8[0])
            {
                Reset();
                m_WifiDataPkg.U8SendStep = NET_SEND_DATA;
            }            
        }        
    }
    spI8[0] = FindContext((char*)m_U8PackageBuffer, m_U16Count, "+IPCLOSE:", 9);
    spI8[1] = FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n", 2);
    if (spI8[0] && spI8[1]) //ipclose
    {
        
    }
    //RECV FROM:***************:39723 +IPD4 1234 接收Server主动发送的数据
    spI8[0] = FindContext((char*)m_U8PackageBuffer, m_U16Count, "RECV FROM:", 10);
    if (spI8[0])
    {
        spI8[1] = FindContext(spI8[0], m_U16Count, "+IPD", 4);
    }
    if (spI8[0] && spI8[1]) 
    {
        sU16RDataLen = atoi(spI8[1] + 4);        
        spI8[2] = FindContext(spI8[1] + 6, m_U16Count - (spI8[1] - (char*)m_U8PackageBuffer + 6), "\r\n", 2);
        if (spI8[2] && (spI8[2] - spI8[1] >= sU16RDataLen))
        {
            for (i = 0; i < sU16RDataLen; i++)
            {
                AnalyseTcpData((U8)(*spI8[2]));
                spI8[2]++;
            }
        }
    }
    
    if (m_U32RspTicket != 0)
    {
        if (HAL_GetTick() - m_U32RspTicket >= 3000)     //如果距离收到上次数据已达到3000ms，则认为未收到数据。
        {
            if (m_pCurrWifiCmdPkg)
            {
                m_U32SendIntervalTimes = m_pCurrWifiCmdPkg->RspWaitTime;    //立即重新发送数据包
            }    
            else if (m_WifiDataPkg.U16SDataLen)
            {
                m_U32SendIntervalTimes = m_WifiDataPkg.RspWaitTime;
            }
        }
    }
}

void CSIM7600Module::RunTask()
{
    OS_ERR  err;      	   

    const PARAMETERCONFIG* ParameterData = m_pConfigData->GetParameterConfig();      
    
    while (1)
    {
        ProcessRcvData();
//        WaitRsp();
        //OSTimeDly(50, OS_OPT_TIME_DLY, &err);    
        OSTimeDly(WIFI_SEND_TIME_CYCLE, OS_OPT_TIME_DLY, &err);
        if (m_U8ModuleInitFinish == 0)
        {
            continue;
        }
        AutoSendWifiCmdPkg();
//        if (ParameterData->WifiConfig.Switch)       //只有在WIFI模块存在时，才进行以下处理
//        {
            if (m_U8ServerConnectStatus)
            {
                SendHeartBeat();
                if (m_WifiDataManager.TimerSaveDataInfo())
                {
                    g_SdCradDataSave.PostAddDataEvent(WIFI_INFODATA_SAVE);
                }
            }
//        }
//        else
//        {
//            HAL_GPIO_WritePin(WIFI_CONTROL_GPIO_Port, WIFI_CONTROL_Pin, GPIO_PIN_SET);
//            UpdateServerConnectStatus(0);
//        }
    }
}

CSIM7600Module g_Sim7600UartModule;

void AppTaskSIM7600Uart(void *p_arg)
{		
    g_Sim7600UartModule.RunTask();    
}

void CreateSimUartTask(void)
{
    OS_ERR  err = OS_ERR_NONE;  	    
	/**************创建血氧任务*********************/			 
	OSTaskCreate((OS_TCB       *)&AppTaskSimUartTCB,              
                 (CPU_CHAR     *)"App Task SIM Uart",
                 (OS_TASK_PTR   )AppTaskSIM7600Uart, 
                 (void         *)0,
                 (OS_PRIO       )APP_CFG_TASK_SIMUART_PRIO,
                 (CPU_STK      *)&AppTaskSimUartStk[0],
                 (CPU_STK_SIZE  )APP_CFG_TASK_SIMUART_STK_SIZE / 10,
                 (CPU_STK_SIZE  )APP_CFG_TASK_SIMUART_STK_SIZE,
                 (OS_MSG_QTY    )0,
                 (OS_TICK       )0,
                 (void         *)0,
                 (OS_OPT        )(OS_OPT_TASK_STK_CHK | OS_OPT_TASK_STK_CLR),
                 (OS_ERR       *)&err);			
}




