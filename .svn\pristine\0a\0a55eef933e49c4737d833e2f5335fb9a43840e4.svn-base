#ifndef __SERIAL_PROTOCOL_ADAPTER_H_
#define __SERIAL_PROTOCOL_ADAPTER_H_

#ifdef __cplusplus
extern "C" {
#endif

//解析协议端口识别
#define SERIAL_PROTOCOL_SPO2        (0x00)
#define SERIAL_PROTOCOL_PC          (0x01)
#define SERIAL_PROTOCOL_MCU         (0x02)
#define SERIAL_PROTOCOL_NONE        (0x03)

#define SPO2_PROTOCOL_HEADR         (0xfcfa)
#define PC_UART_HEADER              (0xA5)

void task_serial_protocol_adapter_event_poll(void);

#ifdef __cplusplus
}  /* extern "C" */
#endif

#endif




