#ifndef __VALUETOSTR_H__
#define __VALUETOSTR_H__

#include "DataType.h"

/*
流量曲线，超出200显示200，小于0，显示0
压力曲线，超出30显示30，小于0，显示0
数据也一样，负数显示--，超范围显示最大值
IE比，0显示 -:--，其它的1:data/10
呼吸时间:60/BPM,不在5-40显示--
Leak:0-2000,200L
TV:0-2000,200L
RR:5-40
*/

#define MAX_FLOW_VALUE  2000
#define MIN_FLOW_VALUE  0
#define MIN_FLOW_VALUE_1  -2000

#define MAX_PRESSURE_VALUE  300
#define MIN_PRESSURE_VALUE  0

#define MAX_INSPTIME_VALUE    400     //60S/5RPM*10
#define MIN_INSPTIME_VALUE    1      //60S/40RPM*10

#define MAX_RR_VALUE  80
#define MIN_RR_VALUE  1
#define MAX_SPO2_VALUE  100
#define MIN_SPO2_VALUE  35

#define MAX_PR_VALUE    250
#define MIN_PR_VALUE    25

#define MAX_LEAK_VALUE  2000
#define MIN_LEAK_VALUE  0

#define MAX_TV_VALUE    20000
#define MIN_TV_VALUE    0

#define MAX_MV_VALUE    400             //分钟通气量
#define MIN_MV_VALUE    0

#define MAX_PIPETEMP_VALUE 400
#define MIN_PIPETEMP_VALUE  1

typedef struct
{
	float fPF_a;
	float fPF_b;
	float fPF_c;
    float fPF_d;
}LeakCalc_TypeDef;
extern LeakCalc_TypeDef LeakCalc;
I16 GetRealMaskFlow(I16 PressValue);
I16 GetAvgDispLeakFlow(int8_t IsMask, I16 shAvgMaskFlow, I16 shAvgSumLeakFlow);
I16 CheckLeakFlowSide(I16 shVal);
void mask_type_leak_parm_calc(void);

U8 FlowIsValid(I16 Value);
void FlowBuffPrint(I16 Value, char* str);
void LeakageBuffPrint(I16 Value, char* str);
I16 LeakageIsMaskValue(int8_t IsMask, I16 PressValue, I16 Value);
U8 PressureIsValid(I16 Value);
void PressureBuffPrint(I16 Value, char* str);
void IERateBuffPrint(U16 Value, char* str);
void InspTimeBuffPrint(U16 Value, char* str);
void TvBuffPrint(U16 Value, char* str);
void MvBuffPrint(U16 Value, char* str);
void RRBuffPrint(U16 Value, char* str);
U8 SpO2IsValid(U16 Value);
void SpO2BuffPrint(U16 Value, char* str);
U8 PRIsValid(U16 Value);
void PRBuffPrint(U16 Value, char* str);
U8 PipeTempIsValid(U16 Value);
void PipeTempBuffPrint(U16 Value, char* str);

#endif

