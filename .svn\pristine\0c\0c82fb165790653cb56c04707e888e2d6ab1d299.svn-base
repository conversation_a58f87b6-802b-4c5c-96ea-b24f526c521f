#ifndef __OXIMODULE_H__
#define __OXIMODULE_H__

#include "DataType.h"

#define OXI_ISR_RCV_LENGTH 12
#define INVALID_SPO2_VALUE 0x00
#define INVALID_PR_VALUE 0x00

class COxiModule
{
public:
    COxiModule();
    ~COxiModule();
    U8 GetConnectStatus();
public:
    U8 GetPR();
    U8 GetSpO2();
};

extern COxiModule g_OxiModule;
extern U8 g_U8OxiRcvBuf[OXI_ISR_RCV_LENGTH];

extern OS_TCB AppTaskSpO2TCB;
extern CPU_STK AppTaskSpO2Stk[APP_CFG_TASK_SPO2_STK_SIZE];

void AppTaskSpO2Uart(void *p_arg);

#endif
