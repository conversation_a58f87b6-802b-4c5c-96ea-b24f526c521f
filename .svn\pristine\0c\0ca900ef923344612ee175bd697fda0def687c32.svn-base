#include "TidalDispose.h"
#include "Filter.h"
#include "main.h"
#include <math.h>
#include "delay/delay.h"

uint8_t s_bInTriggerSignal = 0;
int16_t s_shInTriggerBaseFlow = 0;
int16_t s_shInTriggerBasePress = 0;

int32_t s_swInspTidalFlow = 0;
int32_t s_swExpTidalFlow = 0;
int32_t s_swInspTidalVolResult = 0;
int32_t s_swExpTidalVolResult = 0;

int32_t s_swInspLeakFlow;
int32_t s_swExpLeakFlow;
int32_t s_swInspTidalLeakResult;
int32_t s_swExpTidalLeakResult;

int32_t swTempInsp = 0;
int32_t s_swInspTidalCorrect = 0;
int16_t s_shFilterTidalVol = 0;

TidalVolAverage_t s_avgTidalVol;

/**
 * @brief  从小到大排序
 * @param  arr 滤波数据缓存
 * @param  length 滤波数据长度
 */
void TidalVolInsertSort(int16_t *arr, uint32_t length)
{
    for(int i = 1; i < length; i++)
    {
        int j;
        if(arr[i] < arr[i - 1])
        {
            int16_t temp = arr[i];
            for(j = i - 1; j >= 0 && temp < arr[j]; j--)
            {
                arr[j + 1] = arr[j];
            }
            arr[j + 1] = temp;
        }
    }
}

/**
 * @brief  获取平均滤波数据
 * @param  p 平均滤波数据结构指针
 * @param  shVal 原始值
 * @return 滤波后的数据
 */
uint32_t TidalVolAverageFilter(TidalVolAverage_t *p, int16_t shVal)
{
#define AVERAGE_FILTER_SIZE(size)     (size * 4 / 10)
    if(p->hIndex > AVERAGE_WIN_SIZE)
    {
        p->hIndex = 0;
    }
    p->shBuf[p->hIndex] = shVal;
    p->hIndex++;
    p->hSize++;
    if(p->hSize > AVERAGE_WIN_SIZE)
    {
        p->hSize = AVERAGE_WIN_SIZE;
    }
    memcpy(p->shSortBuf, p->shBuf, sizeof(p->shSortBuf));
    TidalVolInsertSort(p->shSortBuf, p->hSize);

    int32_t swSum = 0;
    uint32_t swWinSize = 0;
    for(int32_t n = AVERAGE_FILTER_SIZE(p->hSize); n < (p->hSize - AVERAGE_FILTER_SIZE(p->hSize)); n++)
    {
        swSum += p->shSortBuf[n];
        swWinSize++;
    }
    if(swWinSize != 0)
    {
        p->shAvgValue = swSum / swWinSize;
    }
    return p->shAvgValue;
}

/**
 * @brief  清除潮气量缓存数据
 * @param  无
 * @return 无
 */
void clear_tidal_vol_buffer(void)
{
	s_shFilterTidalVol = 0;
	memset(s_avgTidalVol.shBuf,0,sizeof(s_avgTidalVol.shBuf));
	memset(s_avgTidalVol.shSortBuf,0,sizeof(s_avgTidalVol.shSortBuf));
}


/**
 * @brief  获取呼气潮气量值
 * @return 潮气量
 */
int16_t GetExpTidalVolume(void)
{
    return s_shFilterTidalVol;
}



/**
 * @brief  获取吸气潮气量值
 * @return 潮气量
 */
int16_t GetInspTidalVolume(void)
{
    //吸气潮气量暂使用呼气潮气量替代
    return s_shFilterTidalVol;
}


/**
 * @brief  获取压力基线值
 * @return 压力基线值
 */
int16_t GetTidalPressBaseFilter(void)
{
    extern int16_t g_s16BasePress;
    return  g_s16BasePress;
}

/**
 * @brief  设置潮气量触发计算
 * @param  bInTrigger 1使能 0失能
 * @param  shBaseFlow 触发时流量基线
 */
void SetTidalBaseFlowInTrigger(uint8_t bInTrigger, int16_t shBaseFlow)
{
    s_bInTriggerSignal = bInTrigger;
    s_shInTriggerBaseFlow = shBaseFlow;
    s_shInTriggerBasePress = GetTidalPressBaseFilter();
}

/**
 * @brief  潮气量计算
 * @param  shRealFlow 实时流量
 * @param  shRealBaseFlow 实时流量基线
 */
void TaskTidalVolumeCaculation(int16_t shRealFlow, int16_t shRealBaseFlow)
{
    (void)shRealBaseFlow;
    int16_t shDiffFlow = shRealFlow - s_shInTriggerBaseFlow;

    if(s_bInTriggerSignal > 0)
    {
        s_bInTriggerSignal = 0;

        s_swInspTidalLeakResult = s_swInspLeakFlow * 1 / 6;
        s_swExpTidalLeakResult = s_swExpLeakFlow * 1 / 6;

        //累计潮气量漏气清零
        s_swInspLeakFlow = 0;
        s_swExpLeakFlow = 0;
#if (TIDAL_PLAN_SEL == TIDAL_PLAN_A) 
        //换算为潮气量
        s_swInspTidalVolResult = s_swInspTidalFlow * 1 / 6 - s_swInspTidalLeakResult;
        s_swExpTidalVolResult = s_swExpTidalFlow * 1 / 6 - s_swExpTidalLeakResult;
        s_swInspTidalCorrect = -s_swExpTidalVolResult;
#elif (TIDAL_PLAN_SEL == TIDAL_PLAN_B) 
        //换算为潮气量
        s_swInspTidalVolResult = s_swInspTidalFlow * 1 / 6 - s_swInspTidalLeakResult;
        s_swExpTidalVolResult = s_swExpTidalFlow * 1 / 6 - s_swExpTidalLeakResult;
        s_swInspTidalCorrect = s_swInspTidalVolResult;
#elif (TIDAL_PLAN_SEL == TIDAL_PLAN_C) 
         //换算为潮气量
        s_swInspTidalVolResult = s_swInspTidalLeakResult;
        s_swExpTidalVolResult = s_swExpTidalLeakResult;
        s_swInspTidalCorrect = (s_swInspTidalVolResult - s_swExpTidalVolResult) / 2;
#endif        
        //潮气量正常值的参考范围:成人为8-10ml/kg, 小儿为6-10ml/kg
        if(s_swInspTidalCorrect > 500 && s_swInspTidalCorrect < 20000)
        {
            s_shFilterTidalVol = TidalVolAverageFilter(&s_avgTidalVol, s_swInspTidalCorrect);
        }
        //累计潮气量流速和
        s_swInspTidalFlow = 0;
        s_swExpTidalFlow = 0;
    }

    if(shDiffFlow > 0)
    {
        s_swInspTidalFlow += shDiffFlow;
    }
    else
    {
        s_swExpTidalFlow += shDiffFlow;
    }
}

#if ((TIDAL_PLAN_SEL == TIDAL_PLAN_B) || (TIDAL_PLAN_SEL == TIDAL_PLAN_C)) 
/**
 * @brief  漏气补偿压力值
 * @return 补偿压力
 */
uint16_t GetLeakCompensationPress(void)
{
    extern volatile uint16_t g_u16AddPress;
    return g_u16AddPress;
}

//在CPAP模式下设置不同压力，记录显示漏气量（带压力补偿，且记录的压力为设置值）
#define LEAK_BASE_VALUE_LEN           (15)
const float c_fLeakBasePressVal[LEAK_BASE_VALUE_LEN] = {0, 40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300};
const float c_fLeakBaseFlowVal[LEAK_BASE_VALUE_LEN] = {0, 213.73, 258.13, 298.73, 335.67, 370.47, 402.33, 432.6, 459, 483.6, 509.8, 533.07, 557.2, 578.67, 601.53};
/**
 * @brief  漏气量计算
 * @param  shPress 实时压力
 * @return 漏气量
 */
static int16_t GetLeakFlowForPress(int16_t shPress)
{
    int16_t shRet = 0;
    if(shPress < 0)
    {
        shPress = -shPress;
    }
    float k = 0;
    float b = 0;
    int swIndex = 1;
    while(swIndex < LEAK_BASE_VALUE_LEN)
    {
        if(shPress < c_fLeakBasePressVal[swIndex])
        {
            break;
        }
        swIndex++;
    }
    if(swIndex == LEAK_BASE_VALUE_LEN)
    {
        swIndex -= 1;
    }

    k = (c_fLeakBaseFlowVal[swIndex] - c_fLeakBaseFlowVal[swIndex - 1]) / (c_fLeakBasePressVal[swIndex] - c_fLeakBasePressVal[swIndex - 1]);
    b = c_fLeakBaseFlowVal[swIndex] - k * c_fLeakBasePressVal[swIndex];
    shRet = k * shPress + b;
    
    return shRet;
}

#endif

/**
 * @brief  漏气潮气量计算
 * @param  shRealFlow 实时流量值
 * @param  shRealPress 实时压力值
 */
void TaskTidalLeakVolumeCaculation(int16_t shRealFlow, int16_t shRealPress)
{
#if (TIDAL_PLAN_SEL == TIDAL_PLAN_A)    
    int16_t shTempLeakVal = 0;
    int16_t shDiffVal = 0;
    shTempLeakVal = s_shInTriggerBaseFlow * pow(shRealPress, 1.0 / 3.0) / pow(s_shInTriggerBasePress, 1.0 / 3.0);
    shDiffVal = shTempLeakVal - s_shInTriggerBaseFlow;
    if(shDiffVal > 0)
    {
        s_swInspLeakFlow += shDiffVal;
    }
    else
    {
        s_swExpLeakFlow += shDiffVal;
    }
#elif (TIDAL_PLAN_SEL == TIDAL_PLAN_B)  
    int16_t shDiffVal = GetLeakFlowForPress(shRealPress) - GetLeakFlowForPress(s_shInTriggerBasePress);
    if(shDiffVal > 0)
    {
        s_swInspLeakFlow += shDiffVal;
    }
    else
    {
        s_swExpLeakFlow += shDiffVal;
    }
#elif (TIDAL_PLAN_SEL == TIDAL_PLAN_C)      
    int16_t shDiffVal = shRealFlow - GetLeakFlowForPress(shRealPress - GetLeakCompensationPress());
    if(shDiffVal > 0)
    {
        s_swInspLeakFlow += shDiffVal;
    }
    else
    {
        s_swExpLeakFlow += shDiffVal;
    }
#endif    
}

