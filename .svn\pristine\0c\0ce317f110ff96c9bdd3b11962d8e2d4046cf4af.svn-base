import argparse
import os
import sys

# Get the absolute directory path of the script
script_dir = os.path.dirname(os.path.abspath(__file__))

# Add the `aes/` and `ecdsa/` directories to sys.path
sys.path.append(os.path.join(script_dir, "aes"))
sys.path.append(os.path.join(script_dir, "ecdsa"))

from aes_util import encrypt_file, decrypt_file, generate_aes_key  # Import AES functions
from ecdsa_util import sign_file, verify_file, generate_signing_keys # Import ECDSA functions

FILE_ENC_KEY = os.path.join(script_dir, "../aes_keys/encryption_key_fw.bin")
FILE_SIGING_KEY = os.path.join(script_dir, "../assets/intermediary_fw/intermediary_fw.key")
FILE_PUB_KEY = os.path.join(script_dir, "../assets/intermediary_fw/intermediary_fw.pub.bin")
def generate():
    """Generates cryptographic material in relative locations"""
    generate_aes_key()      # AES Key for Encryption/Decryption
    generate_signing_keys() # Signing Keys for ECDSA

def prepare(input_file, prepared_file=None):
    """Signs the file, then encrypts it."""
    if input_file.endswith('.bin'):
        signed_file = input_file[:-4] + '.signed.bin'
        encrypted_file = input_file[:-4] + '.encrypted.bin'
    else:
        raise ValueError("The file does not have a .bin extension")
    print(FILE_SIGING_KEY)
    sign_file(input_file, signed_file,786368, FILE_SIGING_KEY)  # Sign first

    if prepared_file is None:
        prepared_file = encrypted_file

    encrypt_file(signed_file, prepared_file, FILE_ENC_KEY)  # Then encrypt
    print(f"File signed and encrypted: {prepared_file}")

def validate(encrypted_signed_file, output_file):
    """Decrypts the file, then verifies the signature and removes it from the output file."""
    decrypted_file = encrypted_signed_file + ".decrypted"
    decrypt_file(encrypted_signed_file, decrypted_file, FILE_ENC_KEY)  # Decrypt first
    
    with open(decrypted_file, "rb") as f:
        file_data = f.read()
    
    signature_length = 64  # Assuming ECDSA P-256 signature is 64 bytes
    file_content = file_data[:-signature_length]  # Strip signature
    
    #print(f"Decrypted: {file_data.hex()}")
    verify_file(decrypted_file,FILE_PUB_KEY)  # Verify signature
    
    with open(output_file, "wb") as f:
        f.write(file_content)  # Save file without signature
    
    os.remove(decrypted_file)  # Clean up intermediate decrypted file
    print(f"Decryption and signature verification successful. Output saved as {output_file}")

def main():
    parser = argparse.ArgumentParser(description="CLI tool for signing and encrypting files")
    subparsers = parser.add_subparsers(dest="command")

    parser_prepare = subparsers.add_parser("generate", help="Generates keys")

    parser_prepare = subparsers.add_parser("prepare", help="Sign and encrypt a file")

    parser_prepare.add_argument("-i", "--input", required=True, help="Path to the input file")
    parser_prepare.add_argument("-o", "--output", required=False, help="Path to save the signed and encrypted file")
    parser_prepare.add_argument("-k", "--key", required=False, help="Path to the AES key file")

    parser_validate = subparsers.add_parser("validate", help="Decrypt and verify a file")
    parser_validate.add_argument("-i", "--input", required=True, help="Path to the the signed and encrypted input file")
    parser_validate.add_argument("-o", "--output", required=True, help="Path to save the decrypted file ")
    parser_validate.add_argument("-k", "--key", required=False, help="Path to the AES key file")
    

    args = parser.parse_args()
    
    if args.command == "generate":
        generate()
    elif args.command == "prepare":
        prepare(args.input, args.output)
    elif args.command == "validate":
        validate(args.input, args.output)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
