#include "fuzzy_pid.h"

#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <math.h>

#define NB   -3
#define NM   -2
#define NS   -1
#define ZO   0
#define PS   1
#define PM   2
#define PB   3

//规则库
static const float ruleKp[7][7] =
{
    PB, PB, PM, PM, PS, ZO, ZO,
    PB, PB, PM, PS, PS, ZO, NS,
    PM, PM, PM, PS, ZO, NS, NS,
    PM, PM, PS, ZO, NS, NM, NM,
    PS, PS, ZO, NS, NS, NM, NM,
    PS, ZO, NS, NM, NM, NM, NB,
    ZO, ZO, NM, NM, NM, NB, NB
};

static const float ruleKi[7][7] =
{
    NB, NB, NM, NM, NS, ZO, ZO,
    NB, NB, NM, NS, NS, ZO, ZO,
    NB, NM, NS, NS, ZO, PS, PS,
    NM, NM, NS, ZO, PS, PM, PM,
    NM, NS, ZO, PS, PS, PM, PB,
    ZO, ZO, PS, PS, PM, PB, PB,
    ZO, ZO, PS, PM, PM, PB, PB
};

static const float ruleKd[7][7] =
{
    PS, PS, ZO, ZO, ZO, PB, PB,
    NS, NS, NS, NS, ZO, NS, PM,
    NB, NB, NM, NS, ZO, PS, PM,
    NB, NM, NM, NS, ZO, PS, PM,
    NB, NM, NS, NS, ZO, PS, PS,
    NM, NS, NS, NS, ZO, PS, PS,
    PS, ZO, ZO, ZO, ZO, PB, PB
};

//隶属度计算函数
static void CalcMembership(float *ms, float qv, int *index)
{
    if((qv >= NB) && (qv < NM))
    {
        index[0] = 0;
        index[1] = 1;
        ms[0] = -0.5 * qv - 2.0; //y=-0.5x-2.0
        ms[1] = 0.5 * qv + 3.0; //y=0.5x+3.0
    }
    else if((qv >= NM) && (qv < NS))
    {
        index[0] = 1;
        index[1] = 2;
        ms[0] = -0.5 * qv - 1.0; //y=-0.5x-1.0
        ms[1] = 0.5 * qv + 2.0; //y=0.5x+2.0
    }
    else if((qv >= NS) && (qv < ZO))
    {
        index[0] = 2;
        index[1] = 3;
        ms[0] = -0.5 * qv; //y=-0.5x
        ms[1] = 0.5 * qv + 1.0; //y=0.5x+1.0
    }
    else if((qv >= ZO) && (qv < PS))
    {
        index[0] = 3;
        index[1] = 4;
        ms[0] = -0.5 * qv + 1.0; //y=-0.5x+1.0
        ms[1] = 0.5 * qv; //y=0.5x
    }
    else if((qv >= PS) && (qv < PM))
    {
        index[0] = 4;
        index[1] = 5;
        ms[0] = -0.5 * qv + 2.0; //y=-0.5x+2.0
        ms[1] = 0.5 * qv - 1.0; //y=0.5x-1.0
    }
    else if((qv >= PM) && (qv <= PB))
    {
        index[0] = 5;
        index[1] = 6;
        ms[0] = -0.5 * qv + 3.0; //y=-0.5x+3.0
        ms[1] = 0.5 * qv - 2.0; //y=0.5x-2.0
    }
}

//输入值的量化论域
static void LinearQuantization(_pid *vPID, float _Real_Value, float *qValue)
{
    float thisError;
    float deltaError;
    thisError = vPID->target_val - _Real_Value; //计算当前偏差
    deltaError = thisError - vPID->err_last; //计算偏差增量
//E和EC的量化
    qValue[0] = 3 * thisError / (vPID->maximum - vPID->minimum);
    qValue[1] = 1.5 * deltaError / (vPID->maximum - vPID->minimum);
}

//解模糊
//量化值
float qValue[2] = {0, 0};

int indexE[2] = {0, 0};  //e在规则库中的索引
float msE[2] = {0, 0};   //e的隶属度

int indexEC[2] = {0, 0}; //ec在规则库中的索引
float msEC[2] = {0, 0};   //ec的隶属度
static void FuzzyComputation(_pid *vPID, float _Real_Value)
{
//pid增量值
    float pidvalue[3];
//量化
    LinearQuantization(vPID, _Real_Value, qValue);
//计算e的隶属度和索引
    CalcMembership(msE, qValue[0], indexE);
//计算ec的隶属度和索引
    CalcMembership(msEC, qValue[1], indexEC);
//采用重心法计算pid增量值
    pidvalue[0] = msE[0] * (msEC[0] * ruleKp[indexE[0]][indexEC[0]] + msEC[1] * ruleKp[indexE[0]][indexEC[1]])
                    + msE[1] * (msEC[0] * ruleKp[indexE[1]][indexEC[0]] + msEC[1] * ruleKp[indexE[1]][indexEC[1]]);
    pidvalue[1] = msE[0] * (msEC[0] * ruleKi[indexE[0]][indexEC[0]] + msEC[1] * ruleKi[indexE[0]][indexEC[1]])
                    + msE[1] * (msEC[0] * ruleKi[indexE[1]][indexEC[0]] + msEC[1] * ruleKi[indexE[1]][indexEC[1]]);
    pidvalue[2] = msE[0] * (msEC[0] * ruleKd[indexE[0]][indexEC[0]] + msEC[1] * ruleKd[indexE[0]][indexEC[1]])
                    + msE[1] * (msEC[0] * ruleKd[indexE[1]][indexEC[0]] + msEC[1] * ruleKd[indexE[1]][indexEC[1]]);
//pid增量修正
    vPID->deltaKp = vPID->qKp * pidvalue[0];
    vPID->deltaKi = vPID->qKi * pidvalue[1];
    vPID->deltaKd = vPID->qKd * pidvalue[2];
	
	if(vPID->deltaKp < -8.2)
	{
		vPID->deltaKp = -8.2;
	}
	if(vPID->deltaKp > 1)
	{
		vPID->deltaKp = 1;
	}
	
}


/*程序入口主函数*/
void Fuzzytrans(float _Measure_Vaule)
{
    FuzzyComputation(&pid, _Measure_Vaule);
}

