#include "WifiConnectionStatusPopUpWindow.h"
#include "Led.h"
#include <stdio.h>
#include "Key.h"
#include "ConfigSave.h"
#include "MultiLanguage.h"
#include "GlobalVariable.h"
#include "AlarmManager.h"
#include "StaterBar.h"
#include "CalcDateTime.h"
#include "WifiUartModule.h"
#include "stack.h"

void RepaintWifiConnectionStatusPopUpWindow(void);
static void WifiConnectionStatusPopUpWindowProcess(U8 Key);
WINDOWINFO g_WifiConnectionStatusPopUpWindowWInfo = {NULL, WifiConnectionStatusPopUpWindowProcess, NULL, RepaintWifiConnectionStatusPopUpWindow, NULL, 0, 0};


static GUI_TIMER_HANDLE g_RefreshWifiConnectionStatusTimer = NULL;
static uint8_t g_sU8RefreshWifiConnectionStatueCnt = 0; //30sδ�����ϣ���ʾ"����ʧ�ܣ���������������"
static uint8_t g_sU8WifiConnectionStatus = WIFI_NONE;

char g_sUWifiSSIDNameAndDot[40] = {0};

static uint8_t g_sU8WifiConnectionStatusPopUpScreenFocus = 1;
#if (LCD_TYPE == LCD_5_TFT)
    static GUI_RECT g_sWindowsRect = {ALARMPOPUPWINDOWS_XPOS, ALARMPOPUPWINDOWS_YPOS, ALARMPOPUPWINDOWS_XPOS + 500 - 1, ALARMPOPUPWINDOWS_YPOS + 300 - 1};
    static GUI_RECT g_sTitileRect = {ALARMPOPUPWINDOWS_XPOS, ALARMPOPUPWINDOWS_YPOS, ALARMPOPUPWINDOWS_XPOS + 500 - 1, 70 - 1};
    static GUI_RECT g_sBottonRect = {ALARMPOPUPWINDOWS_XPOS + 4, ALARMPOPUPWINDOWS_YPOS + 224, ALARMPOPUPWINDOWS_XPOS + 4 * 2 + 244 * 2 - 1, ALARMPOPUPWINDOWS_YPOS + 224 + 72 - 1};
    static GUI_RECT g_PopUpRect = {ALARMPOPUPWINDOWS_XPOS + 45, ALARMPOPUPWINDOWS_YPOS + 70, ALARMPOPUPWINDOWS_XPOS + 500 - 1 - 50, ALARMPOPUPWINDOWS_YPOS + 220 - 1};
#elif (LCD_TYPE == LCD_35_TFT)
    static GUI_RECT g_sWindowsRect = {ALARMPOPUPWINDOWS_XPOS, ALARMPOPUPWINDOWS_YPOS, ALARMPOPUPWINDOWS_XPOS + 300 - 1, ALARMPOPUPWINDOWS_YPOS + 200 - 1};
    static GUI_RECT g_sTitileRect = {ALARMPOPUPWINDOWS_XPOS, ALARMPOPUPWINDOWS_YPOS, ALARMPOPUPWINDOWS_XPOS + 300 - 1, ALARMPOPUPWINDOWS_YPOS + 46 - 1};
    static GUI_RECT g_sBottonRect = {ALARMPOPUPWINDOWS_XPOS + 2, ALARMPOPUPWINDOWS_YPOS + 149, ALARMPOPUPWINDOWS_XPOS + 2 * 2 + 146 * 2 - 1, ALARMPOPUPWINDOWS_YPOS + 149 + 48 - 1};
    static GUI_RECT g_PopUpRect = {ALARMPOPUPWINDOWS_XPOS, ALARMPOPUPWINDOWS_YPOS + 46, ALARMPOPUPWINDOWS_XPOS + 300 - 1, ALARMPOPUPWINDOWS_YPOS + 153 - 1};
#else
    static GUI_RECT g_sWindowsRect = {ALARMPOPUPWINDOWS_XPOS, ALARMPOPUPWINDOWS_YPOS, ALARMPOPUPWINDOWS_XPOS + 220, ALARMPOPUPWINDOWS_YPOS + 180};
    static GUI_RECT g_sTitileRect = {ALARMPOPUPWINDOWS_XPOS, ALARMPOPUPWINDOWS_YPOS + 20, ALARMPOPUPWINDOWS_XPOS + 220, ALARMPOPUPWINDOWS_YPOS + 40};
    static GUI_RECT g_sBottonRect = {ALARMPOPUPWINDOWS_XPOS + 2, ALARMPOPUPWINDOWS_YPOS + 149, ALARMPOPUPWINDOWS_XPOS + 216, ALARMPOPUPWINDOWS_YPOS + 176};
    static GUI_RECT g_PopUpRect = {ALARMPOPUPWINDOWS_XPOS, ALARMPOPUPWINDOWS_YPOS + 40, ALARMPOPUPWINDOWS_XPOS + 220, ALARMPOPUPWINDOWS_YPOS + 148};
#endif
    

void WifiConnectionStatusPopUpWindowProcess(U8 Key)
{
    switch (Key) 
    {
        case GUI_KEY_BACKTAB:            
        case GUI_KEY_TAB:              
            break;
        case GUI_KEY_ENTER:             
        case GUI_KEY_F1:
                ShowWifiConnectionStatusPopUpWindow(0);

                if (g_sU8WifiConnectionStatus == WIFI_CONNECTION_FAILED)
                {
                    //"����ʧ��",ѡ��"ȷ��"������SSID�������ý���
                    ShowWifiSSIDSettingScreen(1);
                }
                else if (g_sU8WifiConnectionStatus != WIFI_CONNECTION_SUCCEEDED)
                {
                    //"������",ѡ��"ȡ��"������wifi�б�����
                    g_ConfigSave.SetApNamePwd("", "");
                    g_WifiUartModule.SetModuleInfo("", "");
                }
                g_sU8RefreshWifiConnectionStatueCnt = 0;
            break;
        default:
            break;
    }    
}

static char* WifiSSIDName(void)
{
    char DotBuff[] = "...";
    
    memset(g_sUWifiSSIDNameAndDot, 0, sizeof(g_sUWifiSSIDNameAndDot));
    if (strlen(g_ConfigSave.GetWifiConfig()->SSID) > MAX_LEN_WIFISSID)
    {
        memcpy(g_sUWifiSSIDNameAndDot, g_ConfigSave.GetWifiConfig()->SSID, MAX_LEN_WIFISSID);
        strcat(g_sUWifiSSIDNameAndDot, DotBuff);
    }
    else
    {
        memcpy(g_sUWifiSSIDNameAndDot, g_ConfigSave.GetWifiConfig()->SSID, strlen(g_ConfigSave.GetWifiConfig()->SSID));
    }
    return g_sUWifiSSIDNameAndDot;
}

static void RefreshWifiConnectionStatusTimerTask(void)
{
    char str[WIFI_CONNECTION_STR_MAX_LEN];
    
	GUI_TIMER_Restart(g_RefreshWifiConnectionStatusTimer);
    //DEBUG_INFO("50 WifiConnectionStatusTimer GetConnectStatus = %d\n", g_WifiUartModule.GetConnectStatus());

    WM_SelectWindow(WM_HBKWIN);
    if (g_sU8RefreshWifiConnectionStatueCnt < 30)
        g_sU8RefreshWifiConnectionStatueCnt++;
    
    if (g_WifiUartModule.GetAPStatus())
    {
        if (g_sU8WifiConnectionStatus == WIFI_CONNECTION_SUCCEEDED)
            return;

        //wifi���ӳɹ�����ˢ������
        GUI_SetColor(DEFAULT_SYSRESET_BACKCOLOR);
        GUI_FillRect(g_PopUpRect.x0, g_PopUpRect.y0, g_PopUpRect.x1, g_PopUpRect.y1);
        GUI_SetColor(GUI_WHITE);
//        sprintf(str, "%s%s\n%s", GetMultiLanguageString(WIFI_CONNECTED_IDX), ParameterData->WifiConfig.SSID, GetMultiLanguageString(WIFI_CONNECTED_IDX));
//        GUI_DispStringInRect(str, &g_PopUpRect, GUI_TA_HCENTER | GUI_TA_VCENTER);

        //sprintf(str, "%s\n%s", g_sUWifiSSIDNameAndDot, GetMultiLanguageString(WIFI_CONNECTION_SUCCEEDED_IDX));
        //GUI_DispStringInRect(str, &g_PopUpRect, GUI_TA_HCENTER | GUI_TA_VCENTER);
        GUI_DispStringInRect(GetMultiLanguageString(WIFI_CONNECTION_SUCCEEDED_IDX), &g_PopUpRect, GUI_TA_HCENTER | GUI_TA_VCENTER);

        GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);//���ð��������ɫ

        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
            GUI_AA_FillRoundedRect(g_sBottonRect.x0, g_sBottonRect.y0, g_sBottonRect.x1, g_sBottonRect.y1, 20);
        #elif (LCD_TYPE == LCD_35_TFT)//�����ڴ��豸���ƣ����ڵ�һ�λ�����һ�λ������
            GUI_SetFont(FONT_24);
            GUI_AA_FillRoundedRect(g_sBottonRect.x0, g_sBottonRect.y0, g_sBottonRect.x1, g_sBottonRect.y1, 12);
        #else
            GUI_SetFont(FONT_24);
            GUI_AA_FillRoundedRect(g_sBottonRect.x0, g_sBottonRect.y0, g_sBottonRect.x1, g_sBottonRect.y1, 12);
        #endif

        //ȷ��
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(GetMultiLanguageString(OK_RESET_IDX), &g_sBottonRect, GUI_TA_HCENTER | GUI_TA_VCENTER);
        
        g_sU8WifiConnectionStatus = WIFI_CONNECTION_SUCCEEDED;  //���ӳɹ�

//        //ɾ����ʱˢ�¶�ʱ��
//        GUI_TIMER_Delete(g_RefreshWifiConnectionStatusTimer);
    }
    else if (g_WifiUartModule.GetAPStatus() == 0 && g_sU8RefreshWifiConnectionStatueCnt >= 30)
    {
        if (g_sU8WifiConnectionStatus == WIFI_CONNECTION_FAILED)
            return;        
        //30s,wifiδ���ӳɹ�����ˢ������"����ʧ�ܣ���������������"
        GUI_SetColor(DEFAULT_SYSRESET_BACKCOLOR);
        GUI_FillRect(g_PopUpRect.x0, g_PopUpRect.y0, g_PopUpRect.x1, g_PopUpRect.y1);
        GUI_SetColor(GUI_WHITE);
//        sprintf(str, "%s%s\n%s", GetMultiLanguageString(WIFI_DISCONNECTED_IDX), ParameterData->WifiConfig.SSID, GetMultiLanguageString(WIFI_DISCONNECTED_IDX));
//        GUI_DispStringInRect(str, &g_PopUpRect, GUI_TA_HCENTER | GUI_TA_VCENTER);

        //sprintf(str, "%s%s\n%s", g_sUWifiSSIDNameAndDot, GetMultiLanguageString(WIFI_CONNECTION_FAILED_IDX), GetMultiLanguageString(WIFI_ENTER_PASSWORD_IDX));
        sprintf(str, "%s\n%s", GetMultiLanguageString(WIFI_CONNECTION_FAILED_IDX), GetMultiLanguageString(WIFI_ENTER_PASSWORD_IDX));
        GUI_DispStringInRect(str, &g_PopUpRect, GUI_TA_HCENTER | GUI_TA_VCENTER);

        GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);//���ð��������ɫ

        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
            GUI_AA_FillRoundedRect(g_sBottonRect.x0, g_sBottonRect.y0, g_sBottonRect.x1, g_sBottonRect.y1, 20);
        #elif (LCD_TYPE == LCD_35_TFT)//�����ڴ��豸���ƣ����ڵ�һ�λ�����һ�λ������
            GUI_SetFont(FONT_24);
            GUI_AA_FillRoundedRect(g_sBottonRect.x0, g_sBottonRect.y0, g_sBottonRect.x1, g_sBottonRect.y1, 12);
        #else
            GUI_SetFont(FONT_24);
            GUI_AA_FillRoundedRect(g_sBottonRect.x0, g_sBottonRect.y0, g_sBottonRect.x1, g_sBottonRect.y1, 12);
        #endif

        //ȷ��
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(GetMultiLanguageString(OK_RESET_IDX), &g_sBottonRect, GUI_TA_HCENTER | GUI_TA_VCENTER);
        
        g_sU8WifiConnectionStatus = WIFI_CONNECTION_FAILED;  //����ʧ��
        
////        //ɾ����ʱˢ�¶�ʱ��
////        GUI_TIMER_Delete(g_RefreshWifiConnectionStatusTimer);
////        g_sU8RefreshWifiConnectionStatueCnt = 0;
    }
}

static void CreateWifiConnectionStatusTimer(void)
{	
	g_RefreshWifiConnectionStatusTimer = GUI_TIMER_Create((GUI_TIMER_CALLBACK *)RefreshWifiConnectionStatusTimerTask, 20000, 0, 0);
    GUI_TIMER_SetPeriod(g_RefreshWifiConnectionStatusTimer, (GUI_TIMER_TIME)(1000 / (1000 / OS_CFG_TICK_RATE_HZ)));
    g_sU8RefreshWifiConnectionStatueCnt = 0;
    GUI_TIMER_Restart(g_RefreshWifiConnectionStatusTimer);
}

void RepaintWifiConnectionStatusPopUpWindow(void)
{
    char str[WIFI_CONNECTION_STR_MAX_LEN];
    
    WM_SelectWindow(WM_HBKWIN);
    GUI_SetTextMode(GUI_TM_TRANS);  //���ñ���͸��
    GUI_SetColor(DEFAULT_SYSRESET_BACKCOLOR);
    
    g_sU8RefreshWifiConnectionStatueCnt = 0;
    
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_38);
        GUI_AA_FillRoundedRect(g_sWindowsRect.x0, g_sWindowsRect.y0, g_sWindowsRect.x1, g_sWindowsRect.y1, 20);
    #elif (LCD_TYPE == LCD_35_TFT)
        GUI_SetFont(FONT_32);
        GUI_FillRect(g_sWindowsRect.x0, g_sWindowsRect.y0, g_sWindowsRect.x1, g_sWindowsRect.y1);//�����ڴ��豸����Բ�Ǿ�������ʾ����
    #else
        GUI_SetFont(FONT_24);
        GUI_FillRect(g_sWindowsRect.x0, g_sWindowsRect.y0, g_sWindowsRect.x1, g_sWindowsRect.y1);
    #endif

    GUI_SetColor(GUI_WHITE);
    GUI_DispStringInRect(GetMultiLanguageString(WLANTITLE_IDX), &g_sTitileRect, GUI_TA_HCENTER | GUI_TA_VCENTER);

    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
    #else
        GUI_SetFont(FONT_24);
    #endif
//    GUI_DispStringInRect(GetMultiLanguageString(IdType_PopUpWindow[0][1]), &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);//���ñ����ɫ

    #if (LCD_TYPE == LCD_5_TFT)
        GUI_AA_FillRoundedRect(ALARMPOPUPWINDOWS_XPOS + 4, ALARMPOPUPWINDOWS_YPOS + 224, ALARMPOPUPWINDOWS_XPOS + 8 + 2 * 244 - 1, ALARMPOPUPWINDOWS_YPOS + 224 + 72 - 1, 20);
    #elif (LCD_TYPE == LCD_35_TFT)//�����ڴ��豸���ƣ����ڵ�һ�λ�����һ�λ������        
        GUI_SetFont(FONT_24);
        GUI_AA_FillRoundedRect(ALARMPOPUPWINDOWS_XPOS + 2, ALARMPOPUPWINDOWS_YPOS + 149, ALARMPOPUPWINDOWS_XPOS + 4 + 2 * 146  - 1, ALARMPOPUPWINDOWS_YPOS + 149 + 48 - 1, 12);
    #else
        GUI_AA_FillRoundedRect(ALARMPOPUPWINDOWS_XPOS + 2, ALARMPOPUPWINDOWS_YPOS + 149, ALARMPOPUPWINDOWS_XPOS + 216, ALARMPOPUPWINDOWS_YPOS + 176, 12);
    #endif
    GUI_SetColor(GUI_WHITE);
    
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
    #elif (LCD_TYPE == LCD_35_TFT)
        GUI_SetFont(FONT_24);
    #else
        GUI_SetFont(FONT_24);
    #endif
    if (g_sU8WifiConnectionStatus == WIFI_NONE)
    {
        g_sU8WifiConnectionStatus = WIFI_CONNECTING;    //�������ӣ���ȴ�...
        sprintf(str, "%s\n%s", GetMultiLanguageString(WIFI_CONNECTING_IDX), GetMultiLanguageString(WIFI_WAIT_IDX));
    }
    else if (g_sU8WifiConnectionStatus == WIFI_CONNECTING)
    {
        sprintf(str, "%s\n%s", GetMultiLanguageString(WIFI_CONNECTING_IDX), GetMultiLanguageString(WIFI_WAIT_IDX));
    }
    else if (g_sU8WifiConnectionStatus == WIFI_CONNECTION_SUCCEEDED)
    {
        sprintf(str, "%s", GetMultiLanguageString(WIFI_CONNECTION_SUCCEEDED_IDX));
    }
    else if (g_sU8WifiConnectionStatus == WIFI_CONNECTION_FAILED)
    {
        sprintf(str, "%s\n%s", GetMultiLanguageString(WIFI_CONNECTION_FAILED_IDX), GetMultiLanguageString(WIFI_ENTER_PASSWORD_IDX));
    }
    
    WifiSSIDName();

    GUI_DispStringInRect(str, &g_PopUpRect, GUI_TA_HCENTER | GUI_TA_VCENTER);
    
    if(g_sU8WifiConnectionStatus == WIFI_CONNECTION_SUCCEEDED || g_sU8WifiConnectionStatus == WIFI_CONNECTION_FAILED)
    {
        //ȷ��
        GUI_DispStringInRect(GetMultiLanguageString(OK_RESET_IDX), &g_sBottonRect, GUI_TA_HCENTER | GUI_TA_VCENTER);
    }
    else
    {
        //ȡ��
        GUI_DispStringInRect(GetMultiLanguageString(CANCEL_RESET_IDX), &g_sBottonRect, GUI_TA_HCENTER | GUI_TA_VCENTER);
    }
}

void ShowWifiConnectionStatusPopUpWindow(uint8_t Flag)
{       
    if (Flag)
    {
        CreateWifiConnectionStatusTimer();
//        RepaintWifiConnectionStatusPopUpWindow();
        g_sU8WifiConnectionStatus = WIFI_NONE;
        g_WindowDrv.PushWindow(&g_WifiConnectionStatusPopUpWindowWInfo);        
    }
    else
    {
        GUI_TIMER_Delete(g_RefreshWifiConnectionStatusTimer);
        g_WindowDrv.PopWindow(&g_WifiConnectionStatusPopUpWindowWInfo);
    }
    g_U8CurEditState_ForPowerON = Flag;	
}

