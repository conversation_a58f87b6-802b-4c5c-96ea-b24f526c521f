/***************************************************************
 * File Name:   AppBMP.h
 * Copyright@2019-2029,Beyond,All Right Reserved
 *
 * Description: this file is created by automatic tool, don't edit!
 *
****************************************************************/
#ifndef _APP_BMP_H_
#define _APP_BMP_H_

/********************************************************************
具体数据举例：
如某BMP文件开头：
424D 4690 0000 0000 0000 4600 0000 2800 0000 8000 0000 9000 0000 0100*1000 0300 0000 0090 0000 A00F 0000 A00F 0000 0000 0000 0000 0000*00F8 0000 E007 0000 1F00 0000 0000 0000*02F1 84F1 04F1 84F1 84F1 06F2 84F1 06F2 04F2 86F2 06F2 86F2 86F2 .... ....
BMP文件可分为四个部分：位图文件头、位图信息头、彩色板、图像数据阵列，在上图中已用*分隔。
一、图像文件头
1）1：(这里的数字代表的是"字",即两个字节,下同)图像文件头。424Dh=’BM’，表示是Windows支持的BMP格式。
2）2-3：整个文件大小。4690 0000，为00009046h=36934。
3）4-5：保留，必须设置为0。
4）6-7：从文件开始到位图数据之间的偏移量。4600 0000，为00000046h=70，上面的文件头就是35字=70字节。
5）8-9：位图图信息头长度。
6）10-11：位图宽度，以像素为单位。8000 0000，为00000080h=128。
7）12-13：位图高度，以像素为单位。9000 0000，为00000090h=144。
8）14：位图的位面数，该值总是1。0100，为0001h=1。
二、位图信息头
9）15：每个像素的位数。有1（单色），4（16色），8（256色），16（64K色，高彩色），24（16M色，真彩色），32（4096M色，增强型真彩色）。1000为0010h=16。
10）16-17：压缩说明：有0（不压缩），1（RLE 8，8位RLE压缩），2（RLE 4，4位RLE压缩，3（Bitfields，位域存放）。RLE简单地说是采用像素数+像素值的方式进行压缩。T408采用的是位域存放方式，用两个字节表示一个像素，位域分配为r5b6g5。图中0300 0000为00000003h=3。
11）18-19：用字节数表示的位图数据的大小，该数必须是4的倍数，数值上等于位图宽度×位图高度×每个像素位数。0090 0000为00009000h=80×90×2h=36864。
12）20-21：用象素/米表示的水平分辨率。A00F 0000为0000 0FA0h=4000。
13）22-23：用象素/米表示的垂直分辨率。A00F 0000为0000 0FA0h=4000。
14）24-25：位图使用的颜色索引数。设为0的话，则说明使用所有调色板项。
15）26-27：对图象显示有重要影响的颜色索引的数目。如果是0，表示都重要。
*************************************************************************/

#include "Typedef.h"
#include "Gui.h"

#pragma pack(push)
#pragma pack(1)
typedef  struct
{
    unsigned char       bfType[2];       //位图文件的类型，必须为BMP
    unsigned long       bfSize;       //文件大小，以字节为单位
    unsigned short int  bfReserverd1; //位图文件保留字，必须为0
    unsigned short int  bfReserverd2; //位图文件保留字，必须为0
    unsigned long       bfbfOffBits;  //位图文件头到数据的偏移量，以字节为单位
    long  bInfoSize;                   //位图图信息头长度，字节为单位
    long  biWidth;                     //图形宽度以象素为单位
    long  biHeight;                     //图形高度以象素为单位
} BmpInfoHead;
#pragma pack(pop)


typedef struct
{
    uint32_t startAddr;
    uint16_t xSize;
    uint16_t ySize;
} BmpSourceInfo;







void BmpInit(void);
BmpSourceInfo *GetBmpInfoHead(uint8_t bmpIndx);
int BmpReadData(void *pBmpIndx, const uint8_t **ppData, unsigned NumBytes, U32 Offset);


#endif