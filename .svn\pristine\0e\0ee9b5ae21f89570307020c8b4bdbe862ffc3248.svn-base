/******************************************************************************

                  版权所有 (C), 2013-2023, 长沙比扬医疗器械有限公司

 ******************************************************************************
  文 件 名   : rtc.h
  版 本 号   : 初稿
  作    者   : LL
  生成日期   : 2015年8月5日
  最近修改   :
  功能描述   : 实时时钟模块接口
  函数列表   :
  修改历史   :
  1.日    期   : 2015年8月5日
    作    者   : LL
    修改内容   : 创建文件

******************************************************************************/

#ifndef __BSP_RTC_DS1302_H__
#define __BSP_RTC_DS1302_H__

#include "DataType.h"
#include "main.h"

/*
 * @时间日期结构定义
 */

typedef struct
{
    int8_t   sec; //秒
    int8_t   minu; //分
    int8_t   hour; //时
    int8_t   day; //日
    int8_t   mon; //月
    int8_t   week; //周
    int16_t  year; //年
} RTC_TimeTypeDef1;

#define CENTURY     2000    //21世纪


// I2C1 IO-Pins                         /* -- adapt the defines for your uC -- */
// SCL on port D, bit 12			
#define DS1302_SCL_LOW()   HAL_GPIO_WritePin(RTC_CLK_GPIO_Port, RTC_CLK_Pin, GPIO_PIN_RESET) // set scl to low
#define DS1302_SCL_HIGH()  HAL_GPIO_WritePin(RTC_CLK_GPIO_Port, RTC_CLK_Pin, GPIO_PIN_SET) // set scl to high
#define DS1302_SCL_READ()  HAL_GPIO_ReadPin(RTC_CLK_GPIO_Port, RTC_CLK_Pin)     // read SDA

// SDA on port D, bit 13                /* -- adapt the defines for your uC -- */
#define DS1302_SDA_LOW()  HAL_GPIO_WritePin(RTC_SDA_GPIO_Port, RTC_SDA_Pin, GPIO_PIN_RESET) // set sda to low
#define DS1302_SDA_HIGH() HAL_GPIO_WritePin(RTC_SDA_GPIO_Port, RTC_SDA_Pin, GPIO_PIN_SET) // set sda to high

#define DS1302_SDA_READ() HAL_GPIO_ReadPin(RTC_SDA_GPIO_Port, RTC_SDA_Pin)      // read 




/*
 * @时钟不能正常启动时，系统默认给1302设置的初始日期为2011年1月1日，0时0分0秒，星期六
 */
#define DEFLAUT_YEAR    0x11
#define DEFLAUT_WEEK    0x06
#define DEFLAUT_MONTH   0x01
#define DEFLAUT_DAY     0x01
#define DEFLAUT_HOUR    0x00
#define DEFLAUT_MIN     0x00
#define DEFLAUT_SEC     0x00

/*
 * @DS1302常用寄存器地址
 */
#define YEAR_R      0x8D          //读年地址
#define YEAR_W      0x8C          //写年地址
#define WEEK_R      0x8B          //读周地址
#define WEEK_W      0x8A          //写周地址
#define MONTH_R     0x89          //读月地址
#define MONTH_W     0x88          //写月地址
#define DAY_R       0x87          //读日地址
#define DAY_W       0x86          //写日地址
#define HOUR_R      0x85          //读时地址
#define HOUR_W      0x84          //写时地址
#define MINU_R      0x83          //读分地址
#define MINU_W      0x82          //写分地址
#define SEC_R       0x81          //读秒地址
#define SEC_W       0x80          //写秒地址
#define WP_W        0x8E          //写保护位
#define CTR_W       0x90          //充电等控制
#define CTR_DATA    0xAB          //涓流充电，二个二极管，8K电阻

uint8_t ToBin(uint8_t dat);
void RTC_Write(uint8_t addr, uint8_t u8Dat);
void RTC_Init(void);
void RTC_SetTime(RTC_TimeTypeDef1 *RTC_Time);
void RTC_ReadTime(RTC_TimeTypeDef1 *RTC_Time);
bool RTC_RealReadTime(RTC_TimeTypeDef1 *RTC_Time);
uint8_t RTC_ReadData(uint8_t addr);
void RTC_WriteData(uint8_t addr, uint8_t* pData, uint8_t DataLen);
void ReadRTCTime(void);

extern RTC_TimeTypeDef1 g_SD1302RTCDateTime;

extern OS_MUTEX g_RtcClkMutex; 
#endif /* RTC_H */

