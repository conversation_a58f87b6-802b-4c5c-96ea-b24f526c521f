#include "AppStringLib.h"
#include "bsp_MX25L256.h"

#define LANGUAGE_LIB_NUM_ADDR       MX25_LANGUAGE_LIB_ADDR
#define LANGUAGE_LIB_INFO_HEAD      (LANGUAGE_LIB_NUM_ADDR + 4)

uint8_t gLanguageIndex = 0;//语种
LanguageInfo gLanguageInfo = {0};//语言信息

static char sStringBUff[256];//字符串缓冲

#define    LanguageNum    2

#if (Is_ExFlash==0)

static  char*LanguageName[LanguageNum]=
{
    "中文",
    "English",
};

#endif


const char *GetLanguageName(void)
{
    return gLanguageInfo.Name;
}

void InitLanguage(void)
{
    //获得用户设置的语种
    if(gLanguageIndex<GetLanguageLibNum())
    {
    }
    else
    {
        gLanguageIndex=0;
    }
    GetLanguageInfoByIndex(gLanguageIndex,&gLanguageInfo);
}

uint32_t GetLanguageLibNum(void)
{
    uint32_t num = 0;
    #if Is_ExFlash
    SPIFLASH_ReadBuff((uint8_t *)&num, LANGUAGE_LIB_NUM_ADDR, 4 );
    #else
    num=LanguageNum;
    #endif
    return num;
}

bool GetLanguageInfoByIndex(uint8_t index,LanguageInfo* info)
{
    memset(info, 0, sizeof(LanguageInfo));
    #if Is_ExFlash
    if (GetLanguageLibNum() > index)
    {
        SPIFLASH_ReadBuff((uint8_t *)info, LANGUAGE_LIB_INFO_HEAD+index*sizeof(LanguageInfo), sizeof(LanguageInfo) );
    }
    else
    {
        SPIFLASH_ReadBuff((uint8_t *)info, LANGUAGE_LIB_INFO_HEAD, sizeof(LanguageInfo) );
    }
    #else
    strcpy(info->Name,LanguageName[gLanguageIndex]);
    #endif
    return true;
}

void SetLanguage(uint8_t index)
{
    if(index>=GetLanguageLibNum())
    {
        index=0;
    }
    gLanguageIndex=index;
    GetLanguageInfoByIndex(gLanguageIndex,&gLanguageInfo);
}


