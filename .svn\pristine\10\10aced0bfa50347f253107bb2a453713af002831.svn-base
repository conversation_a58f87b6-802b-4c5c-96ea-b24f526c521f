#include "WifiUartModule.h"
#include "GlobalVariable.h"
#include "CalcDateTime.h"
#include <string.h>
#include "bsp_MX25L256.h"
#include "StaterBar.h"
#include "EdfDataSave.h"
#include "TaskResourceManager.h"

OS_TCB   AppTaskWifiUartTCB;
CPU_STK  AppTaskWifiUartStk[APP_CFG_TASK_WIFIUART_STK_SIZE];

U8 g_U8UartWifiRcvBuf[WIFI_ISR_RCV_LENGTH];

static char* m_ServerIP[] = 
{
    SERVER_IP_1,
    SERVER_IP_2,
    SERVER_IP_3,
};

static uint32_t m_ServerPort[] = 
{
    SERVER_PORT_1,
    SERVER_PORT_2,
    SERVER_PORT_3,
};

U8 get_server_type(void)
{
    return g_ConfigSave.GetServerType();
}

CWifiUartModule::CWifiUartModule()
{    

    m_u32WifiReinitTime = 0;
    m_bWaitingForTcpClose = false;
    m_u32WaitTcpCloseTimeout = 0;
    m_U8ReconnectCount = 0;
    m_U32ReconnectTime = 0;  
    m_pParameterData = NULL;
    m_pCurrWifiCmdPkg = NULL;
    m_NetDataSendBuf.U16InIdx = 0;
    m_NetDataSendBuf.U8BaseIdx = 0;
    m_NetDataHead.Head[0] = NET_PACKAGE_HEAD1;
    m_NetDataHead.Head[1] = NET_PACKAGE_HEAD2;
#if (NETWORD_PROTOCAL_VERSION)
    m_NetDataHead.ProtocolVersion = NETWORD_PROTOCAL_VERSION;
    m_PowerOnSendStatus = SEND_HANDSHAKE;
#endif
    m_U8SendIdx = 0;
    m_U8HaveUpdateApList = 0;
    m_U16OverTime = 0;
    m_U16HeartTicket = 0;
    m_U8ServerConnectStatus = DISCONNECTED; 
    m_U8APConnectStatus = DISCONNECTED; 
    m_WifiDataPkg.U16CmdLen = 0;    
    m_WifiDataPkg.U16RspWaitTime = 1000;
    
    m_U16AtRspLen = 0;
    
    m_SendType = SEND_REAL_DATA;
    m_U16SendEdfIdx = 0;
    
    strcpy(m_WifiInfo.FirmwareVersion, "0.0.0.0");
    m_WifiInfo.DHCPEnable = 1;   
    m_WifiInfo.WifiValid = 0;
    
    strcpy(m_CurrentApInfo.ApName, "");      //WIFI名称
    m_CurrentApInfo.U8SignalQuality = 2;       //信号质量
    m_CurrentApInfo.U8EncryptionType = 1;      //加密类型 0-无加密 

    m_ApList.ApNum = 0;
    m_TmpApList.ApNum = 0;
    for (U8 i = 0; i < MAX_AP_NUMBER; i++)
    {
        m_TmpApList.ApInfo[i].U8SignalQuality = 2;
        m_TmpApList.ApInfo[i].U8EncryptionType = 1;
    }
    m_U8RestoreFactoryFlag = 0;
    m_U8SendRealDataFlag = 1;

    // 初始化MTLS配置状态
    m_MTLSConfigState = MTLS_CONFIG_NONE;
    m_MTLSConfigTimer = 0;
}

CWifiUartModule::~CWifiUartModule()
{
}

void CWifiUartModule::RestoreDefault()
{
    const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
    if (pWifiConfig->Switch)
    {
        m_U8RestoreFactoryFlag = 1;
        IPStop();
        DisConnectAP();
    }
    m_WifiDataManager.RestoreWifiDataInfo();  
}

void CWifiUartModule::ConfigBaseInfo()
{
    m_U8RestoreFactoryFlag = 0;
    // 修改为使用GetWifiConfig()方法获取WIFICONFIG
    const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
    if (g_ConfigSave.GetParameter(WIFI_SWITCH) == FALSE || strlen(pWifiConfig->SSID) == 0)   //如果WIFI开关处于关闭状态，或者WIFI名称为空，则断开AP连接，避免自动连接之前配置的WIFI
    {
        IPStop();               //断开网络连接
        DisConnectAP();
    }
    else
    {
        ScanAP();
//        SetModuleInfo(m_pParameterData->WifiConfig.SSID, m_pParameterData->WifiConfig.PWD);
    }
    //QueryConnectStatus();    
}

PWIFIINFO CWifiUartModule::GetWifiInfo()
{
    return &m_WifiInfo;
}

void CWifiUartModule::Init(CConfigSave* pConfig, CEdfDataSave* pEdfDataSave)
{
    const MACHINECONFIG* pMachineConfig = pConfig->GetMachineConfig();
   
    m_pEdfDataSave = pEdfDataSave;
    
    m_pParameterData = pConfig->GetParameterConfig();
    m_pWifiConfig = pConfig->GetWifiConfig(); // 初始化WIFICONFIG指针
    
    m_DeviceInfo.Model = pMachineConfig->DType;
    m_DeviceInfo.ProNum = pMachineConfig->ProNum;
    m_DeviceInfo.ProduceYear = pMachineConfig->Year;
    m_DeviceInfo.ProduceMon = pMachineConfig->Month;
    m_DeviceInfo.ProductTailCode = pMachineConfig->TailCode;
#if (NETWORD_PROTOCAL_VERSION)    
    m_DeviceInfo.FlowSensorType = pMachineConfig->FlowSensorType;
    m_DeviceInfo.PressSensorType = pMachineConfig->PressSensorType;
#endif
    m_DeviceInfo.AppVersion = g_U32SoftVersion;
    m_DeviceInfo.FontLibVersion = g_U32SoftVersion;
    m_DeviceInfo.PicLibVersion = g_U32SoftVersion;
    m_DeviceInfo.Customized = 0;
    m_RealDataHead.Model = m_DeviceInfo.Model;
    m_RealDataHead.ProNum = m_DeviceInfo.ProNum;
    m_RealDataHead.ProduceYear = m_DeviceInfo.ProduceYear;
    m_RealDataHead.ProduceMon = m_DeviceInfo.ProduceMon;
    m_RealDataHead.ProductTailCode = m_DeviceInfo.ProductTailCode;
#if (NETWORD_PROTOCAL_VERSION)    
    m_RealDataHead.FlowSensorType = m_DeviceInfo.FlowSensorType;
    m_RealDataHead.PressSensorType = m_DeviceInfo.PressSensorType;
#endif
    m_NetPowerOnInfo.Model = m_NetPowerOffInfo.Model = m_DeviceInfo.Model;
    m_NetPowerOnInfo.ProNum = m_NetPowerOffInfo.ProNum = m_DeviceInfo.ProNum;
    m_NetPowerOnInfo.ProduceYear = m_NetPowerOffInfo.ProduceYear = m_DeviceInfo.ProduceYear;
    m_NetPowerOnInfo.ProduceMon = m_NetPowerOffInfo.ProduceMon = m_DeviceInfo.ProduceMon;
    m_NetPowerOnInfo.ProductTailCode = m_NetPowerOffInfo.ProductTailCode = m_DeviceInfo.ProductTailCode;
#if (NETWORD_PROTOCAL_VERSION)    
    m_NetPowerOnInfo.FlowSensorType = m_NetPowerOffInfo.FlowSensorType = m_DeviceInfo.FlowSensorType;
    m_NetPowerOnInfo.PressSensorType = m_NetPowerOffInfo.PressSensorType = m_DeviceInfo.PressSensorType;
#endif    

    QueryModuleFirmware();
    ConfigBaseInfo();

    m_WifiDataManager.Init();       
}

void CWifiUartModule::AnalyseTcpDatas()
{
    //+CIPEVENT：SOCKET,id,len,data
    char* spI8 = FindContext((char*)m_U8PackageBuffer + 2, m_U16Count, ",", 1);     //查找第一个','
    U32 DataLen;
    U32 i;
    if (spI8 == NULL)
    {
        return;
    }
    spI8 = FindContext(spI8 + 1, m_U16Count - ((U32)spI8 - (U32)m_U8PackageBuffer), ",", 1);     //查找第一个','
    if (spI8 == NULL)
    {
        return;
    }
    spI8++;
    DataLen = atoi(spI8);
    spI8 = strstr(spI8, ",");
    if (spI8 == NULL)
    {
        return;
    }
    spI8++;
    for (i = 0; i < DataLen; i++)
    {
        AnalyseTcpData((U8)(*spI8));
        spI8++;
    }
}

ESENDTYPE CWifiUartModule::GetSendType()
{
    return m_SendType;
}

void CWifiUartModule::DealSaveNetData()
{
#if (WIFI_NET_DEBUG == 1)
     g_SdCardDataSave.AddNetRcvData(m_U8TcpRcvPkgData, 10 + (((U16)m_U8TcpRcvPkgData[4] << 8) + m_U8TcpRcvPkgData[3]));
#endif    
       
#if (NETWORD_PROTOCAL_VERSION)
    if (m_U8TcpRcvPkgData[2] == RESPONSE_OK_CMD && m_U8TcpRcvPkgData[7] == HANDSHAKE_CMD)
    {
#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
        m_PowerOnSendStatus = SEND_PARAMETERCONFIG;      
#else
        m_PowerOnSendStatus = SEND_HEARTBEAT;
#endif
    }
#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
    else if (m_U8TcpRcvPkgData[2] == RESPONSE_OK_CMD && m_U8TcpRcvPkgData[7] == SENDPARAMETER_CMD)
    {
        m_PowerOnSendStatus = SEND_HEARTBEAT;
    }
#endif
#if (NETWORD_PROTOCAL_VERSION > 1)
#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
    else if (m_U8TcpRcvPkgData[2] == UPDATEPARAMETER_CMD)
    {
        m_SendType = SEND_RESPONSE;
        ChangeParameter((PPARAMETERCONFIG)(m_U8TcpRcvPkgData + 17), GetBldcmStatus() == MOTOR_STOP_STATE);
    }
#endif
    else if (m_U8TcpRcvPkgData[2] == RESPONSE_OK_CMD && m_U8TcpRcvPkgData[7] == POWERON_CMD)
    {
        m_U8SendRealDataFlag = m_U8TcpRcvPkgData[8];
    }
#endif        
    if (m_PowerOnSendStatus == SEND_HANDSHAKE)
    {
        return;
    }
#if (NETWORD_PROTOCAL_VERSION < 2)
    if (m_WifiDataManager.DelHaveSendData(m_U8TcpRcvPkgData[7], m_U8ReadBuf, &m_U16ReadBufLength))      //补发关机包
    {
        ReissuePowerOffData();
    }
    else
    {
        SendNetData(m_U8ReadBuf, m_U16ReadBufLength);
    }
#else  
    switch (m_SendType)
    {
        case SEND_REAL_DATA:
            if (m_WifiDataManager.DelHaveSendData(m_U8TcpRcvPkgData[7], m_U8ReadBuf, &m_U16ReadBufLength))      //补发关机包
            {
                ReissuePowerOffData();
            }
            else
            {
                if (m_U8TcpRcvPkgData[7] == POWEROFF_CMD || m_U8TcpRcvPkgData[7] == REISSUEPOWEROFF_CMD)       //当收到正常关机包回复时，驱动发送当前1组EDF数据
                {
                    m_SendType = SEND_EDF_DATA;                    
                    //近一年统计数据>1条以上，则发送2条，避免在由于跨天时，发送是当天数据，数据全为0
                    m_U16SendEdfIdx = (m_pEdfDataSave->GetLastYearNumberOfRecords() > 1) ? 2 : 1;
                }
                else
                {
                    if (m_U16ReadBufLength == 0)
                    {
                        m_U16SendEdfIdx = m_pEdfDataSave->GetEdfAffectedOfRecords();
                        m_SendType = SEND_EDF_DATA;
                    }
                    else
                    {
                        SendNetData(m_U8ReadBuf, m_U16ReadBufLength);
                    }
                }
            }            
            break;
        case SEND_EDF_DATA:
            if (m_U8TcpRcvPkgData[7] == EDF_CMD)
            {
                if (m_U16SendEdfIdx > 0)
                {
                    if (m_U16SendEdfIdx >= m_U8SendEdfRecords)
                    {
                        m_U16SendEdfIdx -= m_U8SendEdfRecords;
                    }
                    else
                    {
                        m_U16SendEdfIdx = 0;
                    }
                }
                if (m_U16SendEdfIdx == 0)
                {
                    m_pEdfDataSave->ClearAffectedOfRecords();
                    m_SendType = SEND_REAL_DATA;
                }
                else
                {
                    SendEdfInfo();
                }
            }
            else if (m_U16SendEdfIdx)
            {
                SendEdfInfo();
            }
            else
            {
                m_SendType = SEND_REAL_DATA;
            }
            break;
#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
        case SEND_RESPONSE:
            SendResponseInfo();
            m_SendType = SEND_REAL_DATA;
            break;
#endif
        default:
            m_SendType = SEND_REAL_DATA;
            break;
    }
#endif    
#else
    if (m_WifiDataManager.DelHaveSendData(m_U8TcpRcvPkgData[6], m_U8ReadBuf, &m_U16ReadBufLength))      //补发关机包
    {
        ReissuePowerOffData();
    }
    else
    {
        SendNetData(m_U8ReadBuf, m_U16ReadBufLength);
    }
#endif
}

void CWifiUartModule::ProcessTcpPack()
{
    m_U16HeartTicket = 0;
    m_U16OverTime = 0;
    m_U32ReconnectTime = 0; 
    m_U8ReconnectCount = 0;
       
#if (NETWORK_REAL_SEND == 0)       
    DealSaveNetData();
#else
    if (m_U8TcpRcvPkgData[6] == POWERON_CMD || m_U8TcpRcvPkgData[6] == REISSUEPOWEROFF_CMD || m_U8TcpRcvPkgData[6] == REALDATAEVENT_CMD)
    {
        m_U16ReadBufLength = 0;
    }
    else if (m_U16ReadBufLength)
    {
        SendNetData(m_U8ReadBuf, m_U16ReadBufLength);
    }
#endif    
}

void CWifiUartModule::AddTcpPackageData(U8 Data)
{
    if (m_U16TcpDataInIdx < MAX_TCP_PACKAGE_LEN)
    {
        m_U8TcpRcvPkgData[m_U16TcpDataInIdx] = Data;
        m_U16TcpDataInIdx++;
    }
}

void CWifiUartModule::AnalyseTcpData(U8 Data)
{
    U8 PkgTail[4] = {NET_PACKAGE_TAIL1, NET_PACKAGE_TAIL2, NET_PACKAGE_TAIL3, NET_PACKAGE_TAIL4};
	U8 sU8Flag = 1;

	while (sU8Flag)
	{
		sU8Flag = 0;
        switch (m_U8TcpAnalyseStep)
        {
            case 0: //0x77:
                if (Data == NET_PACKAGE_HEAD1)
                {
                    m_U16TcpDataInIdx = 0;
                    m_U8TcpAnalyseStep++;
                    AddTcpPackageData(Data);
                }
                break;
            case 1://0x66:
                if (Data == NET_PACKAGE_HEAD2)
                {
                    m_U8TcpAnalyseStep++;
                    AddTcpPackageData(Data);
                }
                else
                {
                    m_U8TcpAnalyseStep = 0;
                    sU8Flag = 1;
                }
                break;
            case 2://0x07:
#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
                if (Data == RESPONSE_OK_CMD || Data == RESPONSE_ERR_CMD || Data == UPDATEPARAMETER_CMD)
#else
                if (Data == RESPONSE_OK_CMD || Data == RESPONSE_ERR_CMD)
#endif
                {
                    m_U8TcpAnalyseStep++;
                    AddTcpPackageData(Data);
                }
                else
                {
                    m_U8TcpAnalyseStep = 0;
                    sU8Flag = 1;
                }
                break;
            case 3:
#if (NETWORD_PROTOCAL_VERSION <= 1)
                m_U16ParameterLen = ((U16)Data) << 8;
#endif
#if (NETWORD_PROTOCAL_VERSION > 1)
                m_U16ParameterLen = Data;
#endif
                m_U8TcpAnalyseStep++;
                AddTcpPackageData(Data);
                break;
            case 4:
#if (NETWORD_PROTOCAL_VERSION <= 1)
                m_U16ParameterLen |= Data;
#endif
#if (NETWORD_PROTOCAL_VERSION > 1)
                m_U16ParameterLen |= (((U16)Data) << 8);
#endif
                m_U8TcpAnalyseStep++;
                AddTcpPackageData(Data);
                break;
#if (NETWORD_PROTOCAL_VERSION <= 1)
            case 5:
                m_U8TcpAnalyseStep++;
                AddTcpPackageData(Data);
                break;
            case 6:
                AddTcpPackageData(Data);
                if (--m_U16ParameterLen == 0)
                {
                    m_U8TcpAnalyseStep++;
                }
                break;
            case 7:
                AddTcpPackageData(Data);
                m_U8TcpAnalyseStep++;
                break;
            case 8:
            case 9:
            case 10:
            case 11:
                if (Data == PkgTail[m_U8TcpAnalyseStep - 8])
                {
                    if (m_U8TcpAnalyseStep == 11)
                    {
                        m_U8TcpAnalyseStep = 0;
                        //Get Invalid Package
                        ProcessTcpPack();
                    }
                    else
                    {
                        m_U8TcpAnalyseStep++;
                    }
                }
                else
                {
                    m_U8TcpAnalyseStep = 0;
                    sU8Flag = 1;
                }
                break;
#else
            case 5:
                AddTcpPackageData(Data);
                if (--m_U16ParameterLen == 0)
                {
                    m_U8TcpAnalyseStep++;
                }
                break;
            case 6:
                AddTcpPackageData(Data);
                m_U8TcpAnalyseStep++;
                break;
            case 7:
            case 8:
            case 9:
            case 10:
                if (Data == PkgTail[m_U8TcpAnalyseStep - 7])
                {
                    AddTcpPackageData(Data);
                    if (m_U8TcpAnalyseStep == 10)
                    {
                        m_U32RightPkgCnt++;
                        m_U8TcpAnalyseStep = 0;
                        //Get Invalid Package
                        ProcessTcpPack();
                    }
                    else
                    {
                        m_U8TcpAnalyseStep++;
                    }
                }
                else
                {
                    m_U32ErrorPkgCnt++;
                    m_U8TcpAnalyseStep = 0;
                    sU8Flag = 1;
                }
                break;
#endif            
            default:
                m_U8TcpAnalyseStep = 0;
                sU8Flag = 1;
                break;
        }        
    }    
}

//查询射频固件版本号
void CWifiUartModule::QueryModuleFirmware()
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+WFVER?\r");
    WifiCmdPkg.U16CmdType = QUERY_FIRMWARE_VERSION;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);    
}

void CWifiUartModule::SetEcho(U8 Flag)     //设置回显
{
    WIFICMDPKG WifiCmdPkg;
    sprintf((char*)WifiCmdPkg.Pkg, "AT+UARTE=%s\r", Flag ? "ON" : "OFF");    
    WifiCmdPkg.U16CmdType = SET_ECHO;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);    
}

//进入或退出透传模式
void CWifiUartModule::EnterTransparentTransmission(U8 Flag)
{
    //AT+CIPSENDRAW\r 进入透传模式    OK
    //+++ 退出透传模式，进入AT指令模式
    
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+CIPSENDRAW\r");
    WifiCmdPkg.U16CmdType = TRANSPARENT_TRANSMISSION;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);
}

void CWifiUartModule::QueryAP()
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+WJAP=?\r");
    WifiCmdPkg.U16CmdType = QUERY_AP;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);        
}

void CWifiUartModule::ConnectAP(const char* pSSID, const char* pPWD)
{
    //AT+WJAP=SWYANG,yangbatian2015\r
    WIFICMDPKG WifiCmdPkg;
    sprintf((char*)WifiCmdPkg.Pkg, "AT+WJAP=%s,%s\r", pSSID, pPWD);
    WifiCmdPkg.U16CmdType = CONNECT_AP;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 5000;
    PushCmdPkg(&WifiCmdPkg);  

    for (U8 i = 0; i < m_ApList.ApNum; i++)
    {
        if (strcmp(pSSID, m_ApList.ApInfo[i].ApName) == 0)
        {
            memcpy(&m_CurrentApInfo, &m_ApList.ApInfo[i], sizeof(m_CurrentApInfo));
            break;
        }
    }
}

void CWifiUartModule::DisConnectAP()
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+WJAPQ\r");
    WifiCmdPkg.U16CmdType = DISCONNECT_AP;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);
}

void CWifiUartModule::SetScanOPT(U8 Flag)
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+WSCANOPT=1\r");
    WifiCmdPkg.U16CmdType = SET_SCAN_OPT;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);      
}

//查询连接状态
void CWifiUartModule::QueryConnectStatus()
{
    //AT+WJAPS\r     //+WJAPS:CONNECTED\r\nOK
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+WJAPS\r");
    WifiCmdPkg.U16CmdType = QUERY_CONNECT_STATUS;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 3000;
    PushCmdPkg(&WifiCmdPkg);        
}

//查询IP地址
void CWifiUartModule::QueryIPAddr()
{
    //AT+WJAPIP?\r     +WJAPIP:*************,*************,************ 
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+WJAPIP?\r");
    WifiCmdPkg.U16CmdType = QUERY_IP_ADDR;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 3000;
    PushCmdPkg(&WifiCmdPkg);      
}

void CWifiUartModule::QueryIDStatus()
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+CIPSTATUS=1\r");
    WifiCmdPkg.U16CmdType = QUERY_ID_STATUS;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);            
}

void CWifiUartModule::QueryDHCPEnable() 
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+WDHCP?\r");
    WifiCmdPkg.U16CmdType = QUERY_DHCP_ENABLE;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 3000;
    PushCmdPkg(&WifiCmdPkg);        
}

void CWifiUartModule::EnableDHCP()
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+WDHCP=ON\r");
    WifiCmdPkg.U16CmdType = ENABLE_DHCP;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);    
}

void CWifiUartModule::IPStop()
{
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+CIPSTOP=1\r");
    WifiCmdPkg.U16CmdType = IP_STOP;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);    
}

//设置模块做 tcp client 的参数
void CWifiUartModule::SetServerInfo(char* IP, U32 Port, bool useSSL)
{
    //AT+CIPSTART=1,tcp_client,*********,20001\r        //+CIPEVENT:1,SEVER,CONNECTED
    WIFICMDPKG WifiCmdPkg;
    //strcpy((char*)WifiCmdPkg.Pkg, "AT+CIPSTART=1,tcp_client,************,20001\r");
    if (useSSL)
    {
        // 使用SSL连接
        sprintf((char*)WifiCmdPkg.Pkg, "AT+CIPSTART=1,ssl_client,%s,%d\r", IP, Port);
    }
    else
    {
        // 使用普通TCP连接
    sprintf((char*)WifiCmdPkg.Pkg, "AT+CIPSTART=1,tcp_client,%s,%d\r", IP, Port);
    }
    WifiCmdPkg.U16CmdType = IP_OPEN;
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16RspWaitTime = 3000;
    PushCmdPkg(&WifiCmdPkg);        
}

void CWifiUartModule::ScanAP()
{
    SetScanOPT(1);          //设置扫描方式为高级模式
    
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, "AT+WSCAN\r");
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16CmdType = SCAN_AP;
    WifiCmdPkg.U16RspWaitTime = 20000;
    PushCmdPkg(&WifiCmdPkg); 
    m_U8HaveUpdateApList = 0;
}

void CWifiUartModule::AddRealDataHead()
{
    U16 sU16Length;
    RTC_TimeTypeDef1 RTC_Time;    
    RTC_ReadTime(&RTC_Time);    
    m_NetDataHead.Cmd = REALDATAEVENT_CMD;
    sU16Length = 1 + sizeof(REALDATAHEAD);
#if (NETWORD_PROTOCAL_VERSION <= 1)    
    m_NetDataHead.Len[0] = (sU16Length >> 8) & 0xff;
    m_NetDataHead.Len[1] = (sU16Length & 0xff);    
#else
    m_NetDataHead.Len[0] = (sU16Length & 0xff);
    m_NetDataHead.Len[1] = ((sU16Length >> 8) & 0xff);    
#endif    
    m_NetDataHead.Index = m_U8SendIdx++;
    memcpy(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx], &m_NetDataHead, sizeof(NETDATAHEAD));
       
    m_RealDataHead.DataSerialID += 1;
    m_RealDataHead.DateTime.Year = RTC_Time.year - CENTURY;
    m_RealDataHead.DateTime.Mon = RTC_Time.mon;
    m_RealDataHead.DateTime.Day = RTC_Time.day;
    m_RealDataHead.DateTime.Hour = RTC_Time.hour;
    m_RealDataHead.DateTime.Min = RTC_Time.minu;
    m_RealDataHead.DateTime.Sec = RTC_Time.sec;
    
    memcpy(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx] + sizeof(NETDATAHEAD), &m_RealDataHead, sizeof(REALDATAHEAD));   
    m_NetDataSendBuf.U16InIdx = sizeof(NETDATAHEAD) + sizeof(REALDATAHEAD);    
}

void CWifiUartModule::ClearRealData()
{
    m_NetDataSendBuf.U8BaseIdx = 0;
    m_NetDataSendBuf.U16InIdx = 0;
}

void CWifiUartModule::AddRealData(U8* pData, U8 DataLen)
{
    PUREALTIMEDATA pRealData = (PUREALTIMEDATA)pData;
    if (m_WifiDataManager.GetDataSaveFlag() == 0)
    {
        return;
    }
    //如果发送实时标志为0，则不发送实时数据与压力基值与漏气量曲线，实时发送标志通过握手指令回复更新
    #if (SDCARD_PROTOCAL == 1)
    if (m_U8SendRealDataFlag == 0 
        && ((pRealData->BasicData.DataType >= ID1_REALTIME ) 
            || (pRealData->BasicData.DataType == BASEPRESS_LOGEVENT) 
            || (pRealData->BasicData.DataType == FLOWLEAK_LOGEVENT)))
    {
        return;
    }
    #else
    if (m_U8SendRealDataFlag == 0 
        && ((pRealData->BasicData.DataType >= ID1_REALTIME && pRealData->BasicData.DataType <= BPM_AND_MV_REALTIME) 
            || (pRealData->BasicData.DataType == BASEPRESS_LOGEVENT) 
            || (pRealData->BasicData.DataType == FLOWLEAK_LOGEVENT)))
    {
        return;
    }
    #endif

    memcpy(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx] + m_NetDataSendBuf.U16InIdx, pData, DataLen);
    m_NetDataSendBuf.U16InIdx += DataLen;
    if (m_NetDataSendBuf.U16InIdx >= 1000)
    {
#if (NETWORD_PROTOCAL_VERSION > 1)
        m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx][3] = ((m_NetDataSendBuf.U16InIdx - 5) & 0xff);       //+5为包尾，-10(包头+包尾-Index)
        m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx][4] = ((m_NetDataSendBuf.U16InIdx - 5) >> 8);
#else
        m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx][3] = ((m_NetDataSendBuf.U16InIdx - 5) >> 8);       //+5为包尾，-10(包头+包尾-Index)
        m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx][4] = ((m_NetDataSendBuf.U16InIdx - 5) & 0xff);
#endif        
        AddNetPkgTail(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx] + NET_DATA_BEGIN_IDX, m_NetDataSendBuf.U16InIdx - NET_DATA_BEGIN_IDX);
        m_NetDataSendBuf.U16InIdx = 0;
        m_NetDataSendBuf.U8BaseIdx ^= 1;
        AddRealDataHead();
       	g_SdCardDataSave.PostAddDataEvent(WIFI_REALDATA_SAVE);
    }
}

void CWifiUartModule::SaveWifiDataInfo()
{
#if (NETWORK_REAL_SEND == 0)       
    m_WifiDataManager.SaveWifiDataInfo();
#endif    
}

void CWifiUartModule::SaveWifiRealData()
{    
#if (NETWORK_REAL_SEND == 0)    
    m_WifiDataManager.SaveNetPackage(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx ^ 1], 1024);
#else
    memcpy(m_U8ReadBuf, m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx ^ 1], 1024);
    m_U16ReadBufLength = 1024;
#endif    
}

void CWifiUartModule::SaveWifiPowerOnData()
{
#if (NETWORK_REAL_SEND == 0)    
    m_WifiDataManager.SaveNetPackage(m_U8PowerOnOffPkgBuf, 256, 1);
#else    
    memcpy(m_U8ReadBuf, m_U8PowerOnOffPkgBuf, 256);
    m_U16ReadBufLength = 256;
#endif    
}

void CWifiUartModule::SaveWifiPowerOffData()
{
#if (NETWORK_REAL_SEND == 0)        
    m_WifiDataManager.SaveNetPackage(m_U8PowerOnOffPkgBuf, 32, 2);
#else   
    memcpy(m_U8ReadBuf, m_U8PowerOnOffPkgBuf, 32);
    m_U16ReadBufLength = 32;
#endif    
}

void CWifiUartModule::SendNetData(U8* pData, U16 DataLen)
{
    //AT+CIPSEND=1,5\r12345
    if (DataLen >= MAX_SEND_DATA_LEN || DataLen == 0)
    {
        assert_param("Wifi Send Data Len too long or too short.");
        return;
    }

    U16 DataIdx = 0;
    sprintf((char*)m_WifiDataPkg.SendBuf, "AT+CIPSEND=1,%d\r\n", DataLen);
    DataIdx = strlen((char*)m_WifiDataPkg.SendBuf) - 1;
    memcpy((char*)m_WifiDataPkg.SendBuf + DataIdx, pData, DataLen);  
    m_WifiDataPkg.U16CmdLen = DataIdx + DataLen;

    if (m_pCurrWifiCmdPkg == NULL)
    {
        HAL_UART_Transmit(&huart1, (uint8_t*)m_WifiDataPkg.SendBuf, m_WifiDataPkg.U16CmdLen, 5000);
        
#if (WIFI_NET_DEBUG == 1)
        g_SdCardDataSave.AddNetSendData(m_WifiDataPkg.SendBuf + DataIdx, DataLen);
#endif        
/*        switch (pData[2])
        {
            case 0x02:                
                printf("Send Power On!\r\n");
                break;
            case 0x03:
                printf("R Send Power Off!\r\n");
                break;
//            case 0x04:
//                printf("HeartBeat!\r\n");
//                break;
            case 0x05:
                printf("Send Power Off!\r\n");
                break;
            case 0x10:
                printf("Send Real Data On!\r\n");
                break;
        }*/
        //HAL_UART_Transmit(&huart3, (uint8_t*)(m_WifiDataPkg.SendBuf + DataIdx), DataLen, 5000); 
    }
}

void CWifiUartModule::ReissuePowerOffData()
{
#if (NETWORD_PROTOCAL_VERSION)  
#if (NETWORD_PROTOCAL_VERSION == 1)    
    U8 sU8DataPkg[32] = {NET_PACKAGE_HEAD1, NET_PACKAGE_HEAD2, REISSUEPOWEROFF_CMD, 0x00, 0x0c, NETWORD_PROTOCAL_VERSION};
#else
    U8 sU8DataPkg[32] = {NET_PACKAGE_HEAD1, NET_PACKAGE_HEAD2, REISSUEPOWEROFF_CMD, 0x0c, 0x00, NETWORD_PROTOCAL_VERSION};
#endif    
    U16 sU16Length = 0x0c;

    sU8DataPkg[6] = m_U8SendIdx++;
    memcpy(&sU8DataPkg[7], &m_DeviceInfo, 10);
#else
    U8 sU8DataPkg[32] = {NET_PACKAGE_HEAD1, NET_PACKAGE_HEAD2, REISSUEPOWEROFF_CMD, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, NET_PACKAGE_TAIL1, NET_PACKAGE_TAIL2, NET_PACKAGE_TAIL3, NET_PACKAGE_TAIL4};
   
    sU8DataPkg[3] = 0x00;    
    sU8DataPkg[4] = 7;
    sU8DataPkg[5] = m_U8SendIdx++;
    memcpy(&sU8DataPkg[6], &m_DeviceInfo, 6);
#endif
    AddNetPkgTail(sU8DataPkg + NET_DATA_BEGIN_IDX, sU16Length);            
    SendNetData(sU8DataPkg, sU16Length + NET_PACKHEAD_PACKEND_LEN);
}

//2. 若模块端为非自动接收至串口，即：AT+CIPRECVCFG设置为0,若模块端为自动接收至串口AT+CIPRECVCFG设置为1
void CWifiUartModule::AutoRcvConfig(U8 Flag)
{
    //AT+CIPRECV=1\r
    WIFICMDPKG WifiCmdPkg;
    strcpy((char*)WifiCmdPkg.Pkg, Flag ? "AT+CIPRECV=ON\r" : "AT+CIPRECV=OFF\r");
    WifiCmdPkg.U16CmdLen = strlen((char*)WifiCmdPkg.Pkg);
    WifiCmdPkg.U16CmdType = AUTO_RCV_CONFIG;
    WifiCmdPkg.U16RspWaitTime = 1000;
    PushCmdPkg(&WifiCmdPkg);      
}

void CWifiUartModule::SetModuleInfo(const char* pSSID, const char* pPWD)
{
    UpdateAPConnectStatus(0);
    UpdateServerConnectStatus(0);

    if (GetWifiCmdPkgListFree() < 6)
    {
        return;
    }
    m_PowerOnSendStatus = SEND_HANDSHAKE;   //连接WIFI，重新进入握手状态
    //QueryIDStatus();
    IPStop();               //断开网络连接
    SetEcho(0);             //设置回显不显示
    EnableDHCP();           //使能DHCP
    DisConnectAP();         //断开当前AP连接
    ConnectAP(pSSID, pPWD); //重新连接AP
//    QueryAP();
//    QueryIDStatus();
    // 配置MTLS连接，在AP连接建立后进行
    // 注意：这里不直接调用ConfigureMTLSConnection函数，而是设置状态标志
    // 在RunTask中根据状态标志进行MTLS配置
    m_MTLSConfigState = MTLS_CONFIG_INIT;
//    SetServerInfo(m_ServerIP[get_server_type()], m_ServerPort[get_server_type()], false);    //设置服务器信息

}

/*
+WSCAN:19
,-42
呼吸机,-42
TP-LINK_1F60,-45
CMCC-QfKT,-60
TP-LINK_DDDC,-60
TP-LINK_326F,-65
TP-LINK_2.4G_B9BFD3,-71
iTV-E0Ew,-71
beyond-dwx,-73
509,-74
BEYOND WIFI,-76
beyond-wifi,-76
aaafgb,-76
DESKTOP-KDID830 5238,-77
DIRECT-85-HP M427 LaserJet,-78
ChinaNet-E0Ew,-78
BEYOND WIFI,-82
xiaomi,-85
BYOND_PUMP_8702,-88

OK
*/

U8 CWifiUartModule::GetUpdateApListStatus()
{
    if (m_U8HaveUpdateApList)
    {
        memcpy(&m_ApList, &m_TmpApList, sizeof(m_TmpApList));
    }
    return m_U8HaveUpdateApList;
}

void CWifiUartModule::SetApListStatus(U8 Flag)
{
    m_U8HaveUpdateApList = Flag;
}

PAPLIST CWifiUartModule::GetAPList()
{
    return &m_ApList;
}

PAPINFO CWifiUartModule::GetCurrentApInfo()
{
    return &m_CurrentApInfo;
}

PAPLIST CWifiUartModule::GetLastAPList()
{
    return &m_TmpApList;
}

void CWifiUartModule::UpdateAPList()
{
    U8 i, j, sU8HaveAddFlag = 0;
    U32 sU32ApNum = 0;
    U32 sU32WifiNameLen = 0;
    I8 sI8SignalQuality = 0;
    char* psI8Buf1 = FindContext((char*)m_U8PackageBuffer, m_U16Count, "+WSCAN:", strlen("+WSCAN:"));
    char* psI8Buf2;
    char* psI8Buf3;
    char* psI8SSID;
    char* psI8EncryptionType;       //加密类型
    char* psI8Channel;              //信道
    char* psI8SignalQuality;        //信号强度    

    if (psI8Buf1 == NULL)
    {
        return;        
    }
    sU32ApNum = atoi(psI8Buf1 + 7);
    if (sU32ApNum == 0)
    {
        return;
    }
    psI8Buf1 = strstr(psI8Buf1, "\r\n");
    if (psI8Buf1 == NULL)
    {
        return;
    }
    
    m_TmpApList.ApNum = 0;

    psI8Buf1 += 2;
    for (i = 0; i < sU32ApNum; i++)
    {
        psI8Buf2 = strstr(psI8Buf1, ",");
        if (psI8Buf2 == NULL)
        {
            break;
        }
        sU32WifiNameLen = psI8Buf2 - psI8Buf1;      //获取AP名称长度
        
        psI8Buf3 = strstr(psI8Buf2, "\r\n");        
        if (psI8Buf3 == NULL)
        {
            break;
        }        
        psI8SSID = psI8Buf2 + 1;
        
        psI8EncryptionType = strstr(psI8SSID, ",") + 1;           //加密类型
        psI8Channel = strstr(psI8EncryptionType, ",") + 1;        //信道
        psI8SignalQuality = strstr(psI8Channel, ",") + 1;  //信号质量
        
        sI8SignalQuality = atoi(psI8SignalQuality);     //获取AP信号质量
        
        sU8HaveAddFlag = 0;
        if (sU32WifiNameLen < 32 && sU32WifiNameLen > 0)        //WIFI名称长度必须<=31并且>0
        {
            if (sI8SignalQuality >= -52)
            {
                sI8SignalQuality = 4;
            }
            else if (sI8SignalQuality >= -70)
            {
                sI8SignalQuality = 3;
            }
            else if (sI8SignalQuality >= -80)
            {
                sI8SignalQuality = 2;
            }
            else
            {
                sI8SignalQuality = 1;
            }             
            for (j = 0; j < m_TmpApList.ApNum; j++)
            {
                if (memcmp(m_TmpApList.ApInfo[j].ApName, psI8Buf1, sU32WifiNameLen) == 0)    //当扫描列表中存在多个同名的WIFI SSID时，信号强度取大值
                {
                    sU8HaveAddFlag = 1;
                    if (sI8SignalQuality > m_TmpApList.ApInfo[j].U8SignalQuality)
                    {
                        m_TmpApList.ApInfo[j].U8SignalQuality = sI8SignalQuality;                        
                    }
                    break;
                }
            }
            if (sU8HaveAddFlag == 0)
            {
                memcpy(m_TmpApList.ApInfo[m_TmpApList.ApNum].ApName, psI8Buf1, sU32WifiNameLen);
                m_TmpApList.ApInfo[m_TmpApList.ApNum].ApName[sU32WifiNameLen] = 0;                        
                m_TmpApList.ApInfo[m_TmpApList.ApNum].U8EncryptionType = (atoi(psI8EncryptionType) > 0) ? 1 : 0;
                
//                if (psI8Buf1[0] == 0xE5 && psI8Buf1[1] == 0x91)     //测试全部变为未加密，这个未复现，后续还需确认
//                {
//                    if (m_TmpApList.ApInfo[m_TmpApList.ApNum].U8EncryptionType == 0)
//                    {
//                        m_TmpApList.ApInfo[m_TmpApList.ApNum].U8EncryptionType = 0;
//                    }
//                }
                m_TmpApList.ApInfo[m_TmpApList.ApNum].U8SignalQuality = sI8SignalQuality;
                if (strcmp(m_pWifiConfig->SSID, m_TmpApList.ApInfo[m_TmpApList.ApNum].ApName) == 0)
                {
                    memcpy(&m_CurrentApInfo, &m_TmpApList.ApInfo[m_TmpApList.ApNum], sizeof(m_CurrentApInfo));
                }
                m_TmpApList.ApNum++;
                if (m_TmpApList.ApNum >= MAX_AP_NUMBER)
                {                
                    break;
                }
            }
        }
        psI8Buf1 = psI8Buf3 + 2;
    }
    if (m_TmpApList.ApNum > 0)
    {
        UpdateIconStatus(WIFI_COMM_STATE_INDICATION_BIT, m_U8APConnectStatus); 
        m_U8HaveUpdateApList = 1;
    }
}

void CWifiUartModule::UpdateServerConnectStatus(U8 Flag)
{
    if (m_U8ServerConnectStatus == Flag)
    {
        return;
    }
    if (Flag)
    {
        //HAL_UART_Transmit(&huart3, (uint8_t*)"connect", 10, 1000);       
        m_U8ServerConnectStatus = CONNECTED;
        UpdateIconStatus(WIFI_COMM_STATE_INDICATION_BIT, 1); 
//        SendHandshake();                                               //与服务器连接后发送握手指令
    }
    else
    {        
        m_PowerOnSendStatus = SEND_HANDSHAKE;
        //HAL_UART_Transmit(&huart3, (uint8_t*)"disconnec", 10, 1000);       
        m_U8ServerConnectStatus = DISCONNECTED;
        UpdateIconStatus(WIFI_COMM_STATE_INDICATION_BIT, m_U8APConnectStatus);       
    }
}

void CWifiUartModule::UpdateAPConnectStatus(U8 Flag)
{
    if (m_U8APConnectStatus == Flag)
    {
        return;
    }
    if (Flag)
    {        
        m_U8APConnectStatus = CONNECTED;
        UpdateIconStatus(WIFI_COMM_STATE_INDICATION_BIT, 1); 
    }
    else
    {                
        m_U8APConnectStatus = DISCONNECTED;
        UpdateServerConnectStatus(0);
        UpdateIconStatus(WIFI_COMM_STATE_INDICATION_BIT, 0);       
    }    
}

U8 CWifiUartModule::GetConnectStatus()
{
    return m_U8ServerConnectStatus;
}

U8 CWifiUartModule::GetAPStatus()
{
    return m_U8APConnectStatus;
}

void CWifiUartModule::ProcessPack()
{
}

U8 CWifiUartModule::AnalyseData(U8 Data)
{	    
    static U8 tempBuf[2] = {0};
    static U8 tempCount = 0;
    
    // 缓存接收到的数据
    tempBuf[tempCount++] = Data;
    
    // 当收到完整的字符或者缓存满时打印
    if(tempCount >= 1 || Data == '\n' || Data == '\r')
    {
       // PrintWifiData("[WIFI RX]", tempBuf, tempCount, false);
        if(Data == '\n' || Data == '\r')
        {
            PrintWifiData("[WIFI RX STR]", m_U8PackageBuffer, m_U16Count + 1, true);
        }
        tempCount = 0;
    }
    
    AddPackageData(Data);
    if (m_U32RspTicket == 0)
    {
        m_U32RspTicket = HAL_GetTick();
    }     
    return 0;
}

char* CWifiUartModule::FindContext(char* pSrc, I16 SrcLen, char* pContext, I16 ContextLen)
{
    I16 i;
    for (i = 0; i <= SrcLen - ContextLen; i++)
    {
        if (memcmp(pSrc + i, pContext, ContextLen) == 0)
        {
            return pSrc + i;
        }
    }
    return NULL;
}

void CWifiUartModule::AnalyseString()
{  
    U32 sU32Remain = 0;
    char* spS8 = NULL;
    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+WEVENT:", 10))   //包头
    {
        //+WEVENT：STATION_UP
        //+WEVENT：STATION_DOWN
        if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "STATION_UP", strlen("STATION_UP")))                 //AP连接成功
        {
            const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
            UpdateAPConnectStatus(1 && pWifiConfig->Switch);
        }
        else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "STATION_DOWN", strlen("STATION_DOWN")))          //AP连接失败
        {   
            UpdateAPConnectStatus(0);
        }        
    }
    else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "+CIPEVENT:", strlen("+CIPEVENT:")))   //包头
    {
        //+CIPEVENT：id,SERVER,DISCONNECTED
        //+CIPEVENT：id,SERVER,CONNECTED
        //+CIPEVENT：id,SERVER,CLOSED
        //+CIPEVENT：SOCKET,id,len,data
        if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "SERVER,CONNECTED", strlen("SERVER,CONNECTED")))          //与服务器建立连接
        {
            const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
            UpdateServerConnectStatus(1 && pWifiConfig->Switch);
        }
        else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "SERVER,DISCONNECTED", strlen("SERVER,DISCONNECTED")))   //与服务器断开连接
        {   
            UpdateServerConnectStatus(0);
        }
        else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "SERVER,CLOSED", strlen("SERVER,CLOSED")))        //关闭服务器连接
        {                    
            UpdateServerConnectStatus(0);
            if (m_bWaitingForTcpClose) {
                SetServerInfo(m_ServerIP[get_server_type()], m_ServerPort[get_server_type()],true);
                m_bWaitingForTcpClose = false;
                m_u32WaitTcpCloseTimeout = 0; 
            }
        }
        else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "SOCKET", strlen("SOCKET")))        //收到套接字发送来的数据
        {
            AnalyseTcpDatas();
        }        
    }         
    else if (m_pCurrWifiCmdPkg)
    {
        switch (m_pCurrWifiCmdPkg->U16CmdType)
        {
            case QUERY_FIRMWARE_VERSION:    //+WFVER:wl0: Sep 10 2014 11:28:46 version *********** 
                spS8 = FindContext((char*)m_U8PackageBuffer, m_U16Count, "version ", strlen("version "));
                if (spS8)        //关闭服务器连接
                {       
                    strcpy(m_WifiInfo.FirmwareVersion, spS8 + 8);
                    m_WifiInfo.WifiValid = 1;
                }
                break;
            case SET_ECHO:
            case ENABLE_DHCP:
                break;
            case TRANSPARENT_TRANSMISSION:
                break;
            case QUERY_AP:                
                break;
            case CONNECT_AP:
                break;
            case DISCONNECT_AP:
                break;
            case SET_SCAN_OPT:
                break;
            case QUERY_CONNECT_STATUS:            
                break;
            case QUERY_IP_ADDR:
                break;
            case QUERY_ID_STATUS:
                spS8 = FindContext((char*)m_U8PackageBuffer, m_U16Count, "close", strlen("close"));                
                if (spS8)        //关闭服务器连接
                {       
                    SetServerInfo(m_ServerIP[get_server_type()], m_ServerPort[get_server_type()], true);
                }  
                else
                {
                    spS8 =FindContext((char*)m_U8PackageBuffer, m_U16Count, "connected", strlen("connected"));
                    if (spS8)
                    {
                        const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
                        UpdateServerConnectStatus(1 && pWifiConfig->Switch);
                    }
                }
                break;
            case QUERY_DHCP_ENABLE:
                spS8 = FindContext((char*)m_U8PackageBuffer, m_U16Count,"ON", strlen("ON"));
                if (spS8)        //关闭服务器连接
                {       
                    m_WifiInfo.DHCPEnable = 1;                    
                }                
                else
                {
                    m_WifiInfo.DHCPEnable = 0;  
                    EnableDHCP();
                }
                break;
            case IP_STOP:
                break;
            case IP_OPEN:
                break;
            case SCAN_AP:
                UpdateAPList();
                break;                
            case AUTO_RCV_CONFIG:
                break;
            case EASYLINK:
                break;
                case SSL_CERT_SET:
                    // 当收到">"提示符时，表示模块已准备好接收证书数据
                    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, ">", 1))
                    {
                        // 打印接收到">"提示符
                        PrintWifiData("[WIFI RX] Received '>' prompt for SSL certificate", NULL, 0, true);
                        
                        // 更新MTLS配置状态
                        if (m_MTLSConfigState == MTLS_CONFIG_ROOT_CERT_WAIT ||
                            m_MTLSConfigState == MTLS_CONFIG_CLIENT_CERT_WAIT ||
                            m_MTLSConfigState == MTLS_CONFIG_PRIVATE_KEY_WAIT)
                        {
                            // 收到">"提示符，可以发送证书数据了
                            LoadMTLSCertificates();
                        }
                        m_U32RspTicket = 0; // 清除响应等待标志
                        PopCmdPkg(); // 移除当前命令
                    }
                    break;
            case SSL_OPT_SET:
                // 不需要特殊处理，只需等待OK响应
                break;
            case SSL_CERT_GET:
                // 不需要特殊处理，只需等待OK响应
                   // 处理证书获取响应
                if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "+SSLCERTGET:", strlen("+SSLCERTGET:")))
                {
                    ProcessSSLCertGetResponse((const char*)m_U8PackageBuffer, m_U16Count);
                }
                break;
            default:   
                return;            
        }
        m_pCurrWifiCmdPkg->U16CmdLen = 0;
        m_pCurrWifiCmdPkg = NULL; 
    }    
    if (m_U16Count > m_U16AtRspLen)
    {
        sU32Remain = m_U16Count - m_U16AtRspLen;
        memcpy(m_U8PackageBuffer, m_U8PackageBuffer + m_U16AtRspLen, sU32Remain);
    }        
    Reset();
    m_U16Count = sU32Remain;
}

void CWifiUartModule::SendCmdPkg(PWIFICMDPKG pWifiCmdPkg)
{  
    Reset();
    m_U32RspTicket = 0;

    // 打印发送的数据（同时以十六进制和字符串形式显示）
   // PrintWifiData("[WIFI TX HEX]", pWifiCmdPkg->Pkg, pWifiCmdPkg->U16CmdLen, false);
    PrintWifiData("[WIFI TX STR]", pWifiCmdPkg->Pkg, pWifiCmdPkg->U16CmdLen, true);
  
    HAL_UART_Transmit(&huart1, (uint8_t*)pWifiCmdPkg->Pkg, pWifiCmdPkg->U16CmdLen, 1000);           
}

U8 CWifiUartModule::SendEdfInfo()
{
    U16 sU16Length = 0;
    U8 i;
    U32 sU32Time[2];
   
    m_U8SendEdfRecords = (m_U16SendEdfIdx > 15) ? 15 : m_U16SendEdfIdx;
    if (m_U8SendEdfRecords > 0)
    {
        sU32Time[0] = m_pEdfDataSave->GetEdfHeadTime();
        sU32Time[0] = sU32Time[0] - (m_U16SendEdfIdx - 1) * 86400;       //数据开始时间
        sU32Time[1] = sU32Time[0] + (m_U8SendEdfRecords - 1) * 86400;    //数据结束时间
        sU16Length = m_U8SendEdfRecords;
        sU16Length = EDF_ONEC_DATA_FLASH_LEN * sU16Length + 12 + 8 + 1;      //12:Protocal(1Byte)+Index(1Byte)+DeviceInfo(10Bytes) 8:BTime+ETime 1:Flag
        m_U8ReadBuf[0] = NET_PACKAGE_HEAD1;
        m_U8ReadBuf[1] = NET_PACKAGE_HEAD2;
        m_U8ReadBuf[2] = EDF_CMD;
        m_U8ReadBuf[3] = (sU16Length & 0xff);
        m_U8ReadBuf[4] = (sU16Length >> 8);        
        m_U8ReadBuf[5] = NETWORD_PROTOCAL_VERSION;
        m_U8ReadBuf[6] = m_U8SendIdx++;
        
        memcpy(m_U8ReadBuf + 7, &m_DeviceInfo.Model, 10);
        m_U8ReadBuf[17] = (sU32Time[0] & 0xff);
        m_U8ReadBuf[18] = ((sU32Time[0] >> 8) & 0xff);
        m_U8ReadBuf[19] = ((sU32Time[0] >> 16) & 0xff);
        m_U8ReadBuf[20] = ((sU32Time[0] >> 24) & 0xff);
        m_U8ReadBuf[21] = (sU32Time[1] & 0xff);
        m_U8ReadBuf[22] = ((sU32Time[1] >> 8) & 0xff);
        m_U8ReadBuf[23] = ((sU32Time[1] >> 16) & 0xff);
        m_U8ReadBuf[24] = ((sU32Time[1] >> 24) & 0xff);
        m_U8ReadBuf[25] = (m_pEdfDataSave->GetEdfAffectedOfRecords() ? 1 : 0);            //0-正常包1-调整覆盖包
        
        for (i = 0; i < m_U8SendEdfRecords; i++)
        {
            m_pEdfDataSave->ReadEdfStatisticsData((m_pEdfDataSave->GetCurRecordIdx() + MAX_EDF_STORAGE_DAYS - m_U16SendEdfIdx + i) % MAX_EDF_STORAGE_DAYS, (STATISTICSINFO*)(m_U8ReadBuf + 26 + EDF_ONEC_DATA_FLASH_LEN * i));
        }
               
        AddNetPkgTail(m_U8ReadBuf + NET_DATA_BEGIN_IDX, sU16Length);
        SendNetData(m_U8ReadBuf, sU16Length + NET_PACKHEAD_PACKEND_LEN);
        return 1;
    }

    return 0;
}

#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
U8 CWifiUartModule::SendResponseInfo()
{
    U16 sU16Length = 0;
    U8 i;
    
    sU16Length = 13;
    m_U8ReadBuf[0] = NET_PACKAGE_HEAD1;
    m_U8ReadBuf[1] = NET_PACKAGE_HEAD2;
    m_U8ReadBuf[2] = 0xF0;
    m_U8ReadBuf[3] = (sU16Length & 0xff);
    m_U8ReadBuf[4] = (sU16Length >> 8);    
    m_U8ReadBuf[5] = NETWORD_PROTOCAL_VERSION;
    m_U8ReadBuf[6] = m_U8SendIdx++;
    
    memcpy(m_U8ReadBuf + 7, &m_DeviceInfo.Model, 10);
    m_U8ReadBuf[17] = UPDATEPARAMETER_CMD;
           
    AddNetPkgTail(m_U8ReadBuf + NET_DATA_BEGIN_IDX, sU16Length);
    SendNetData(m_U8ReadBuf, sU16Length + NET_PACKHEAD_PACKEND_LEN);

    return 1;
}



#endif

void CWifiUartModule::AddNetPkgTail(U8* pData, U16 DataLen)
{
    pData[DataLen] = CheckSum(pData, DataLen);
    pData[1 + DataLen] = NET_PACKAGE_TAIL1;
    pData[2 + DataLen] = NET_PACKAGE_TAIL2;
    pData[3 + DataLen] = NET_PACKAGE_TAIL3;
    pData[4 + DataLen] = NET_PACKAGE_TAIL4;    
}

void CWifiUartModule::SendHandshake()
{
    U8 sU8DataPkg[64];
    U16 sU16Length;
    if (m_U8ServerConnectStatus != CONNECTED)
    {
        return;
    }    
    m_U16HeartTicket++;
    //if (m_U16HeartTicket >= HEART_BEAT_TIME)      //10S
    if (m_U16HeartTicket < 20) //1S
    {
        return;
    }        
    m_U16HeartTicket = 0;
    m_NetDataHead.Cmd = HANDSHAKE_CMD;
    sU16Length = 2 + sizeof(NETDEVICEINFO);
#if (NETWORD_PROTOCAL_VERSION <= 1)    
    m_NetDataHead.Len[0] = ((sU16Length >> 8) & 0xff);
    m_NetDataHead.Len[1] = (sU16Length & 0xff);
#else   
    m_NetDataHead.Len[0] = (sU16Length & 0xff);
    m_NetDataHead.Len[1] = ((sU16Length >> 8) & 0xff);
#endif    
    m_NetDataHead.Index = m_U8SendIdx++;
    memcpy(sU8DataPkg, &m_NetDataHead, sizeof(NETDATAHEAD));
    memcpy(sU8DataPkg + sizeof(NETDATAHEAD), &m_DeviceInfo, sizeof(m_DeviceInfo));
    AddNetPkgTail(sU8DataPkg + NET_DATA_BEGIN_IDX, sU16Length);    
    SendNetData(sU8DataPkg, sU16Length + NET_PACKHEAD_PACKEND_LEN);
}

#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
void CWifiUartModule::SendParameterConfig()
{
    U16 sU16Length;
    if (m_U8ServerConnectStatus != CONNECTED)
    {
        return;
    }    
    m_U16HeartTicket++;
    if (m_U16HeartTicket < 20) //1S
    {
        return;
    }        
    m_U16HeartTicket = 0;
    m_U8ReadBuf[0] = NET_PACKAGE_HEAD1;
    m_U8ReadBuf[1] = NET_PACKAGE_HEAD2;
    m_U8ReadBuf[2] = SENDPARAMETER_CMD;
    m_U8ReadBuf[5] = NETWORD_PROTOCAL_VERSION;
    sU16Length = 2 + 10 + sizeof(PARAMETERCONFIG);
    m_U8ReadBuf[3] = (sU16Length & 0xff);
    m_U8ReadBuf[4] = ((sU16Length >> 8) & 0xff);
    m_U8ReadBuf[6] = m_U8SendIdx++;

    memcpy(m_U8ReadBuf + 7, &m_DeviceInfo.Model, 10);
    
    memcpy(m_U8ReadBuf + 17, (void*)m_pParameterData, sizeof(PARAMETERCONFIG));
    // 清零wifi配置
    memset(m_U8ReadBuf + 17 + offsetof(PARAMETERCONFIG, WifiConfig), 0, sizeof(WIFICONFIG));

    AddNetPkgTail(m_U8ReadBuf + NET_DATA_BEGIN_IDX, sU16Length);    
    SendNetData(m_U8ReadBuf, sU16Length + NET_PACKHEAD_PACKEND_LEN);    
}
#endif

void CWifiUartModule::SendHeartBeat()
{
#if (NETWORD_PROTOCAL_VERSION)
#if (NETWORD_PROTOCAL_VERSION == 1)
    U8 sU8DataPkg[32] = {NET_PACKAGE_HEAD1, NET_PACKAGE_HEAD2, HEARTBEAT_CMD, 0x00, 0x0c, NETWORD_PROTOCAL_VERSION};
#else
    U8 sU8DataPkg[32] = {NET_PACKAGE_HEAD1, NET_PACKAGE_HEAD2, HEARTBEAT_CMD, 0x0c, 0x00, NETWORD_PROTOCAL_VERSION};
#endif
    U16 sU16Length = 0x0c;
    if (m_U8ServerConnectStatus != CONNECTED)
    {
        return;
    }    
    if (m_U16HeartTicket >= HEART_BEAT_TIME)      //5S
    {
        m_U16HeartTicket = 0;
        memcpy(sU8DataPkg + 7, &m_DeviceInfo.Model, 10);
        sU8DataPkg[6] = m_U8SendIdx++;
        AddNetPkgTail(sU8DataPkg + NET_DATA_BEGIN_IDX, sU16Length);    
        SendNetData(sU8DataPkg, sU16Length + NET_PACKHEAD_PACKEND_LEN);        
    }
    else
    {
        m_U16HeartTicket++;
    }
#else
    U8 sU8DataPkg[32] = {NET_PACKAGE_HEAD1, NET_PACKAGE_HEAD2, 0x04, 0x00, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, NET_PACKAGE_TAIL1, NET_PACKAGE_TAIL2, NET_PACKAGE_TAIL3, NET_PACKAGE_TAIL4};
    if (m_U8ServerConnectStatus != CONNECTED)
    {
        return;
    }    
    if (m_U16HeartTicket >= HEART_BEAT_TIME)      //10S
    {
        m_U16HeartTicket = 0;
        memcpy(sU8DataPkg + 6, &m_DeviceInfo.Model, 10);
        sU8DataPkg[5] = m_U8SendIdx++;
        AddNetPkgTail(sU8DataPkg + NET_DATA_BEGIN_IDX, sU8DataPkg[4]);    
        SendNetData(sU8DataPkg, sU8DataPkg[4] + NET_PACKHEAD_PACKEND_LEN);        
    }
    else
    {
        m_U16HeartTicket++;
    }
#endif
}

void CWifiUartModule::OverTime()
{
    m_U16OverTime++;
    if (m_U16OverTime == OVER_TIME)
    {
        m_U16OverTime = 0;
        UpdateServerConnectStatus(0);
        IPStop();
        SetServerInfo(m_ServerIP[get_server_type()], m_ServerPort[get_server_type()], true);
        return;
    }    
}

void CWifiUartModule::SendPowerOnOffNetData(U8 Flag/*, RTC_TimeTypeDef1* RtcTime*/)
{
    U16 sU16Length;
    RTC_TimeTypeDef1 RTC_Time;    
   
    if (m_U8ServerConnectStatus == CONNECTED && Flag)   //当开机时网络处于连接状态，数据管理模块则开始存储数据
    {
        m_WifiDataManager.SetDataSaveFlag(1);
    }
    else if (Flag)  //当开机时网络未处于连接状态，数据管理模块则不进行存储数据
    {
        m_WifiDataManager.SetDataSaveFlag(0);
    }
    
    if (m_WifiDataManager.GetDataSaveFlag() == 0)
    {
        return;
    }

    m_NetDataHead.Cmd = Flag ? POWERON_CMD : POWEROFF_CMD;
    m_NetDataHead.Index = m_U8SendIdx++;
  
    RTC_ReadTime(&RTC_Time);    
    if (Flag)
    {
        ClearRealData();
        m_RealDataHead.DataSerialID = 0;
        
        sU16Length = 1 + sizeof(m_NetPowerOnInfo);//1 + sizeof(m_NetPowerOnInfo)取消Flag标志
                     
        m_NetPowerOnInfo.DateTime.Year = RTC_Time.year - CENTURY;
        m_NetPowerOnInfo.DateTime.Mon = RTC_Time.mon;
        m_NetPowerOnInfo.DateTime.Day = RTC_Time.day;
        m_NetPowerOnInfo.DateTime.Hour = RTC_Time.hour;
        m_NetPowerOnInfo.DateTime.Min = RTC_Time.minu;
        m_NetPowerOnInfo.DateTime.Sec = RTC_Time.sec;
        UpdateNetSystemConfig(&m_NetPowerOnInfo);
    }
    else
    {
#if (NETWORD_PROTOCAL_VERSION > 1)
        sU16Length = 18;
        m_NetPowerOffInfo.DateTime.Year = RTC_Time.year - CENTURY;
        m_NetPowerOffInfo.DateTime.Mon = RTC_Time.mon;
        m_NetPowerOffInfo.DateTime.Day = RTC_Time.day;
        m_NetPowerOffInfo.DateTime.Hour = RTC_Time.hour;
        m_NetPowerOffInfo.DateTime.Min = RTC_Time.minu;
        m_NetPowerOffInfo.DateTime.Sec = RTC_Time.sec;
#endif
#if (NETWORD_PROTOCAL_VERSION == 1)
        sU16Length = 12;
#endif
#if (NETWORD_PROTOCAL_VERSION == 0)
        sU16Length = 8;
#endif
    }
#if (NETWORD_PROTOCAL_VERSION <= 1)
    m_NetDataHead.Len[0] = ((sU16Length >> 8) & 0xff);
    m_NetDataHead.Len[1] = (sU16Length & 0xff);
#else    
    m_NetDataHead.Len[0] = (sU16Length & 0xff);
    m_NetDataHead.Len[1] = ((sU16Length >> 8) & 0xff);
#endif    

    memcpy(m_U8PowerOnOffPkgBuf, &m_NetDataHead, sizeof(NETDATAHEAD));
    //m_U8PowerOnOffPkgBuf[6] = Flag;   //取消Flag标志
#if (NETWORD_PROTOCAL_VERSION > 1)
    if (Flag)
    {
        if(g_ConfigSave.GetParameter(WIFI_SWITCH))  //标记数据来源
        {
            m_NetPowerOnInfo.Config.UpLoadChannelType = (U8)WIFI_TYPE;
        }
        //加载版本号
        memset(&m_NetPowerOnInfo.Config.AppSoftVersion, 0, sizeof(m_NetPowerOnInfo.Config.AppSoftVersion));
        snprintf((char*)m_NetPowerOnInfo.Config.AppSoftVersion, sizeof(m_NetPowerOnInfo.Config.AppSoftVersion), "%s", g_I8AppSoftVersion);
        memcpy(m_U8PowerOnOffPkgBuf + 7, (void*)&m_NetPowerOnInfo, sizeof(NETPOWERONINFO));//m_U8PowerOnOffPkgBuf + 7取消Flag标志
    }
    else
    {
        memcpy(m_U8PowerOnOffPkgBuf + 7, (void*)&m_NetPowerOffInfo, sizeof(NETPOWEROFFINFO));
    }
    AddNetPkgTail(m_U8PowerOnOffPkgBuf + NET_DATA_BEGIN_IDX, sU16Length);
#endif    
#if (NETWORD_PROTOCAL_VERSION == 1)
    if (Flag)
    {
        memcpy(m_U8PowerOnOffPkgBuf + 7, (void*)&m_NetPowerOnInfo, sizeof(NETPOWERONINFO));//m_U8PowerOnOffPkgBuf + 7取消Flag标志
    }
    else
    {
        memcpy(m_U8PowerOnOffPkgBuf + 7, (void*)&m_DeviceInfo, 10);
    }
    AddNetPkgTail(m_U8PowerOnOffPkgBuf + NET_DATA_BEGIN_IDX, sU16Length);
#endif
#if (NETWORD_PROTOCAL_VERSION == 0)
    if (Flag)
    {
        memcpy(m_U8PowerOnOffPkgBuf + 6, (void*)&m_NetPowerOnInfo, sizeof(NETPOWERONINFO));//m_U8PowerOnOffPkgBuf + 7取消Flag标志
    }
    else
    {
        memcpy(m_U8PowerOnOffPkgBuf + 6, (void*)&m_DeviceInfo, 6);
    }
    AddNetPkgTail(m_U8PowerOnOffPkgBuf + NET_DATA_BEGIN_IDX, sU16Length);
#endif    
    if (Flag == 0)
    {
        if (m_NetDataSendBuf.U16InIdx)  //未填满整块的数据，进行存储，用于进行发送
        {
    #if (NETWORD_PROTOCAL_VERSION > 1)
            m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx][3] = ((m_NetDataSendBuf.U16InIdx - 5) & 0xff);       //+5为包尾，-10(包头+包尾-Index)
            m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx][4] = ((m_NetDataSendBuf.U16InIdx - 5) >> 8);
    #else
            m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx][3] = ((m_NetDataSendBuf.U16InIdx - 5) >> 8);       //+5为包尾，-10(包头+包尾-Index)
            m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx][4] = ((m_NetDataSendBuf.U16InIdx - 5) & 0xff);
    #endif        
            AddNetPkgTail(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx] + NET_DATA_BEGIN_IDX, m_NetDataSendBuf.U16InIdx - NET_DATA_BEGIN_IDX);            
            m_WifiDataManager.SaveNetPackage(m_NetDataSendBuf.SendBuf[m_NetDataSendBuf.U8BaseIdx], 1024);
        }        
        SaveWifiPowerOffData();
    }
    else
    {
        SaveWifiPowerOnData();
        AddRealDataHead();
    }        
    if (Flag == 0)          //如果关机，则不再进行数据存储
    {
        m_WifiDataManager.SetDataSaveFlag(0);
    }
}

void CWifiUartModule::PushCmdPkg(PWIFICMDPKG pWifiCmdPkg)
{
    if (((m_WifiCmdPkgList.U8InIdx + 1) % WIFI_MAX_PKG_CNT) == m_WifiCmdPkgList.U8OutIdx)
    {
        return;
    }    
    memcpy(&m_WifiCmdPkgList.CmdPkg[m_WifiCmdPkgList.U8InIdx], pWifiCmdPkg, sizeof(WIFICMDPKG));
    m_WifiCmdPkgList.U8InIdx = (m_WifiCmdPkgList.U8InIdx + 1) % WIFI_MAX_PKG_CNT;
}

void CWifiUartModule::PopCmdPkg()
{
    if (m_WifiCmdPkgList.U8OutIdx != m_WifiCmdPkgList.U8InIdx)
    {        
        m_pCurrWifiCmdPkg = &m_WifiCmdPkgList.CmdPkg[m_WifiCmdPkgList.U8OutIdx];        
        m_WifiCmdPkgList.U8OutIdx = (m_WifiCmdPkgList.U8OutIdx + 1) % WIFI_MAX_PKG_CNT;       
    }
}

U8 CWifiUartModule::GetWifiCmdPkgListFree()
{
    return (m_WifiCmdPkgList.U8OutIdx + WIFI_MAX_PKG_CNT - 1 - m_WifiCmdPkgList.U8InIdx) % WIFI_MAX_PKG_CNT;
}

void CWifiUartModule::AutoSendWifiCmdPkg()
{    
    if (m_pCurrWifiCmdPkg)
    {
        m_U32SendIntervalTimes += WIFI_SEND_TIME_CYCLE;
        if (m_pCurrWifiCmdPkg->U16CmdLen)
        {
            //if (m_U32SendIntervalTimes >= MAX_SEND_INTERVAL)
            if (m_U32SendIntervalTimes >= m_pCurrWifiCmdPkg->U16RspWaitTime)
            {
                m_U32SendIntervalTimes = 0;

                m_U8CurrPkgSendTimes++;                
                if(m_U8CurrPkgSendTimes >= MAX_SEND_TIMES)
                {
                    m_pCurrWifiCmdPkg = NULL;
                }
                else
                {
                    SendCmdPkg(m_pCurrWifiCmdPkg);
                    return;
                }
            }
        }
    }
    else
    {
        if (m_WifiCmdPkgList.U8OutIdx != m_WifiCmdPkgList.U8InIdx)
        {
            m_U32SendIntervalTimes = 0;
            PopCmdPkg();                   
            m_U8CurrPkgSendTimes = 0;
            SendCmdPkg(m_pCurrWifiCmdPkg);            
        }
        else
        {
            if (m_WifiDataPkg.U16CmdLen)
            {
                m_U32SendIntervalTimes += WIFI_SEND_TIME_CYCLE;
                if (m_U32SendIntervalTimes >= m_WifiDataPkg.U16RspWaitTime)
                {
                    m_U32SendIntervalTimes = 0;  
                    return;
                }                 
            }
        }
    }
}

void CWifiUartModule::WaitRsp()
{    
    I16 sI16FindDataLen = 0;
    char* spI8[2];    

    if (m_U16Count < m_U16MaxPkgSize)
    {
        //if (m_U16Count > 0)
        //{
            //HAL_UART_Transmit(&huart3, (uint8_t*)m_U8PackageBuffer, m_U16Count, 1000);       
        //}
        m_U8PackageBuffer[m_U16Count] = 0;
    }
//    else
//    {
//        HAL_UART_Transmit(&huart3, (uint8_t*)"ccc\r\n", 5, 1000);       
//        m_U16MaxPkgSize = m_U16MaxPkgSize;
//    }
    spI8[0] = FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+WEVENT:", 9);
    spI8[1] = FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+CIPEVENT:", 12);
    
    
    if (spI8[0])   //包头
    {
        spI8[1] = strstr(spI8[0] + strlen("\r\n+WEVENT:"), "\r\n");
        //HAL_UART_Transmit(&huart3, (uint8_t*)"WEVENT1", 10, 1000);       
        if (spI8[1])
        {
        //HAL_UART_Transmit(&huart3, (uint8_t*)"WEVENT2", 6, 1000);       
            m_U16AtRspLen = (U32)spI8[1] - (U32)m_U8PackageBuffer + 2;
            AnalyseString();                        
        }                    
        return;
    }
    else if (spI8[1])   //包头
    {
       // HAL_UART_Transmit(&huart3, (uint8_t*)"ipevent1", 10, 1000);       
        sI16FindDataLen = (U32)(&m_U8PackageBuffer[m_U16Count]) - (U32)spI8[1] - strlen("\r\n+CIPEVENT:");
        spI8[1] = FindContext(spI8[1] + strlen("\r\n+CIPEVENT:"), sI16FindDataLen, "\r\n", 2);         
        if (spI8[1])
        {
        //HAL_UART_Transmit(&huart3, (uint8_t*)"ip2", 3, 1000);       
            m_U16AtRspLen = (U32)spI8[1] - (U32)m_U8PackageBuffer + 2;
            AnalyseString();                        
        }
        return;
    }        
    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\nERROR", strlen("\r\nERROR")))
    {
        Reset();
    }
    
    if (m_U16Count > 0)
    {
        if (m_pCurrWifiCmdPkg)
        {
            switch (m_pCurrWifiCmdPkg->U16CmdType)
            {
                case QUERY_FIRMWARE_VERSION:
                    //+WFVER:wl0: Sep 10 2014 11:28:46 version *********** 
                    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+WFVER:", strlen("\r\n+WFVER:")))
                    {
                        spI8[1] = strstr((char*)m_U8PackageBuffer, "OK\r\n");
                        if (spI8[1])
                        {                        
                            m_U16AtRspLen = (U32)spI8[1] - (U32)m_U8PackageBuffer + 4;
                            AnalyseString();
                        }
                    } 
                    break;
                case SET_ECHO:
                case ENABLE_DHCP:
                case IP_STOP:
                case QUERY_AP:
                case CONNECT_AP:
                case DISCONNECT_AP:
                case SET_SCAN_OPT:
                case SSL_OPT_SET:
                case SSL_CERT_GET:
                case IP_OPEN:
                    spI8[1] = FindContext((char*)m_U8PackageBuffer, m_U16Count, "OK\r\n", strlen("OK\r\n"));
                    if (spI8[1])
                    {              
                        m_U16AtRspLen = (U32)spI8[1] - (U32)m_U8PackageBuffer + 4;
                        AnalyseString();
                    } 
                    break;
                case TRANSPARENT_TRANSMISSION:
                    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+CIPSENDRAW:", strlen("\r\n+CIPSENDRAW:")))
                    {
                        AnalyseString();                        
                    }
                    break;
                case QUERY_CONNECT_STATUS:
                    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+WJAPS:", strlen("\r\n+WJAPS:")))   //包头
                    {
                        spI8[1] = strstr((char*)m_U8PackageBuffer, "OK\r\n");
                        if (spI8[1])
                        {                        
                            m_U16AtRspLen = (U32)spI8[1] - (U32)m_U8PackageBuffer + 4;
                            AnalyseString();
                        }
                    }                               
                    break;
                case QUERY_IP_ADDR:
                    break;
                case QUERY_ID_STATUS:
                    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+CIPSTATUS:", strlen("\r\n+CIPSTATUS:")))   //包头
                    {
                        spI8[1] = strstr((char*)m_U8PackageBuffer, "OK\r\n");
                        if (spI8[1])
                        {                        
                            m_U16AtRspLen = (U32)spI8[1] - (U32)m_U8PackageBuffer + 4;
                            AnalyseString();
                        }
                    }
                    break;
                case QUERY_DHCP_ENABLE:
                    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+WDHCP:", strlen("\r\n+WDHCP:")))   //包头
                    {
                        spI8[1] = strstr((char*)m_U8PackageBuffer, "OK\r\n");
                        if (spI8[1])
                        {                        
                            m_U16AtRspLen = (U32)spI8[1] - (U32)m_U8PackageBuffer + 4;
                            AnalyseString();
                        }
                    }   
                    break;
                case SCAN_AP:
                    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\n+WSCAN:", strlen("\r\n+WSCAN:")))   //包头
                    {
                        spI8[1] = strstr((char*)m_U8PackageBuffer, "\r\n\r\n");
                        if (spI8[1])
                        {                        
                            m_U16AtRspLen = (U32)spI8[1] - (U32)m_U8PackageBuffer + 4;
                            AnalyseString();                        
                        }
                    }
                    break;
                case AUTO_RCV_CONFIG:
                    break;
                case EASYLINK:
                    break;                
                case SSL_CERT_SET:
                    // 当收到">"提示符时，表示模块已准备好接收证书数据
                    if (FindContext((char*)m_U8PackageBuffer, m_U16Count, ">", 1))
                    {
                         AnalyseString();  
                    }
                    break;
                default:
                    if (m_WifiDataPkg.U16CmdLen && FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\nOK", strlen("\r\nOK")))   //包头
                    {
                        Reset();
                        m_WifiDataPkg.U16CmdLen = 0;
                    }
                    else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "OK", strlen("OK")))
                    {
                        Reset();
                    }
                    else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\nERROR", strlen("\r\nERROR")))
                    {
                        Reset();
                    }
                    break;        
            }
        }
        else
        {
            if (m_WifiDataPkg.U16CmdLen && FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\nOK", strlen("\r\nOK")))   //包头
            {
                Reset();
                m_WifiDataPkg.U16CmdLen = 0;
            }
            else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "OK", strlen("OK")))
            {
                Reset();
            }
            else if (FindContext((char*)m_U8PackageBuffer, m_U16Count, "\r\nERROR", strlen("\r\nERROR")))
            {
                Reset();
            }            
        }    
    }
    
    if (m_U32RspTicket != 0)
    {
        if (HAL_GetTick() - m_U32RspTicket >= 1000)     //如果距离收到上次数据已达到500ms，则认为未收到数据。
        {
            if (m_pCurrWifiCmdPkg) //发送命令包，重发
            {
                m_U32SendIntervalTimes = m_pCurrWifiCmdPkg->U16RspWaitTime;    //立即重新发送数据包
                //DEBUG_INFO("m_U32SendIntervalTimes: %d, m_pCurrWifiCmdPkg->U16RspWaitTime: %d", m_U32SendIntervalTimes, m_pCurrWifiCmdPkg->U16RspWaitTime);
            }    
            else if (m_WifiDataPkg.U16CmdLen) //发送数据包，重发
            {
                m_U32SendIntervalTimes = m_WifiDataPkg.U16RspWaitTime;
                //DEBUG_INFO("m_U32SendIntervalTimes: %d, m_WifiDataPkg.U16RspWaitTime: %d", m_U32SendIntervalTimes, m_WifiDataPkg.U16RspWaitTime);
            }
        }
    }
}

void CWifiUartModule::RunTask()
{
    OS_ERR  err;      	   
//    OSTaskSuspend(0, &err);   //WIFI任务不挂起，因为在WIFI模块打开电源后，随时都会有数据发送过来
    
    while (1)
    {
#if (ENABLE_WATCHDOG == 1)        
        SetTaskRunFlag(TASK_WIFI_FLAG);
#endif
        ProcessRcvData();
        WaitRsp();
        AutoSendWifiCmdPkg();
        const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
        if (pWifiConfig->Switch)       //只有在WIFI模块存在时，才进行以下处理
        {
            
            // 处理MTLS配置
            ProcessMTLSConfig();
            if (m_U8ServerConnectStatus)
            {
                // 连接成功后重置重连相关变量
                m_U8ReconnectCount = 0;
                m_U32ReconnectTime = 0;
                m_bWaitingForTcpClose = false;
                m_u32WaitTcpCloseTimeout = 0;
#if (NETWORD_PROTOCAL_VERSION)
                if (m_PowerOnSendStatus == SEND_HANDSHAKE)
                {
                    SendHandshake();
                }
#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
                else if (m_PowerOnSendStatus == SEND_PARAMETERCONFIG)
                {
                    SendParameterConfig();
                }
#endif                
                else if (m_PowerOnSendStatus == SEND_HEARTBEAT)
                {
                    SendHeartBeat();
                }
#else                
                SendHeartBeat();
#endif
                OverTime();
            }
            else
            {
                // 计算当前应该使用的重连间隔
                U32 currentInterval = MIN_RECONNECT_TIME * (1 << m_U8ReconnectCount);
                if(currentInterval > MAX_RECONNECT_TIME) {
                    currentInterval = MAX_RECONNECT_TIME;
                }

                m_U32ReconnectTime += WIFI_SEND_TIME_CYCLE;
                if(m_U32ReconnectTime >= currentInterval)
                {
                    m_U32ReconnectTime = 0;

                    m_U8ReconnectCount++;
                    if (m_U8ReconnectCount < 4)
                    {
                        if (!m_bWaitingForTcpClose) {
                            IPStop(); 
                            m_bWaitingForTcpClose = true;
                            m_u32WaitTcpCloseTimeout = 0;
                        }
                    }else
                    {
                         if (GetBldcmStatus() != MOTOR_START_STATE)
                        {
                            // 添加标志位,避免重复复位
                            static bool resetInProgress = false;
                            
                            if(!resetInProgress) {                                
                                // 执行复位操作
                                HAL_GPIO_WritePin(WIFI_CONTROL_GPIO_Port, WIFI_CONTROL_Pin, GPIO_PIN_SET);
                                OSTimeDly(50, OS_OPT_TIME_DLY, &err);
                                HAL_GPIO_WritePin(WIFI_CONTROL_GPIO_Port, WIFI_CONTROL_Pin, GPIO_PIN_RESET);
                                
                                m_u32WifiReinitTime = 0;
                                 resetInProgress = true;
                               
                            }
                            
                            // 等待重新初始化完成
                            m_u32WifiReinitTime += WIFI_SEND_TIME_CYCLE;
                            if(m_u32WifiReinitTime >= 3000) 
                            {
                                const WIFICONFIG* pResetWifiConfig = g_ConfigSave.GetWifiConfig();
                                SetModuleInfo(pResetWifiConfig->SSID, pResetWifiConfig->PWD);
                                resetInProgress = false;
                                m_U8ReconnectCount = 0;
                                m_u32WifiReinitTime =0;
                            }
                        }
                    }
                }
				m_u32WaitTcpCloseTimeout += WIFI_SEND_TIME_CYCLE;
                if (m_u32WaitTcpCloseTimeout >= 30000 && m_bWaitingForTcpClose)
                {
                    SetServerInfo(m_ServerIP[get_server_type()], m_ServerPort[get_server_type()],true);
                    m_bWaitingForTcpClose = false; 
                    m_u32WaitTcpCloseTimeout = 0;  
                }
            }
            m_WifiDataManager.TimerSaveDataInfo();
        }
        else
        {
            m_U16OverTime = 0;
#if (NETWORD_PROTOCAL_VERSION)
            m_NetDataHead.ProtocolVersion = NETWORD_PROTOCAL_VERSION;
            m_PowerOnSendStatus = SEND_HANDSHAKE;
#endif    
            if (m_WifiInfo.WifiValid)                   //如果已检测到WIFI模块，则关闭WIFI模块电源，除非手动打开WIFI开关
            {
                if (m_U8RestoreFactoryFlag == 0)        //如果已经恢复出厂设置，则必须等待Server IP 与AP信息清除后，再关闭电源
                {
                    HAL_GPIO_WritePin(WIFI_CONTROL_GPIO_Port, WIFI_CONTROL_Pin, GPIO_PIN_SET);
                }
                else if (m_U8RestoreFactoryFlag)
                {
                    if (m_WifiCmdPkgList.U8OutIdx == m_WifiCmdPkgList.U8InIdx && m_pCurrWifiCmdPkg == NULL)
                    {
                        m_U8RestoreFactoryFlag = 0;
                        OSTimeDly(1000, OS_OPT_TIME_DLY, &err);
                    }
                }
            }
            UpdateAPConnectStatus(0);
        }
        
        OSTimeDly(WIFI_SEND_TIME_CYCLE, OS_OPT_TIME_DLY, &err);    
    }
}

CWifiUartModule g_WifiUartModule;

void AppTaskWifiUart(void *p_arg)
{
    g_WifiUartModule.RunTask();    
}


/**************创建WIFI通讯任务*********************/
TASK_REGISTER(AppTaskWifiUartTCB,
             "App Task Wifi Uart",
             AppTaskWifiUart, 
             0,
             APP_CFG_TASK_WIFIUART_PRIO,
             &AppTaskWifiUartStk[0],
             APP_CFG_TASK_WIFIUART_STK_SIZE / 10,
             APP_CFG_TASK_WIFIUART_STK_SIZE,
             0,
             0,
             0,
#if (DEBUG_TASK_RESOURCE == 1)
             (OS_OPT_TASK_STK_CHK | OS_OPT_TASK_STK_CLR)
#else
             (OS_OPT_TASK_STK_CLR)
#endif
             );

// 添加SSL证书设置函数实现
void CWifiUartModule::SetSSLCert(U8 type)
{
    if (type > 3) // 证书类型范围检查：0-根证书，1-客户端证书，2-私钥，3-证书吊销表
        return;
        
    char cmd[32];
    sprintf(cmd, "AT+SSLCERTSET=%d\r\n", type);
    
    WIFICMDPKG sCmdPkg;
    memset(&sCmdPkg, 0, sizeof(WIFICMDPKG));
    sCmdPkg.U16CmdType = SSL_CERT_SET;
    sCmdPkg.U16CmdLen = strlen(cmd);
    sCmdPkg.U16RspWaitTime = 3000;
    memcpy(sCmdPkg.Pkg, cmd, sCmdPkg.U16CmdLen);
    
    PushCmdPkg(&sCmdPkg);
}

void CWifiUartModule::ProcessSSLCertData(const char* data, U16 length)
{
  if (data == NULL || length == 0)
        return;
        
    // 打印即将发送的证书数据
    PrintWifiData("[WIFI TX] Sending SSL certificate data", (const U8*)data, length, true);
    
    // 发送证书数据
    HAL_UART_Transmit(&huart1, (uint8_t*)data, length, 1000);
    
    // 发送结束符 Ctrl+Z (ASCII码0x1A)
    U8 endChar = 0x1A;
    HAL_UART_Transmit(&huart1, (uint8_t*)&endChar, 1, 1000);
    
    // 打印发送完成信息
    PrintWifiData("[WIFI TX] SSL certificate data sent", NULL, 0, true);
    
    // 更新状态
    switch(m_MTLSConfigState)
    {
        case MTLS_CONFIG_ROOT_CERT_WAIT:
            m_MTLSConfigState = MTLS_CONFIG_CLIENT_CERT;
            break;
        case MTLS_CONFIG_CLIENT_CERT_WAIT:
            m_MTLSConfigState = MTLS_CONFIG_PRIVATE_KEY;
            break;
        case MTLS_CONFIG_PRIVATE_KEY_WAIT:
            m_MTLSConfigState = MTLS_CONFIG_SSL_OPT;
            break;
        default:
            break;
    }
    
    m_MTLSConfigTimer = 0; // 重置配置计时器
}


// 添加SSL选项设置函数实现
void CWifiUartModule::SetSSLOpt(U8 id, U8 isSSLRoot, U8 isSSLClient, U8 isSSLCrl)
{        
    char cmd[64];
    sprintf(cmd, "AT+CIPSSLOPT=%d,%d,%d,%d\r\n", id, isSSLRoot, isSSLClient, isSSLCrl);
    
    WIFICMDPKG sCmdPkg;
    memset(&sCmdPkg, 0, sizeof(WIFICMDPKG));
    sCmdPkg.U16CmdType = SSL_OPT_SET;
    sCmdPkg.U16CmdLen = strlen(cmd);
    sCmdPkg.U16RspWaitTime = 1000;
    memcpy(sCmdPkg.Pkg, cmd, sCmdPkg.U16CmdLen);
    
    PushCmdPkg(&sCmdPkg);
}


// 加载MTLS证书
void CWifiUartModule::LoadMTLSCertificates()
{
    // 从STM32片内Flash中直接读取证书和私钥
    // Flash地址常量定义
    #define OFFSET_EXT_SERVICE_PUB_KEY_LEN  0x080FE000  // CA证书长度地址
    #define OFFSET_EXT_SERVICE_PUB_KEY_DATA 0x080FE002  // CA证书数据地址
    #define OFFSET_DEVICE_PRIVATE_KEY_LEN   0x080FE800  // 设备私钥长度地址
    #define OFFSET_DEVICE_PRIVATE_KEY_DATA  0x080FE802  // 设备私钥数据地址
    #define OFFSET_DEVICE_CERT_LEN          0x080FF000  // 设备证书长度地址
    #define OFFSET_DEVICE_CERT_DATA         0x080FF002  // 设备证书数据地址
    
    // 直接从Flash地址获取长度
    U16 caCertLength = *((U16*)OFFSET_EXT_SERVICE_PUB_KEY_LEN);
    
#if USE_FULL_MTLS_AUTH
    // 完整MTLS模式：需要客户端证书和私钥
    U16 clientKeyLength = *((U16*)OFFSET_DEVICE_PRIVATE_KEY_LEN);
    U16 clientCertLength = *((U16*)OFFSET_DEVICE_CERT_LEN);
    
    // 获取证书和密钥的数据指针
    const U8* pClientKeyData = (const U8*)OFFSET_DEVICE_PRIVATE_KEY_DATA;
    const U8* pClientCertData = (const U8*)OFFSET_DEVICE_CERT_DATA;
#endif
    
    // 获取根证书数据指针
    const U8* pCaCertData = (const U8*)OFFSET_EXT_SERVICE_PUB_KEY_DATA;
    
    // 安全校验：检查长度是否合理
    const U16 MAX_CERT_SIZE = 2048;  // 设置一个合理的最大证书大小
    // 结束符号 Ctrl+Z (ASCII码0x1A)
    const U8 endChar = 0x1A;
    
    // 根据当前状态处理证书配置
    switch (m_MTLSConfigState)
    {
        case MTLS_CONFIG_ROOT_CERT:
            // 发送根证书
            SetSSLCert(0);
            m_MTLSConfigState = MTLS_CONFIG_ROOT_CERT_WAIT; // 等待">"提示符
            break;
            
        case MTLS_CONFIG_ROOT_CERT_WAIT:
            // 发送根证书数据（直接从Flash发送）
            if (caCertLength > 0 && caCertLength <= MAX_CERT_SIZE) {
                PrintWifiData("[WIFI TX] 发送CA证书数据", pCaCertData, (caCertLength > 64) ? 64 : caCertLength, false);
                // 直接发送Flash数据，不使用临时缓冲区
                HAL_UART_Transmit(&huart1, (uint8_t*)pCaCertData, caCertLength, 5000);
                HAL_UART_Transmit(&huart1, (uint8_t*)&endChar, 1, 1000);
                PrintWifiData("[WIFI TX] CA证书数据发送完成", NULL, 0, true);
                
#if USE_FULL_MTLS_AUTH
                // 完整MTLS模式：继续配置客户端证书
                m_MTLSConfigState = MTLS_CONFIG_CLIENT_CERT;
#else
                // 仅根证书模式：直接跳到SSL选项配置
                m_MTLSConfigState = MTLS_CONFIG_SSL_OPT;
#endif
            } else {
                PrintWifiData("[WIFI ERROR] 无法发送CA证书，证书数据无效", NULL, 0, true);
            }
            break;
            
#if USE_FULL_MTLS_AUTH
        case MTLS_CONFIG_CLIENT_CERT:
            // 发送客户端证书
            SetSSLCert(1);
            m_MTLSConfigState = MTLS_CONFIG_CLIENT_CERT_WAIT; // 等待">"提示符
            break;
            
        case MTLS_CONFIG_CLIENT_CERT_WAIT:
            // 发送客户端证书数据（直接从Flash发送）
            if (clientCertLength > 0 && clientCertLength <= MAX_CERT_SIZE) {
                PrintWifiData("[WIFI TX] 发送客户端证书数据", pClientCertData, (clientCertLength > 64) ? 64 : clientCertLength, false);
                // 直接发送Flash数据，不使用临时缓冲区
                HAL_UART_Transmit(&huart1, (uint8_t*)pClientCertData, clientCertLength, 5000);
                HAL_UART_Transmit(&huart1, (uint8_t*)&endChar, 1, 1000);
                PrintWifiData("[WIFI TX] 客户端证书数据发送完成", NULL, 0, true);
                
                // 更新状态
                m_MTLSConfigState = MTLS_CONFIG_PRIVATE_KEY;
            } else {
                PrintWifiData("[WIFI ERROR] 无法发送客户端证书，证书数据无效", NULL, 0, true);
            }
            break;
            
        case MTLS_CONFIG_PRIVATE_KEY:
            // 发送私钥
            SetSSLCert(2);
            m_MTLSConfigState = MTLS_CONFIG_PRIVATE_KEY_WAIT; // 等待">"提示符
            break;
            
        case MTLS_CONFIG_PRIVATE_KEY_WAIT:
            // 发送私钥数据（直接从Flash发送）
            if (clientKeyLength > 0 && clientKeyLength <= MAX_CERT_SIZE) {
                PrintWifiData("[WIFI TX] 发送客户端私钥数据", pClientKeyData, (clientKeyLength > 16) ? 16 : clientKeyLength, false);
                // 直接发送Flash数据，不使用临时缓冲区
                HAL_UART_Transmit(&huart1, (uint8_t*)pClientKeyData, clientKeyLength, 5000);
                HAL_UART_Transmit(&huart1, (uint8_t*)&endChar, 1, 1000);
                PrintWifiData("[WIFI TX] 客户端私钥数据发送完成", NULL, 0, true);
                
                // 更新状态
                m_MTLSConfigState = MTLS_CONFIG_SSL_OPT;
            } else {
                PrintWifiData("[WIFI ERROR] 无法发送客户端私钥，私钥数据无效", NULL, 0, true);
            }
            break;
#else
        // 仅根证书模式：保持这些状态以便兼容性，但让它们直接进入SSL选项配置
        case MTLS_CONFIG_CLIENT_CERT:
        case MTLS_CONFIG_CLIENT_CERT_WAIT:
        case MTLS_CONFIG_PRIVATE_KEY:
        case MTLS_CONFIG_PRIVATE_KEY_WAIT:
            // 跳过客户端证书和私钥配置，直接进入SSL选项配置
            m_MTLSConfigState = MTLS_CONFIG_SSL_OPT;
            break;
#endif
            
        default:
            break;
    }
    m_MTLSConfigTimer = 0; // 重置计时器
}

// 处理MTLS配置
void CWifiUartModule::ProcessMTLSConfig()
{
    // 如果不需要配置MTLS，直接返回
    if (m_MTLSConfigState == MTLS_CONFIG_NONE || m_MTLSConfigState == MTLS_CONFIG_DONE)
        return;
    
    // 增加计时器，用于状态转换的延时
    m_MTLSConfigTimer += WIFI_SEND_TIME_CYCLE;
    
    // 根据当前状态处理MTLS配置
    switch (m_MTLSConfigState)
    {
        case MTLS_CONFIG_INIT:
            // 初始化MTLS配置，等待AP连接成功
            if (m_U8APConnectStatus == CONNECTED && m_MTLSConfigTimer >= 1000)
            {
                // AP连接成功，开始配置MTLS
                m_MTLSConfigState = MTLS_CONFIG_ROOT_CERT;
                m_MTLSConfigTimer = 0;
            }
            break;
            
        case MTLS_CONFIG_ROOT_CERT:
        case MTLS_CONFIG_CLIENT_CERT:
        case MTLS_CONFIG_PRIVATE_KEY:
        case MTLS_CONFIG_ROOT_CERT_WAIT:
        case MTLS_CONFIG_CLIENT_CERT_WAIT:
        case MTLS_CONFIG_PRIVATE_KEY_WAIT:
            // 加载证书
            if (m_MTLSConfigTimer >= 100) // 等待500ms
            {
                LoadMTLSCertificates();
            }
            break;
            
        case MTLS_CONFIG_SSL_OPT:
            // 配置SSL选项
            if (m_MTLSConfigTimer >= 100) // 等待500ms
            {
#if USE_FULL_MTLS_AUTH
                SetSSLOpt(1, 1, 1, 0); // id=1, isSSLRoot=1, isSSLClient=1, isSSLCrl=0
#else
                SetSSLOpt(1, 1, 0, 0); // id=1, isSSLRoot=1, isSSLClient=0, isSSLCrl=0
#endif
                m_MTLSConfigState = MTLS_CONFIG_CONNECT;
                m_MTLSConfigTimer = 0;
            }
            break;
            
        case MTLS_CONFIG_CONNECT:
            // 建立SSL连接
            if (m_MTLSConfigTimer >= 500) // 等待500ms
            {
                SetServerInfo(m_ServerIP[get_server_type()], m_ServerPort[get_server_type()], true); // 使用SSL连接
                m_MTLSConfigState = MTLS_CONFIG_DONE;
                m_MTLSConfigTimer = 0;
            }
            break;
            
        default:
            break;
    }
}

void CWifiUartModule::PrintWifiData(const char* prefix, const U8* data, U16 length, bool isString)
{
    #ifdef __USER_DEBUG
    printf("\r\n%s Len=%d: ", prefix, length);
    
    if(isString)
    {
        // 打印字符串形式
        for(U16 i = 0; i < length; i++)
        {
            if(data[i] >= 32 && data[i] <= 126) // 可打印字符
            {
                printf("%c", data[i]);
            }
            else if(data[i] == '\r')
            {
                printf("\\r");
            }
            else if(data[i] == '\n')
            {
                printf("\\n");
            }
            else
            {
                printf(".");
            }
        }
    }
    else
    {
        // 打印十六进制形式
        for(U16 i = 0; i < length; i++)
        {
            printf("%02X ", data[i]);
            if((i + 1) % 16 == 0)
            {
                printf("\r\n     ");
            }
        }
    }
    printf("\r\n");
    #endif
}
// 在WifiUartModule.c中添加函数实现
void CWifiUartModule::GetSSLCert(U8 type)
{
    if (type > 3) // 证书类型范围检查
        return;
        
    char cmd[32];
    sprintf(cmd, "AT+SSLCERTGET=%d\r\n", type);
    
    WIFICMDPKG sCmdPkg;
    memset(&sCmdPkg, 0, sizeof(WIFICMDPKG));
    sCmdPkg.U16CmdType = SSL_CERT_GET; // 需要在枚举中增加此类型
    sCmdPkg.U16CmdLen = strlen(cmd);
    sCmdPkg.U16RspWaitTime = 3000;
    memcpy(sCmdPkg.Pkg, cmd, sCmdPkg.U16CmdLen);
    
    PushCmdPkg(&sCmdPkg);
}

void CWifiUartModule::ProcessSSLCertGetResponse(const char* response, U16 length)
{
    if (response == NULL || length == 0)
        return;
        
    // 打印接收到的证书数据
    PrintWifiData("[WIFI RX] SSL certificate data received", (const U8*)response, length, true);
    
    // 检查响应是否包含+SSLCERTGET:cert
    if (FindContext((char*)response, length, "+SSLCERTGET:cert", strlen("+SSLCERTGET:cert")))
    {
        PrintWifiData("[WIFI INFO] SSL证书验证成功", NULL, 0, true);
    }
    else
    {
        PrintWifiData("[WIFI ERROR] SSL证书验证失败", NULL, 0, true);
    }
}
