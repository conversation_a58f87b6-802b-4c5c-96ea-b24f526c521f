#ifndef __MANAGERSETSCREEN_H__
#define __MANAGERSETSCREEN_H__

#include "GUI.h"
#include "DIALOG.h"
#include "WM.h"
#include "BUTTON.h"
#include "CHECKBOX.h"
#include "DROPDOWN.h"
#include "EDIT.h"
#include "FRAMEWIN.h"
#include "LISTBOX.h"
#include "MULTIEDIT.h"
#include "RADIO.h"
#include "SLIDER.h"
#include "TEXT.h"
#include "PROGBAR.h"
#include "SCROLLBAR.h"
#include "LISTVIEW.h"
#include "GRAPH.h"
#include "MENU.h"
#include "MULTIPAGE.h"
#include "ICONVIEW.h"
#include "TREEVIEW.h"
#include "MultiLanguage.h"
#include "DataType.h"

#define MAX_FOCUS_NUM_MANAGERSETTING_CHINA_VERSION      3
#define MAX_FOCUS_NUM_MANAGERSETTING_FOREIGN_VERSION    5

void RepaintManagerSettingScreen(void);
void ShowManagerSetScreen(uint8_t Flag);

#endif