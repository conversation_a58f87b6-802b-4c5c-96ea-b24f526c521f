#include "GlobalVariable.h"
#if (LCD_TYPE != LCD_5_TFT)
#include "ili9488.h"
#include "bsp_tft_lcd.h"
#include "main.h"

uint16_t g_ChipID = 0x9488;

uint16_t PIC_AddrIndex = 0;

void Init_9488(void);
static void ILI9488_SetDispWin(uint16_t _usX, uint16_t _usY, uint16_t _usHeight, uint16_t _usWidth);
static void ILI9488_QuitWinMode(void);
static void ILI9488_SetCursor(uint16_t _usX, uint16_t _usY);
static void ILI9488_WriteCmd(uint8_t _ucCmd);
static void ILI9488_WriteParam(uint16_t _ucParam);

void usrapi_set_display_win(uint16_t _usX, uint16_t _usY, uint16_t _usHeight, uint16_t _usWidth)
{
    ILI9488_SetDispWin(_usX, _usY, _usHeight, _usWidth);
}
void usrapi_quit_display_win(void)
{
    ILI9488_QuitWinMode();
}
void usrapi_set_cursor(uint16_t _usX, uint16_t _usY)
{
    ILI9488_SetCursor(_usX, _usY);
}
void usrapi_wirte_cmd(uint8_t _ucCmd)
{
    ILI9488_WriteCmd(_ucCmd);
}
void usrapi_wirte_param(uint8_t _ucParam)
{
    ILI9488_WriteParam(_ucParam);
}
/*
*********************************************************************************************************
*   函 数 名: ILI9488_InitHard
*   功能说明: 初始化LCD
*   形    参:  无
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_InitHard(void)
{
    uint32_t id;
    id = ILI9488_ReadID();
    if (id == 0x548066)
    {
        Init_9488();    /* 初始化5420和4001屏硬件 */
        //ILI9488_WriteCmd(0x23);
        //ILI9488_WriteCmd(0x22);
        //      s_RGBChgEn = 0;
        ILI9488_PutPixel(1, 1, 0x12);
        g_ChipID = ILI9488_GetPixel(1, 1);
        ILI9488_PutPixel(1, 1, 0x34);
        g_ChipID = ILI9488_GetPixel(1, 1);
        ILI9488_PutPixel(1, 1, 0x56);
        g_ChipID = ILI9488_GetPixel(1, 1);
        g_ChipID = 0x9488;
    }
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_SetDirection
*   功能说明: 设置显示方向。
*   形    参:  _ucDir : 显示方向代码 0 横屏正常, 1=横屏180度翻转, 2=竖屏, 3=竖屏180度翻转
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_SetDirection(uint8_t _ucDir)
{
    /*
        Memory Access Control (36h)
        This command defines read/write scanning direction of the frame memory.

        These 3 bits control the direction from the MPU to memory write/read.

        Bit  Symbol  Name  Description
        D7   MY  Row Address Order
        D6   MX  Column Address Order
        D5   MV  Row/Column Exchange
        D4   ML  Vertical Refresh Order  LCD vertical refresh direction control. 、

        D3   BGR RGB-BGR Order   Color selector switch control
             (0 = RGB color filter panel, 1 = BGR color filter panel )
        D2   MH  Horizontal Refresh ORDER  LCD horizontal refreshing direction control.
        D1   X   Reserved  Reserved
        D0   X   Reserved  Reserved
    */
    ILI9488_WriteCmd(0x36);
    /* 0 表示竖屏(排线在下)，1表示竖屏(排线在上), 2表示横屏(排线在左边)  3表示横屏 (排线在右边) */
    if (_ucDir == 0)
    {
        ILI9488_WriteParam(0xA8);   /* 横屏(排线在左边) */

    }
    else if (_ucDir == 1)
    {
        ILI9488_WriteParam(0x68);   /* 横屏 (排线在右边) */
    }
    else if (_ucDir == 2)
    {
        ILI9488_WriteParam(0xC8);   /* 竖屏(排线在上) */
    }
    else if (_ucDir == 3)
    {
        ILI9488_WriteParam(0x08);   /* 竖屏(排线在下) */
    }
}


/*
*********************************************************************************************************
*   函 数 名: Init_9488
*   功能说明: 初始化ILI9488驱动器
*   形    参:  无
*   返 回 值: 无
*********************************************************************************************************
*/
void Init_9488(void)
{
    //************* Start Initial Sequence **********//
    HAL_Delay(120);

#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
    ILI9488_WriteCmd(0xea);  ILI9488_WriteParam(0x00);
    ILI9488_WriteCmd(0xeb);  ILI9488_WriteParam(0x20);
    ILI9488_WriteCmd(0xec);  ILI9488_WriteParam(0x0C);
    ILI9488_WriteCmd(0xed);  ILI9488_WriteParam(0xc4);
    ILI9488_WriteCmd(0xe8);  ILI9488_WriteParam(0xC8);
    ILI9488_WriteCmd(0xe9);  ILI9488_WriteParam(0xC8);
    ILI9488_WriteCmd(0xf1);  ILI9488_WriteParam(0x01);
    ILI9488_WriteCmd(0xf2);  ILI9488_WriteParam(0x10);

    ILI9488_WriteCmd(0x2e);  ILI9488_WriteParam(0x86);
    ILI9488_WriteCmd(0x29);  ILI9488_WriteParam(0xff);
    ILI9488_WriteCmd(0xe4);  ILI9488_WriteParam(0x01);
    ILI9488_WriteCmd(0xe7);  ILI9488_WriteParam(0x01);

    //gamma 2.2 setting
    ILI9488_WriteCmd(0x40);  ILI9488_WriteParam(0x00);
    ILI9488_WriteCmd(0x41);  ILI9488_WriteParam(0x00);
    ILI9488_WriteCmd(0x42);  ILI9488_WriteParam(0x01);
    ILI9488_WriteCmd(0x43);  ILI9488_WriteParam(0x12);
    ILI9488_WriteCmd(0x44);  ILI9488_WriteParam(0x10);
    ILI9488_WriteCmd(0x45);  ILI9488_WriteParam(0x26);
    ILI9488_WriteCmd(0x46);  ILI9488_WriteParam(0x08);
    ILI9488_WriteCmd(0x47);  ILI9488_WriteParam(0x54);
    ILI9488_WriteCmd(0x48);  ILI9488_WriteParam(0x02);
    ILI9488_WriteCmd(0x49);  ILI9488_WriteParam(0x15);
    ILI9488_WriteCmd(0x4a);  ILI9488_WriteParam(0x19);
    ILI9488_WriteCmd(0x4b);  ILI9488_WriteParam(0x19);
    ILI9488_WriteCmd(0x4c);  ILI9488_WriteParam(0x16);

    ILI9488_WriteCmd(0x50);  ILI9488_WriteParam(0x19);
    ILI9488_WriteCmd(0x51);  ILI9488_WriteParam(0x2f);
    ILI9488_WriteCmd(0x52);  ILI9488_WriteParam(0x2d);
    ILI9488_WriteCmd(0x53);  ILI9488_WriteParam(0x3e);
    ILI9488_WriteCmd(0x54);  ILI9488_WriteParam(0x3f);
    ILI9488_WriteCmd(0x55);  ILI9488_WriteParam(0x3f);
    ILI9488_WriteCmd(0x56);  ILI9488_WriteParam(0x2b);
    ILI9488_WriteCmd(0x57);  ILI9488_WriteParam(0x77);
    ILI9488_WriteCmd(0x58);  ILI9488_WriteParam(0x09);
    ILI9488_WriteCmd(0x59);  ILI9488_WriteParam(0x06);
    ILI9488_WriteCmd(0x5a);  ILI9488_WriteParam(0x06);
    ILI9488_WriteCmd(0x5b);  ILI9488_WriteParam(0x0a);
    ILI9488_WriteCmd(0x5c);  ILI9488_WriteParam(0x1d);
    ILI9488_WriteCmd(0x5d);  ILI9488_WriteParam(0xcc);

    //power voltage setting
    ILI9488_WriteCmd(0x1b);  ILI9488_WriteParam(0x16);//VRH=4.65V 
    ILI9488_WriteCmd(0x1c);  ILI9488_WriteParam(0x06);
    ILI9488_WriteCmd(0x1a);  ILI9488_WriteParam(0x02);//BT (VGH~15V,VGL~-10V,DDVDH~5V) 
    ILI9488_WriteCmd(0x24);  ILI9488_WriteParam(0x57);//VMH(VCOM High voltage ~3.2V) );
    ILI9488_WriteCmd(0x25);  ILI9488_WriteParam(0x64);//VML(VCOM Low voltage -1.2V) 
      
    //vcom offset
    ILI9488_WriteCmd(0x23);  ILI9488_WriteParam(0x90);//0x79 92
    ILI9488_WriteCmd(0x2f);  ILI9488_WriteParam(0x01);
    //power on setting
    ILI9488_WriteCmd(0x18);  ILI9488_WriteParam(0x36);//0x36-Normal mode 75Hz 
    ILI9488_WriteCmd(0x19);  ILI9488_WriteParam(0x01);
    ILI9488_WriteCmd(0x1c);  ILI9488_WriteParam(0x06);

    ILI9488_WriteCmd(0x01);  ILI9488_WriteParam(0x00); 
    ILI9488_WriteCmd(0x1f);  ILI9488_WriteParam(0x88);
    HAL_Delay(5);
    ILI9488_WriteCmd(0x1f);  ILI9488_WriteParam(0x80);
    HAL_Delay(5);
    ILI9488_WriteCmd(0x1f);  ILI9488_WriteParam(0x90);
    HAL_Delay(5);
    ILI9488_WriteCmd(0x1f);  ILI9488_WriteParam(0xd0);
    HAL_Delay(5);
    //set 262/65k color
    ILI9488_WriteCmd(0x17);  ILI9488_WriteParam(0x05);//16-bit/pixel
    //set panel
    ILI9488_WriteCmd(0x36);  ILI9488_WriteParam(0x02); //02//00
    //display on setting
    ILI9488_WriteCmd(0x28);  ILI9488_WriteParam(0x38);
    HAL_Delay(40);
    ILI9488_WriteCmd(0x28);  ILI9488_WriteParam(0x3c);
    ILI9488_WriteCmd(0x16);  ILI9488_WriteParam(0x28);//竖屏
#elif (LCD_TYPE == LCD_28_TFT && LCD_28_TFT_TYPE == 1)
    //朋云 ST7789V
    ILI9488_WriteCmd(0x11);
      
    HAL_Delay(120);                //ms

    ILI9488_WriteCmd(0x36);
    ILI9488_WriteParam(0x00);

    ILI9488_WriteCmd(0x3A);
//    ILI9488_WriteParam(0x06);
    ILI9488_WriteParam(0x05);

    ILI9488_WriteCmd(0xB2);
    ILI9488_WriteParam(0x0C);
    ILI9488_WriteParam(0x0C);
    ILI9488_WriteParam(0x00);
    ILI9488_WriteParam(0x33);
    ILI9488_WriteParam(0x33);

    ILI9488_WriteCmd(0xB7);
    ILI9488_WriteParam(0x46);   //VGH=13.65V,VGL=-11.38V

    ILI9488_WriteCmd(0xBB);
    ILI9488_WriteParam(0x1B);

    ILI9488_WriteCmd(0xC0);
    ILI9488_WriteParam(0x2C);

    ILI9488_WriteCmd(0xC2);
    ILI9488_WriteParam(0x01);

    ILI9488_WriteCmd(0xC3);
    ILI9488_WriteParam(0x0F);

    ILI9488_WriteCmd(0xC4);
    ILI9488_WriteParam(0x20);

    ILI9488_WriteCmd(0xC6);
    ILI9488_WriteParam(0x0F);

    ILI9488_WriteCmd(0xD0);
    ILI9488_WriteParam(0xA4);
    ILI9488_WriteParam(0xA1);

    ILI9488_WriteCmd(0xD6);
    ILI9488_WriteParam(0xA1);

    ILI9488_WriteCmd(0xE0);
    ILI9488_WriteParam(0xF0);
    ILI9488_WriteParam(0x02);
    ILI9488_WriteParam(0x08);
    ILI9488_WriteParam(0x05);
    ILI9488_WriteParam(0x07);
    ILI9488_WriteParam(0x15);
    ILI9488_WriteParam(0x34);
    ILI9488_WriteParam(0x44);
    ILI9488_WriteParam(0x4B);
    ILI9488_WriteParam(0x37);
    ILI9488_WriteParam(0x13);
    ILI9488_WriteParam(0x13);
    ILI9488_WriteParam(0x30);
    ILI9488_WriteParam(0x37);

    ILI9488_WriteCmd(0xE1);
    ILI9488_WriteParam(0xF0);
    ILI9488_WriteParam(0x0B);
    ILI9488_WriteParam(0x0F);
    ILI9488_WriteParam(0x0F);
    ILI9488_WriteParam(0x0C);
    ILI9488_WriteParam(0x07);
    ILI9488_WriteParam(0x33);
    ILI9488_WriteParam(0x43);
    ILI9488_WriteParam(0x4A);
    ILI9488_WriteParam(0x38);
    ILI9488_WriteParam(0x14);
    ILI9488_WriteParam(0x15);
    ILI9488_WriteParam(0x31);
    ILI9488_WriteParam(0x35);

    ILI9488_WriteCmd(0x21);

    ILI9488_WriteCmd(0x29);

    ILI9488_WriteCmd(0x2C);

////////    //飞信达 ST7789T3
////////    ILI9488_WriteCmd(0x11);
////////    HAL_Delay(120);
////////    ILI9488_WriteCmd(0x36);  
////////    ILI9488_WriteParam(0x00);
//////////    ILI9488_SetDirection(0);    /* 横屏(排线在左边) */
////////    
////////    ILI9488_WriteCmd(0x3a);
////////    ILI9488_WriteParam(0x05);
////////    
////////    ILI9488_WriteCmd(0xb2);
////////    ILI9488_WriteParam(0x0c);
////////    ILI9488_WriteParam(0x0c);
////////    ILI9488_WriteParam(0x00);
////////    ILI9488_WriteParam(0x33);
////////    ILI9488_WriteParam(0x33);
////////    
////////    ILI9488_WriteCmd(0xb7);
////////    ILI9488_WriteParam(0x71);
////////    
////////    ILI9488_WriteCmd(0xbb);
////////    ILI9488_WriteParam(0x3b);
////////    
////////    ILI9488_WriteCmd(0xc0);
////////    ILI9488_WriteParam(0x2c);

////////    ILI9488_WriteCmd(0xc2);
////////    ILI9488_WriteParam(0x01);
////////    
////////    ILI9488_WriteCmd(0xc3);
////////    ILI9488_WriteParam(0x13);
////////    
////////    ILI9488_WriteCmd(0xc4);
////////    ILI9488_WriteParam(0x20);

////////    ILI9488_WriteCmd(0xc6);
////////    ILI9488_WriteParam(0x0f);
////////    
////////    ILI9488_WriteCmd(0xd0);
////////    ILI9488_WriteParam(0xa4);
////////    ILI9488_WriteParam(0xa1);
////////    
////////    ILI9488_WriteCmd(0xd6);
////////    ILI9488_WriteParam(0xa1);

////////    ILI9488_WriteCmd(0xe0);
////////    ILI9488_WriteParam(0xd0);
////////    ILI9488_WriteParam(0x08);
////////    ILI9488_WriteParam(0x0a);
////////    ILI9488_WriteParam(0x0d);
////////    ILI9488_WriteParam(0x0b);
////////    ILI9488_WriteParam(0x07);
////////    ILI9488_WriteParam(0x21);
////////    ILI9488_WriteParam(0x33);
////////    ILI9488_WriteParam(0x39);
////////    ILI9488_WriteParam(0x39);
////////    ILI9488_WriteParam(0x16);
////////    ILI9488_WriteParam(0x16);
////////    ILI9488_WriteParam(0x1F);
////////    ILI9488_WriteParam(0x3C);
////////    
////////    ILI9488_WriteCmd(0xe1);
////////    ILI9488_WriteParam(0xd0);
////////    ILI9488_WriteParam(0x00);
////////    ILI9488_WriteParam(0x03);
////////    ILI9488_WriteParam(0x01);
////////    ILI9488_WriteParam(0x00);
////////    ILI9488_WriteParam(0x10);
////////    ILI9488_WriteParam(0x21);
////////    ILI9488_WriteParam(0x32);
////////    ILI9488_WriteParam(0x38);
////////    ILI9488_WriteParam(0x16);
////////    ILI9488_WriteParam(0x14);
////////    ILI9488_WriteParam(0x14);
////////    ILI9488_WriteParam(0x20);
////////    ILI9488_WriteParam(0x3d);
////////    
////////    ILI9488_WriteCmd(0x21);
////////    ILI9488_WriteCmd(0x29);
#else
	/* Adjust Control 3 (F7h)  */
    ILI9488_WriteCmd(0XF7);
    ILI9488_WriteParam(0xA9);
    ILI9488_WriteParam(0x51);
    ILI9488_WriteParam(0x2C);
    ILI9488_WriteParam(0x82);   /* DSI write DCS command, use loose packet RGB 666 */

    /* Power Control 1 (C0h)  */
    ILI9488_WriteCmd(0xC0);
    ILI9488_WriteParam(0x19);
    ILI9488_WriteParam(0x19);

    /* Power Control 2 (C1h) */
    ILI9488_WriteCmd(0xC1);
    ILI9488_WriteParam(0x41);

    /* VCOM Control (C5h)  */
    ILI9488_WriteCmd(0XC5);
    ILI9488_WriteParam(0x00);
    ILI9488_WriteParam(0x0A);
    ILI9488_WriteParam(0x80);

    /* Frame Rate Control (In Normal Mode/Full Colors) (B1h) */
    ILI9488_WriteCmd(0xB1);
    ILI9488_WriteParam(0xB0);
    ILI9488_WriteParam(0x11);

    /* Display Inversion Control (B4h) */
    ILI9488_WriteCmd(0xB4);
    ILI9488_WriteParam(0x02);

    /* Display Function Control (B6h)  */
    ILI9488_WriteCmd(0xB6);
    ILI9488_WriteParam(0x02);
    ILI9488_WriteParam(0x22);

    /* Entry Mode Set (B7h)  */
    ILI9488_WriteCmd(0xB7);
    ILI9488_WriteParam(0xc6);

    /* HS Lanes Control (BEh) */
    ILI9488_WriteCmd(0xBE);
    ILI9488_WriteParam(0x00);
    ILI9488_WriteParam(0x04);

    /* Set Image Function (E9h)  */
    ILI9488_WriteCmd(0xE9);
    ILI9488_WriteParam(0x00);

    ILI9488_SetDirection(0);    /* 横屏(排线在左边) */

    /* Interface Pixel Format (3Ah) */
    ILI9488_WriteCmd(0x3A);
    ILI9488_WriteParam(0x55);   /* 0x55 : 16 bits/pixel  */

    /* PGAMCTRL (Positive Gamma Control) (E0h) */
    ILI9488_WriteCmd(0xE0);
    ILI9488_WriteParam(0x00);
    ILI9488_WriteParam(0x07);
    ILI9488_WriteParam(0x10);
    ILI9488_WriteParam(0x09);
    ILI9488_WriteParam(0x17);
    ILI9488_WriteParam(0x0B);
    ILI9488_WriteParam(0x41);
    ILI9488_WriteParam(0x89);
    ILI9488_WriteParam(0x4B);
    ILI9488_WriteParam(0x0A);
    ILI9488_WriteParam(0x0C);
    ILI9488_WriteParam(0x0E);
    ILI9488_WriteParam(0x18);
    ILI9488_WriteParam(0x1B);
    ILI9488_WriteParam(0x0F);

    /* NGAMCTRL (Negative Gamma Control) (E1h)  */
    ILI9488_WriteCmd(0XE1);
    ILI9488_WriteParam(0x00);
    ILI9488_WriteParam(0x17);
    ILI9488_WriteParam(0x1A);
    ILI9488_WriteParam(0x04);
    ILI9488_WriteParam(0x0E);
    ILI9488_WriteParam(0x06);
    ILI9488_WriteParam(0x2F);
    ILI9488_WriteParam(0x45);
    ILI9488_WriteParam(0x43);
    ILI9488_WriteParam(0x02);
    ILI9488_WriteParam(0x0A);
    ILI9488_WriteParam(0x09);
    ILI9488_WriteParam(0x32);
    ILI9488_WriteParam(0x36);
    ILI9488_WriteParam(0x0F);

    /* Sleep Out (11h */
    ILI9488_WriteCmd(0x11);
    HAL_Delay(120);    
    ILI9488_WriteCmd(0x29); /* Display ON (29h) */
#endif

    /* 设置显示窗口 */
    ILI9488_SetDispWin(0, 0, SCREEN_HEIGHT, SCREEN_WIDTH);

    ILI9488_ReadID();
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_WriteCmd
*   功能说明: 向LCD控制器芯片发送命令
*   形    参: _ucCmd : 命令代码
*   返 回 值: 无
*********************************************************************************************************
*/
static void ILI9488_WriteCmd(uint8_t _ucCmd)
{
    ILI9488_REG = _ucCmd;   /* 发送CMD */
}


/*
*********************************************************************************************************
*   函 数 名: ILI9488_WriteParam
*   功能说明: 向LCD控制器芯片发送参数(data)
*   形    参: _ucParam : 参数数据
*   返 回 值: 无
*********************************************************************************************************
*/
static void ILI9488_WriteParam(uint16_t _ucParam)
{
    ILI9488_RAM = _ucParam;
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_SetDispWin
*   功能说明: 设置显示窗口，进入窗口显示模式。TFT驱动芯片支持窗口显示模式，这是和一般的12864点阵LCD的最大区别。
*   形    参:
*       _usX : 水平坐标
*       _usY : 垂直坐标
*       _usHeight: 窗口高度
*       _usWidth : 窗口宽度
*   返 回 值: 无
*********************************************************************************************************
*/
#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
static void ILI9488_SetDispWin(uint16_t _usX, uint16_t _usY, uint16_t _usHeight, uint16_t _usWidth)
{
    ILI9488_WriteCmd(0X06);         /* 设置X坐标 */
    ILI9488_WriteParam(_usX >> 8);  /* 起始点 */
    ILI9488_WriteCmd(0X07);
    ILI9488_WriteParam(_usX);
    ILI9488_WriteCmd(0X08);
    ILI9488_WriteParam((_usX + _usWidth - 1) >> 8); /* 结束点 */
    ILI9488_WriteCmd(0X09);
    ILI9488_WriteParam(_usX + _usWidth - 1);

    ILI9488_WriteCmd(0X02);                   /* 设置Y坐标*/
    ILI9488_WriteParam(_usY >> 8);   /* 起始点 */
    ILI9488_WriteCmd(0X03);
    ILI9488_WriteParam(_usY);
    ILI9488_WriteCmd(0X04);
    ILI9488_WriteParam((_usY + _usHeight - 1) >> 8);        /* 结束点 */
    ILI9488_WriteCmd(0X05);
    ILI9488_WriteParam((_usY + _usHeight - 1));
}
#else
static void ILI9488_SetDispWin(uint16_t _usX, uint16_t _usY, uint16_t _usHeight, uint16_t _usWidth)
{
    ILI9488_WriteCmd(0X2A);         /* 设置X坐标 */
    ILI9488_WriteParam(_usX >> 8);  /* 起始点 */
    ILI9488_WriteParam(_usX);
    ILI9488_WriteParam((_usX + _usWidth - 1) >> 8); /* 结束点 */
    ILI9488_WriteParam(_usX + _usWidth - 1);

    ILI9488_WriteCmd(0X2B);                   /* 设置Y坐标*/
    ILI9488_WriteParam(_usY >> 8);   /* 起始点 */
    ILI9488_WriteParam(_usY);
    ILI9488_WriteParam((_usY + _usHeight - 1) >> 8);        /* 结束点 */
    ILI9488_WriteParam((_usY + _usHeight - 1));
}
#endif

/*
*********************************************************************************************************
*   函 数 名: ILI9488_SetCursor
*   功能说明: 设置光标位置
*   形    参:  _usX : X坐标; _usY: Y坐标
*   返 回 值: 无
*********************************************************************************************************
*/
#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
static void ILI9488_SetCursor(uint16_t _usX, uint16_t _usY)
{
    ILI9488_WriteCmd(0X06);         /* 设置X坐标 */
    ILI9488_WriteParam(_usX >> 8);  /* 先高8位，然后低8位 */
    ILI9488_WriteCmd(0X07);
    ILI9488_WriteParam(_usX);       /* 设置起始点和结束点*/
    ILI9488_WriteCmd(0X08);
    ILI9488_WriteParam(_usX >> 8);  /* 先高8位，然后低8位 */
    ILI9488_WriteCmd(0X09);
    ILI9488_WriteParam(_usX);       /* 设置起始点和结束点*/

    ILI9488_WriteCmd(0X02);         /* 设置Y坐标*/
    ILI9488_WriteParam(_usY >> 8);
    ILI9488_WriteCmd(0X03); 
    ILI9488_WriteParam(_usY);
    ILI9488_WriteCmd(0X04); 
    ILI9488_WriteParam(_usY >> 8);
    ILI9488_WriteCmd(0X05); 
    ILI9488_WriteParam(_usY);
}
#else
static void ILI9488_SetCursor(uint16_t _usX, uint16_t _usY)
{
    ILI9488_WriteCmd(0X2A);         /* 设置X坐标 */
    ILI9488_WriteParam(_usX >> 8);  /* 先高8位，然后低8位 */
    ILI9488_WriteParam(_usX);       /* 设置起始点和结束点*/
    ILI9488_WriteParam(_usX >> 8);  /* 先高8位，然后低8位 */
    ILI9488_WriteParam(_usX);       /* 设置起始点和结束点*/

    ILI9488_WriteCmd(0X2B);         /* 设置Y坐标*/
    ILI9488_WriteParam(_usY >> 8);
    ILI9488_WriteParam(_usY);
    ILI9488_WriteParam(_usY >> 8);
    ILI9488_WriteParam(_usY);
}
#endif

/*
*********************************************************************************************************
*   函 数 名: ILI9488_QuitWinMode
*   功能说明: 退出窗口显示模式，变为全屏显示模式
*   形    参:  无
*   返 回 值: 无
*********************************************************************************************************
*/
static void ILI9488_QuitWinMode(void)
{
    ILI9488_SetDispWin(0, 0, SCREEN_HEIGHT, SCREEN_WIDTH);
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_ReadID
*   功能说明: 读取LCD驱动芯片ID， 4个bit
*   形    参:  无
*   返 回 值: 无
*********************************************************************************************************
*/
uint32_t ILI9488_ReadID(void)
{
    uint8_t buf[4];
#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
    ILI9488_REG = 0x00;
#else
    ILI9488_REG = 0x04;
#endif
    buf[0] = ILI9488_RAM;
    buf[1] = ILI9488_RAM;
    buf[2] = ILI9488_RAM;
    buf[3] = ILI9488_RAM;

    return (buf[1] << 16) + (buf[2] << 8) + buf[3];
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_DispOn
*   功能说明: 打开显示
*   形    参:  无
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DispOn(void)
{
    ;
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_DispOff
*   功能说明: 关闭显示
*   形    参:  无
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DispOff(void)
{
    ;
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_ClrScr
*   功能说明: 根据输入的颜色值清屏
*   形    参:  _usColor : 背景色
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_ClrScr(uint16_t _usColor)
{
    uint32_t i;
    uint32_t n;

    ILI9488_SetDispWin(0, 0, SCREEN_HEIGHT, SCREEN_WIDTH);
#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
    ILI9488_REG = 0x22;             /* 准备读写显存 */
#else
    ILI9488_REG = 0x2C;             /* 准备读写显存 */
#endif

    #if 1       /* 优化代码执行速度 */
    n = (SCREEN_HEIGHT * SCREEN_WIDTH) / 8;
    for (i = 0; i < n; i++)
    {
        ILI9488_RAM = _usColor;
        ILI9488_RAM = _usColor;
        ILI9488_RAM = _usColor;
        ILI9488_RAM = _usColor;

        ILI9488_RAM = _usColor;
        ILI9488_RAM = _usColor;
        ILI9488_RAM = _usColor;
        ILI9488_RAM = _usColor;
    }
    #else
    n = SCREEN_HEIGHT * SCREEN_WIDTH;
    for (i = 0; i < n; i++)
    {
        ILI9488_RAM = _usColor;
    }
    #endif

}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_PutPixel
*   功能说明: 画1个像素
*   形    参:
*           _usX,_usY : 像素坐标
*           _usColor  ：像素颜色
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_PutPixel(uint16_t _usX, uint16_t _usY, uint16_t _usColor)
{
    ILI9488_SetCursor(_usX, _usY);  /* 设置光标位置 */

    /* 写显存 */
#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
    ILI9488_REG = 0x22;
#else
	ILI9488_REG = 0x2C;
#endif
    ILI9488_RAM = _usColor;
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_GetPixel
*   功能说明: 读取1个像素
*   形    参:
*           _usX,_usY : 像素坐标
*           _usColor  ：像素颜色
*   返 回 值: RGB颜色值 （565）
*********************************************************************************************************
*/
uint16_t ILI9488_GetPixel(uint16_t _usX, uint16_t _usY)
{
    uint16_t R = 0, G = 0, B = 0 ;

    ILI9488_SetCursor(_usX, _usY);  /* 设置光标位置 */

#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
    ILI9488_REG = 0x22;
#else
    ILI9488_REG = 0x2E;
#endif
    R = ILI9488_RAM;    /* 第1个哑读，丢弃 */
    R = ILI9488_RAM;
    B = ILI9488_RAM;
    G = (R & 0x00FF) << 8;

    return (((R >> 11) << 11) | ((G >> 10 ) << 5) | (B >> 11));
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_DrawLine
*   功能说明: 采用 Bresenham 算法，在2点间画一条直线。
*   形    参:
*           _usX1, _usY1 ：起始点坐标
*           _usX2, _usY2 ：终止点Y坐标
*           _usColor     ：颜色
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DrawLine(uint16_t _usX1, uint16_t _usY1, uint16_t _usX2, uint16_t _usY2, uint16_t _usColor)
{
    int32_t dx, dy ;
    int32_t tx, ty ;
    int32_t inc1, inc2 ;
    int32_t d, iTag ;
    int32_t x, y ;

    /* 采用 Bresenham 算法，在2点间画一条直线 */

    ILI9488_PutPixel(_usX1, _usY1, _usColor);

    /* 如果两点重合，结束后面的动作。*/
    if ( _usX1 == _usX2 && _usY1 == _usY2 )
    {
        return;
    }

    iTag = 0 ;
    /* dx = abs ( _usX2 - _usX1 ); */
    if (_usX2 >= _usX1)
    {
        dx = _usX2 - _usX1;
    }
    else
    {
        dx = _usX1 - _usX2;
    }

    /* dy = abs ( _usY2 - _usY1 ); */
    if (_usY2 >= _usY1)
    {
        dy = _usY2 - _usY1;
    }
    else
    {
        dy = _usY1 - _usY2;
    }

    if ( dx < dy )   /*如果dy为计长方向，则交换纵横坐标。*/
    {
        uint16_t temp;

        iTag = 1 ;
        temp = _usX1;
        _usX1 = _usY1;
        _usY1 = temp;
        temp = _usX2;
        _usX2 = _usY2;
        _usY2 = temp;
        temp = dx;
        dx = dy;
        dy = temp;
    }
    tx = _usX2 > _usX1 ? 1 : -1 ;    /* 确定是增1还是减1 */
    ty = _usY2 > _usY1 ? 1 : -1 ;
    x = _usX1 ;
    y = _usY1 ;
    inc1 = 2 * dy ;
    inc2 = 2 * ( dy - dx );
    d = inc1 - dx ;
    while ( x != _usX2 )     /* 循环画点 */
    {
        if ( d < 0 )
        {
            d += inc1 ;
        }
        else
        {
            y += ty ;
            d += inc2 ;
        }
        if ( iTag )
        {
            ILI9488_PutPixel ( y, x, _usColor) ;
        }
        else
        {
            ILI9488_PutPixel ( x, y, _usColor) ;
        }
        x += tx ;
    }
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_DrawHLine
*   功能说明: 绘制一条水平线 （主要用于UCGUI的接口函数）
*   形    参:  _usX1    ：起始点X坐标
*             _usY1    ：水平线的Y坐标
*             _usX2    ：结束点X坐标
*             _usColor : 颜色
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DrawHLine(uint16_t _usX1, uint16_t _usY1, uint16_t _usX2, uint16_t _usColor)
{
    uint16_t i;

    ILI9488_SetDispWin(_usX1, _usY1, 1, _usX2 - _usX1 + 1);

#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
    ILI9488_REG = 0x22;
#else
    ILI9488_REG = 0x2C;
#endif

    /* 写显存 */
    for (i = 0; i < _usX2 - _usX1 + 1; i++)
    {
        ILI9488_RAM = _usColor;
    }
}

/*
*********************************************************************************************************
*   函 数 名: LCD9341_DrawVLine
*   功能说明: 画垂直平线 用UCGUI的接口函数
*   形    参: X,Y的坐标和颜色
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DrawVLine(uint16_t _usX1, uint16_t _usY1, uint16_t _usY2, uint16_t _usColor)
{
    uint16_t i;

    for (i = _usY1; i <= _usY2; i++)
    {
        ILI9488_PutPixel(_usX1, i, _usColor);
    }
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_DrawHColorLine
*   功能说明: 绘制一条彩色水平线 （主要用于UCGUI的接口函数）
*   形    参:  _usX1    ：起始点X坐标
*             _usY1    ：水平线的Y坐标
*             _usWidth ：直线的宽度
*             _pColor : 颜色缓冲区
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DrawHColorLine(uint16_t _usX1, uint16_t _usY1, uint16_t _usWidth, const uint16_t *_pColor)
{
    uint16_t i;

    ILI9488_SetDispWin(_usX1, _usY1, 1, _usWidth);

#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
    ILI9488_REG = 0x22;
#else
    ILI9488_REG = 0x2C;
#endif

    /* 写显存 */
    for (i = 0; i < _usWidth; i++)
    {
        ILI9488_RAM = *_pColor++;
    }
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_DrawHTransLine
*   功能说明: 绘制一条彩色透明的水平线 （主要用于UCGUI的接口函数）， 颜色值为0表示透明色
*   形    参:  _usX1    ：起始点X坐标
*             _usY1    ：水平线的Y坐标
*             _usWidth ：直线的宽度
*             _pColor : 颜色缓冲区
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DrawHTransLine(uint16_t _usX1, uint16_t _usY1, uint16_t _usWidth, const uint16_t *_pColor)
{
    uint16_t i, j;
    uint16_t Index;

    ILI9488_SetCursor(_usX1, _usY1);

    /* 写显存 */
#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
    ILI9488_REG = 0x22;
#else
    ILI9488_REG = 0x2C;
#endif
    for (i = 0, j = 0; i < _usWidth; i++, j++)
    {
        Index = *_pColor++;
        if (Index)
        {
            ILI9488_RAM = Index;
        }
        else
        {
            ILI9488_SetCursor(_usX1 + j, _usY1);
            #if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
			    ILI9488_REG = 0x22;
			#else
			    ILI9488_REG = 0x2C;
			#endif
            ILI9488_RAM = Index;
        }
    }
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_DrawRect
*   功能说明: 绘制水平放置的矩形。
*   形    参:
*           _usX,_usY：矩形左上角的坐标
*           _usHeight ：矩形的高度
*           _usWidth  ：矩形的宽度
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DrawRect(uint16_t _usX, uint16_t _usY, uint16_t _usHeight, uint16_t _usWidth, uint16_t _usColor)
{
    /*
     ---------------->---
    |(_usX，_usY)        |
    V                    V  _usHeight
    |                    |
     ---------------->---
          _usWidth
    */

    ILI9488_DrawLine(_usX, _usY, _usX + _usWidth - 1, _usY, _usColor);  /* 顶 */
    ILI9488_DrawLine(_usX, _usY + _usHeight - 1, _usX + _usWidth - 1, _usY + _usHeight - 1, _usColor);  /* 底 */

    ILI9488_DrawLine(_usX, _usY, _usX, _usY + _usHeight - 1, _usColor); /* 左 */
    ILI9488_DrawLine(_usX + _usWidth - 1, _usY, _usX + _usWidth - 1, _usY + _usHeight, _usColor);   /* 右 */
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_FillRect
*   功能说明: 填充矩形。
*   形    参:
*           _usX,_usY：矩形左上角的坐标
*           _usHeight ：矩形的高度
*           _usWidth  ：矩形的宽度
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_FillRect(uint16_t _usX, uint16_t _usY, uint16_t _usHeight, uint16_t _usWidth, uint16_t _usColor)
{
    uint32_t i;

    /*
     ---------------->---
    |(_usX，_usY)        |
    V                    V  _usHeight
    |                    |
     ---------------->---
          _usWidth
    */

    ILI9488_SetDispWin(_usX, _usY, _usHeight, _usWidth);

#if ((LCD_TYPE == LCD_28_TFT) && (LCD_28_TFT_TYPE == 0))
    ILI9488_REG = 0x22;
#else
    ILI9488_REG = 0x2C;
#endif
    for (i = 0; i < _usHeight * _usWidth; i++)
    {
        ILI9488_RAM = _usColor;
    }
}


/*
*********************************************************************************************************
*   函 数 名: ILI9488_DrawCircle
*   功能说明: 绘制一个圆，笔宽为1个像素
*   形    参:
*           _usX,_usY  ：圆心的坐标
*           _usRadius  ：圆的半径
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DrawCircle(uint16_t _usX, uint16_t _usY, uint16_t _usRadius, uint16_t _usColor)
{
    int32_t  D;         /* Decision Variable */
    uint32_t  CurX;     /* 当前 X 值 */
    uint32_t  CurY;     /* 当前 Y 值 */

    D = 3 - (_usRadius << 1);
    CurX = 0;
    CurY = _usRadius;

    while (CurX <= CurY)
    {
        ILI9488_PutPixel(_usX + CurX, _usY + CurY, _usColor);
        ILI9488_PutPixel(_usX + CurX, _usY - CurY, _usColor);
        ILI9488_PutPixel(_usX - CurX, _usY + CurY, _usColor);
        ILI9488_PutPixel(_usX - CurX, _usY - CurY, _usColor);
        ILI9488_PutPixel(_usX + CurY, _usY + CurX, _usColor);
        ILI9488_PutPixel(_usX + CurY, _usY - CurX, _usColor);
        ILI9488_PutPixel(_usX - CurY, _usY + CurX, _usColor);
        ILI9488_PutPixel(_usX - CurY, _usY - CurX, _usColor);

        if (D < 0)
        {
            D += (CurX << 2) + 6;
        }
        else
        {
            D += ((CurX - CurY) << 2) + 10;
            CurY--;
        }
        CurX++;
    }
}

/*
*********************************************************************************************************
*   函 数 名: ILI9488_DrawBMP
*   功能说明: 在LCD上显示一个BMP位图，位图点阵扫描次序：从左到右，从上到下
*   形    参:
*           _usX, _usY : 图片的坐标
*           _usHeight  ：图片高度
*           _usWidth   ：图片宽度
*           _ptr       ：图片点阵指针
*   返 回 值: 无
*********************************************************************************************************
*/
void ILI9488_DrawBMP(uint16_t _usX, uint16_t _usY, uint16_t _usHeight, uint16_t _usWidth, uint16_t *_ptr)
{
    uint32_t index = 0;
    const uint16_t *p;

    /* 设置图片的位置和大小， 即设置显示窗口 */
    ILI9488_SetDispWin(_usX, _usY, _usHeight, _usWidth);

    p = _ptr;
    for (index = 0; index < _usHeight * _usWidth; index++)
    {
        ILI9488_PutPixel(_usX, _usY, *p++);
    }

    /* 退出窗口绘图模式 */
    ILI9488_QuitWinMode();
}

#endif

