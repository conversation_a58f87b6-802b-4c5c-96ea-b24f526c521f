/*********************************************************************
*                                                                    *
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
*                                                                    *
**********************************************************************
*                                                                    *
* C-file generated by:                                               *
*                                                                    *
*        GUI_Builder for emWin version 5.32                          *
*        Compiled Oct  8 2015, 11:59:02                              *
*        (c) 2015 Segger Microcontroller GmbH & Co. KG               *
*                                                                    *
**********************************************************************
*                                                                    *
*        Internet: www.segger.com  Support: <EMAIL>       *
*                                                                    *
**********************************************************************
*/

// USER START (Optionally insert additional includes)
#include "StaterBar.h"
#include "Key.h"
#include "ConfigSave.h"
#include "bsp_bldcm_control.h"
#include "SignalCondition.h"
#include "GlobalVariable.h"
#include "CalcDateTime.h"
#include "Filter.h"
#include "FlowDispose.h"
#include "stack.h"
#include "FlowSensorType.h"
// USER END
#include "DIALOG.h"
#include "AutoFlowCali.h"

typedef struct
{
    bool bPWMAddOrFlag;
} FLOW_TIT;
static FLOW_TIT FlowTitFlow;
/*********************************************************************
*
*       Defines
*
**********************************************************************
*/
#define ID_WINDOW_0         (GUI_ID_USER + 0x00)
#define ID_BUTTON_0         (GUI_ID_USER + 0x01)
#define ID_BUTTON_1         (GUI_ID_USER + 0x02)
#define ID_BUTTON_2         (GUI_ID_USER + 0x03)
#define ID_BUTTON_3         (GUI_ID_USER + 0x04)
#define ID_BUTTON_4         (GUI_ID_USER + 0x05)
#define ID_BUTTON_5         (GUI_ID_USER + 0x06)
#define ID_BUTTON_6         (GUI_ID_USER + 0x07)
#define ID_BUTTON_7         (GUI_ID_USER + 0x08)
#define ID_BUTTON_8         (GUI_ID_USER + 0x09)
#define ID_BUTTON_9         (GUI_ID_USER + 0x0A)
#define ID_BUTTON_10        (GUI_ID_USER + 0x0B)
#define BUTTON_NUM_MAX      (0x0B)
#define ID_TEXT_0           (GUI_ID_USER + BUTTON_NUM_MAX + 0x01)
#define ID_TEXT_1           (GUI_ID_USER + BUTTON_NUM_MAX + 0x02)
#define ID_TEXT_2           (GUI_ID_USER + BUTTON_NUM_MAX + 0x03)
#define ID_TEXT_3           (GUI_ID_USER + BUTTON_NUM_MAX + 0x04)
#define ID_TEXT_4           (GUI_ID_USER + BUTTON_NUM_MAX + 0x05)
#define ID_TEXT_5           (GUI_ID_USER + BUTTON_NUM_MAX + 0x06)
#define ID_TEXT_6           (GUI_ID_USER + BUTTON_NUM_MAX + 0x07)
#define ID_TEXT_7           (GUI_ID_USER + BUTTON_NUM_MAX + 0x08)
#define ID_TEXT_8           (GUI_ID_USER + BUTTON_NUM_MAX + 0x09)
#define ID_TEXT_9           (GUI_ID_USER + BUTTON_NUM_MAX + 0x0A)
#define ID_TEXT_10          (GUI_ID_USER + BUTTON_NUM_MAX + 0x0B)
#define ID_TEXT_11          (GUI_ID_USER + BUTTON_NUM_MAX + 0x0C)
#define ID_TEXT_12          (GUI_ID_USER + BUTTON_NUM_MAX + 0x0D)
#define ID_TEXT_13          (GUI_ID_USER + BUTTON_NUM_MAX + 0x0E)
#define ID_TEXT_14          (GUI_ID_USER + BUTTON_NUM_MAX + 0x0F)
#define ID_TEXT_15          (GUI_ID_USER + BUTTON_NUM_MAX + 0x10)
#define ID_TEXT_16          (GUI_ID_USER + BUTTON_NUM_MAX + 0x11)
#define ID_TEXT_17  		(GUI_ID_USER + BUTTON_NUM_MAX + 0x12)
#define ID_TEXT_18  		(GUI_ID_USER + BUTTON_NUM_MAX + 0x13)
#define TEXT_NUM_MAX 18

// USER START (Optionally insert additional defines)
static void ShowProcess(void);
WINDOWINFO g_FlowCaliWInfo = {NULL, NULL, NULL, NULL, ShowProcess, NULL, 0, 0, FLOWCALI_ID};

void AddBldcmPWM(U8 Flag, U8 Value);

WM_HWIN g_FlowCaliHwin = NULL;
static GUI_TIMER_HANDLE g_RefreshTimer = NULL;
static U8 g_U8FlowCaliType = 0;

static void RefreshTimerTask(void)
{
    WM_HWIN hItem;
    char str[32];

    if(FlowTitFlow.bPWMAddOrFlag == TRUE || ((GetTitFlowCode() + 400) <= GetRealFlowCode()))
    {
        FlowTitFlow.bPWMAddOrFlag = FALSE;
        ClearFilterData(TIT_FLWO_CODE, GetRealFlowCode());
    }
    GUI_TIMER_Restart(g_RefreshTimer);

    hItem = WM_GetDialogItem(g_FlowCaliHwin, ID_TEXT_3); //PWM
    sprintf(str, "%d", GetBldcmPwm());
    TEXT_SetText(hItem, str);

    hItem = WM_GetDialogItem(g_FlowCaliHwin, ID_TEXT_5); //Flow
    sprintf(str, "%d", GetTitFlowCode());
    TEXT_SetText(hItem, str);

    hItem = WM_GetDialogItem(g_FlowCaliHwin, ID_TEXT_7); //Speed
    sprintf(str, "%d", GetMotorSpeed());
    TEXT_SetText(hItem, str);

    hItem = WM_GetDialogItem(g_FlowCaliHwin, ID_TEXT_9); //Phy Flow
    sprintf(str, "%.1f", ((float)GetFlow()) / (float)10.0);
    TEXT_SetText(hItem, str);
}

static void CreateRefreshTimer(void)
{
    GUI_TIMER_CREATE(g_RefreshTimer, RefreshTimerTask, REFRESH_FLOW_CALI_VALUE_TIMER_ID, 0, 0);
    GUI_TIMER_SetPeriod(g_RefreshTimer, (GUI_TIMER_TIME)(200 / (1000 / OS_CFG_TICK_RATE_HZ)));
    GUI_TIMER_Restart(g_RefreshTimer);
}
// USER END

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/

// USER START (Optionally insert additional static data)
// USER END

/*********************************************************************
*
*       _aDialogCreate
*/
#if (LCD_TYPE == LCD_5_TFT)
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] =
{
    { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 70, 800, 410, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Add1", ID_BUTTON_0, 400, 100, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Add5", ID_BUTTON_1, 540, 100, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Add10", ID_BUTTON_2, 680, 100, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Add50", ID_BUTTON_3, 400, 160, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Red1", ID_BUTTON_4, 540, 160, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Red5", ID_BUTTON_5, 680, 160, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Red10", ID_BUTTON_6, 400, 220, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Red50", ID_BUTTON_7, 540, 220, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "OK", ID_BUTTON_8, 400, 280, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Prev", ID_BUTTON_9, 540, 280, 100, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Exit", ID_BUTTON_10, 680, 280, 100, 40, 0, 0x0, 0 },
    { TEXT_CreateIndirect, "FlowCali", ID_TEXT_0, 0, 0, 800, 40, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "Prompt", ID_TEXT_1, 8, 40, 789, 28, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "PWM", ID_TEXT_2, 3, 70, 103, 28, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "PWMValue", ID_TEXT_3, 110, 70, 80, 28, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "Flow", ID_TEXT_4, 3, 98, 103, 28, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "FlowValue", ID_TEXT_5, 110, 98, 120, 28, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "Speed", ID_TEXT_6, 200, 70, 75, 28, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "SpeedValue", ID_TEXT_7, 280, 70, 80, 28, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "PhyFlow", ID_TEXT_8, 3, 126, 110, 28, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "PhyFlowValue", ID_TEXT_9, 110, 126, 80, 28, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "FlowUnit", ID_TEXT_10, 200, 126, 100, 28, 0, 0x64, 0 },
    //ADP801定标界面
    { TEXT_CreateIndirect, "F0Info", ID_TEXT_11, 5, 170, 350, 30, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "F30Info", ID_TEXT_12, 5, 210, 350, 30, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "F80Info", ID_TEXT_13, 5, 250, 350, 30, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "F140Info", ID_TEXT_14, 5, 290, 350, 30, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "CaliInfo", ID_TEXT_15, 5, 330, 350, 30, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "CaliErrFlag", ID_TEXT_16, 5, 370, 350, 30, 0, 0x64, 0 },
	{ TEXT_CreateIndirect, "FlowSensorType", ID_TEXT_17, 400, 55, 150, 30, 0, 0x64, 0 },
	{ TEXT_CreateIndirect, "FlowSensorTypeValue", ID_TEXT_18, 550, 55, 150, 30, 0, 0x64, 0 },
//  //SDP810定标界面
//  { TEXT_CreateIndirect, "F30Info", ID_TEXT_11, 5, 200, 350, 30, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "F80Info", ID_TEXT_12, 5, 240, 350, 30, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "F120Info", ID_TEXT_13, 5, 280, 350, 30, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "CaliInfo", ID_TEXT_14, 5, 320, 350, 30, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "CaliErrFlag", ID_TEXT_15, 5, 360, 350, 30, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "", ID_TEXT_16, 5, 400, 350, 30, 0, 0x64, 0 },
    // USER START (Optionally insert additional widgets)
    // USER END
};
#else
#if (LCD_TYPE == LCD_28_TFT)
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] =
{
    { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 53, 240, 320 - 53, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Add1", ID_BUTTON_0, 10, 177, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Add5", ID_BUTTON_1, 90, 177, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Add10", ID_BUTTON_2, 170, 177, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Add50", ID_BUTTON_3, 10, 200, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Red1", ID_BUTTON_4, 90, 200, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Red5", ID_BUTTON_5, 170, 200, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Red10", ID_BUTTON_6, 10, 223, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Red50", ID_BUTTON_7, 90, 223, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "OK", ID_BUTTON_8, 10, 246, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Prev", ID_BUTTON_9, 90, 246, 60, 21, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Exit", ID_BUTTON_10, 170, 246, 60, 21, 0, 0x0, 0 },
//  { TEXT_CreateIndirect, "Prompt", ID_TEXT_1, 3, 20, 70, 18, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "PWM", ID_TEXT_2, 2, 20, 61, 18, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "PWMValue", ID_TEXT_3, 66, 20, 48, 18, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "Flow", ID_TEXT_4, 2, 40, 61, 18, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "FlowValue", ID_TEXT_5, 66, 40, 72, 18, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "Speed", ID_TEXT_6, 120, 20, 45, 18, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "SpeedValue", ID_TEXT_7, 168, 20, 48, 18, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "PhyFlow", ID_TEXT_8, 2, 60, 80, 18, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "PhyFlowValue", ID_TEXT_9, 88, 60, 50, 18, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "FlowUnit", ID_TEXT_10, 140, 60, 60, 18, 0, 0x64, 0 },

    { TEXT_CreateIndirect, "F10Info", ID_TEXT_11, 3, 80, 210, 20, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "F30Info", ID_TEXT_12, 3, 100, 210, 20, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "F80Info", ID_TEXT_13, 3, 120, 210, 20, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "F160Info", ID_TEXT_14, 3, 140, 210, 20, 0, 0x64, 0 },

    { TEXT_CreateIndirect, "CaliInfo", ID_TEXT_15, 0, 160, 240, 20, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "CaliErrFlag", ID_TEXT_16, 140, 80, 155, 18, 0, 0x64, 0 },
	{ TEXT_CreateIndirect, "FlowSensorType", ID_TEXT_17, 120, 40, 60, 18, 0, 0x64, 0 },
	{ TEXT_CreateIndirect, "FlowSensorTypeValue", ID_TEXT_18, 180, 40, 90, 18, 0, 0x64, 0 },
    // USER START (Optionally insert additional widgets)
    // USER END
#else
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] = {
  { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 46, 480, 320 - 46, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Add1", ID_BUTTON_0, 240, 66, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Add5", ID_BUTTON_1, 324, 66, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Add10", ID_BUTTON_2, 408, 66, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Add50", ID_BUTTON_3, 240, 106, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Red1", ID_BUTTON_4, 324, 106, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Red5", ID_BUTTON_5, 408, 106, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Red10", ID_BUTTON_6, 240, 146, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Red50", ID_BUTTON_7, 324, 146, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "OK", ID_BUTTON_8, 240, 186, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Prev", ID_BUTTON_9, 324, 186, 60, 26, 0, 0x0, 0 },
  { BUTTON_CreateIndirect, "Exit", ID_BUTTON_10, 408, 186, 60, 26, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "FlowCali", ID_TEXT_0, 0, 0, 480, 26, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Prompt", ID_TEXT_1, 4, 33, 473, 18, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PWM", ID_TEXT_2, 2, 66, 61, 18, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PWMValue", ID_TEXT_3, 66, 66, 48, 18, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Flow", ID_TEXT_4, 2, 85, 61, 18, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "FlowValue", ID_TEXT_5, 66, 85, 72, 18, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "Speed", ID_TEXT_6, 120, 66, 45, 18, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SpeedValue", ID_TEXT_7, 168, 66, 48, 18, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PhyFlow", ID_TEXT_8, 2, 104, 80, 18, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PhyFlowValue", ID_TEXT_9, 90, 104, 50, 18, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "FlowUnit", ID_TEXT_10, 140, 104, 60, 18, 0, 0x64, 0 },
  
  { TEXT_CreateIndirect, "F0Info", ID_TEXT_11, 3, 133, 210, 20, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "F30Info", ID_TEXT_12, 3, 158, 210, 20, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "F80Info", ID_TEXT_13, 3, 183, 210, 20, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "F140Info", ID_TEXT_14, 3, 208, 210, 20, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "CaliInfo", ID_TEXT_15, 3, 232, 250, 20, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "CaliErrFlag", ID_TEXT_16, 3, 256, 250, 20, 0, 0x64, 0 },
  // USER START (Optionally insert additional widgets)
  // USER END
#endif
};
#endif

/*********************************************************************
*
*       Static code
*
**********************************************************************
*/

// USER START (Optionally insert additional static code)
// USER END

/*********************************************************************
*
*       _cbDialog
*/
static void _cbDialog(WM_MESSAGE *pMsg)
{
    WM_HWIN hItem;
    int     NCode;
    int     Id;
    // USER START (Optionally insert additional variables)
    U8 i;
    int8_t s8tmp = 0;
    char str[128];
    U8 FlowArray[4] = {0, 30, 80, 120};
    U8 Adp801FlowArray[6] = {10, 30, 60, 80, 120, 160};
    const TITCONFIG *TitConfig = g_ConfigSave.GetTitConfig();
    struct tm tmV;
    // USER END

    switch(pMsg->MsgId)
    {
        case WM_INIT_DIALOG:
            //
            // Initialization of 'Window'
            //
            hItem = pMsg->hWin;
            WINDOW_SetBkColor(hItem, GUI_MAKE_COLOR(0x00000000));
            //
            // Initialization of 'Add1'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0);
            BUTTON_SetText(hItem, "+1");
            //
            // Initialization of 'Add5'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_1);
            BUTTON_SetText(hItem, "+5");
            //
            // Initialization of 'Add10'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_2);
            BUTTON_SetText(hItem, "+10");
            //
            // Initialization of 'Add50'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_3);
            BUTTON_SetText(hItem, "+50");
            //
            // Initialization of 'Red1'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_4);
            BUTTON_SetText(hItem, "-1");
            //
            // Initialization of 'Red5'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_5);
            BUTTON_SetText(hItem, "-5");
            //
            // Initialization of 'Red10'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_6);
            BUTTON_SetText(hItem, "-10");
            //
            // Initialization of 'Red50'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_7);
            BUTTON_SetText(hItem, "-50");
            //
            // Initialization of 'OK'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_8);
            BUTTON_SetText(hItem, "OK");
            //
            // Initialization of 'Prev'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_9);
            BUTTON_SetText(hItem, "Prev");
            //
            // Initialization of 'Exit'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_10);
            BUTTON_SetText(hItem, "Exit");
            //
            // Initialization of 'FlowCali'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_0);
            TEXT_SetText(hItem, "Flow Calibration");
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetTextAlign(hItem, GUI_TA_HCENTER | GUI_TA_VCENTER);
            //
            // Initialization of 'Prompt'
            //
#if (LCD_TYPE == LCD_5_TFT || LCD_TYPE == LCD_35_TFT)
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_1);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetText(hItem, "Adjust Motor");
#endif
            //
            // Initialization of 'PWM'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_2);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetText(hItem, "PWM:");
            //
            // Initialization of 'PWMValue'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_3);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetText(hItem, "0");
            //
            // Initialization of 'Flow'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_4);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetText(hItem, "Flow:");
            //
            // Initialization of 'FlowValue'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_5);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetText(hItem, "0");
            //
            // Initialization of 'Speed'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_6);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetText(hItem, "Speed:");
            //
            // Initialization of 'SpeedValue'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_7);
            TEXT_SetText(hItem, "0");
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            //
            // Initialization of 'PhyFlow'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_8);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetText(hItem, "Phy Flow:");
            //
            // Initialization of 'PhyFlowValue'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_9);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetText(hItem, "0");
            //
            // Initialization of 'FlowUnit'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_10);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetText(hItem, "L/min");
			//    
			// Initialization of 'FlowSensorType'
			//   
			hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_17);
			TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
			TEXT_SetText(hItem, "FlowSensorType:");
			//    
			// Initialization of 'FlowSensorTypeValue'
			//   
			hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_18);
			TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
			switch(GetSensorType())
			{
				case ADP800:
				{
					TEXT_SetText(hItem, "ADP801");
					break;
				}
				case HDP800:
				{
					TEXT_SetText(hItem, "HDP800");
					break;
				}
				case SDP810:
				{
					TEXT_SetText(hItem, "SDP800");
					break;
				}
				default:
				{
					TEXT_SetText(hItem, "UNKNOWN");
					break;
				}
			}  
            if(GetSensorType() == ADP800 || GetSensorType() == HDP800)
            {
                //
                // Initialization of 'F0Info'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_11);
                TEXT_SetText(hItem, "10:Flow");
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                //
                // Initialization of 'F30Info'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_12);
                TEXT_SetText(hItem, "30:Flow");
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                //
                // Initialization of 'F80Info'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_13);
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                TEXT_SetText(hItem, "80:Flow");
                //
                // Initialization of 'F140Info'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_14);
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                TEXT_SetText(hItem, "160:Flow");
                //
                // Initialization of 'CaliInfo'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_15);
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                TEXT_SetText(hItem, "Cali Time:1970/01/01 00:00:00");
                //
                // Initialization of 'CaliErrFlag'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_16);
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FF0000));
                TEXT_SetText(hItem, "Flow Cali Err");

                for(i = 0; i < 4; i++)
                {
                    sprintf(str, "%d:%s%d", Adp801FlowArray[i], GetMultiLanguageString(CALI_FLOW_IDX),
                                    (int16_t)TitConfig->Adp801TitFlow[i]);
                    TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + i), str);
                    TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_WHITE);
                }
#if (LCD_TYPE == LCD_5_TFT)
                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_24);
#else
                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_18);
#endif
                TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_BLUE);

                tmV = Time_ConvUnixToCalendar(TitConfig->TitFlowTime);

                sprintf(str, "%s%04d-%02d-%02d %02d:%02d:%02d", GetMultiLanguageString(CALI_TIME_IDX), 1900 + tmV.tm_year,
                                1 + tmV.tm_mon, tmV.tm_mday, tmV.tm_hour, tmV.tm_min, tmV.tm_sec);
                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_15), str);
                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_16),
                                GetFlowCalibrationErrFlag() ? GetMultiLanguageString(DEBUG_FLOW_CALI_ERROR_IDX) : "");
            }
            else if(GetSensorType() == SDP810)
            {
                //
                // Initialization of 'F30Info'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_11);
                TEXT_SetText(hItem, "0:Flow");
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                //
                // Initialization of 'F30Info'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_12);
                TEXT_SetText(hItem, "30:Flow");
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                //
                // Initialization of 'F80Info'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_13);
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                TEXT_SetText(hItem, "80:Flow");
                //
                // Initialization of 'F140Info'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_14);
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                TEXT_SetText(hItem, "120:Flow");
                //
                // Initialization of 'CaliInfo'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_15);
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
                TEXT_SetText(hItem, "Cali Time:1970/01/01 00:00:00");
                //
                // Initialization of 'CaliErrFlag'
                //
                hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_16);
                TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FF0000));
                TEXT_SetText(hItem, "Flow Cali Err");


                for(i = 0; i < 4; i++)
                {
                    //sprintf(str, "%1:Pressure %2,Flow %3,PWM %4,Speed %d", PressureArray[i], TitConfig->TitPress[i], TitConfig->TitFlow
                    sprintf(str, "%d:%s%d", FlowArray[i], GetMultiLanguageString(CALI_FLOW_IDX), (int16_t)TitConfig->TitFlow[(i + 3) % 4]);
                    TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + i), str);
                    TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_WHITE);
                }
#if (LCD_TYPE == LCD_5_TFT)
                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_24);
#else
                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_18);
#endif
                TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_BLUE);

                tmV = Time_ConvUnixToCalendar(TitConfig->TitFlowTime);

                sprintf(str, "%s%04d-%02d-%02d %02d:%02d:%02d", GetMultiLanguageString(CALI_TIME_IDX), 1900 + tmV.tm_year,
                                1 + tmV.tm_mon, tmV.tm_mday, tmV.tm_hour, tmV.tm_min, tmV.tm_sec);
                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_15), str);
                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_16),
                                GetFlowCalibrationErrFlag() ? GetMultiLanguageString(DEBUG_FLOW_CALI_ERROR_IDX) : "");
            }

            // USER START (Optionally insert additional code for further widget initialization)
            for(i = 0; i < BUTTON_NUM_MAX; i++)
            {
#if (LCD_TYPE == LCD_5_TFT)
                BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), FONT_24);
#else
                BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), FONT_18);
#endif
                _SetButtonSkin(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i));
            }
#if (LCD_TYPE == LCD_5_TFT)
            TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_0), FONT_24);
#else
            TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_0), FONT_18);
#endif
            for(i = 0; i < TEXT_NUM_MAX; i++)
            {
#if (LCD_TYPE == LCD_5_TFT)
                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_1 + i), FONT_24);
#else
                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_1 + i), FONT_18);
#endif
            }

            TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_0), GetMultiLanguageString(FLOWCALI_TITLE_IDX));
#if (LCD_TYPE == LCD_5_TFT || LCD_TYPE == LCD_35_TFT)
            TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_1), GetMultiLanguageString(CALI_ADJUSTMOTOR_IDX));
#endif
            TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_4), GetMultiLanguageString(CALI_FLOW_IDX));
            TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_6), GetMultiLanguageString(CALI_SPEED_IDX));
            TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_8), GetMultiLanguageString(CALI_PHYFLOW_IDX));
            TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_10), GetMultiLanguageString(L_MIN_IDX));
			TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_17), GetMultiLanguageString(DEBUG_FLOWSENSORTYPE_IDX));

            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_8), GetMultiLanguageString(CALI_OK_IDX));
            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_9), GetMultiLanguageString(CALI_PREV_IDX));
            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_10), GetMultiLanguageString(CALI_EXIT_IDX));
            // USER END
            break;
        case WM_NOTIFY_PARENT:
            Id    = WM_GetId(pMsg->hWinSrc);
            NCode = pMsg->Data.v;
            switch(NCode)
            {
                case WM_NOTIFICATION_CLICKED:
                    switch(Id)
                    {
                        case ID_BUTTON_0:
                            AddBldcmPWM(1, 1);
                            FlowTitFlow.bPWMAddOrFlag = TRUE;
                            break;
                        case ID_BUTTON_1:
                            AddBldcmPWM(1, 5);
                            FlowTitFlow.bPWMAddOrFlag = TRUE;
                            break;
                        case ID_BUTTON_2:
                            AddBldcmPWM(1, 10);
                            FlowTitFlow.bPWMAddOrFlag = TRUE;
                            break;
                        case ID_BUTTON_3:
                            AddBldcmPWM(1, 50);
                            FlowTitFlow.bPWMAddOrFlag = TRUE;
                            break;
                        case ID_BUTTON_4:
                            AddBldcmPWM(0, 1);
                            FlowTitFlow.bPWMAddOrFlag = TRUE;
                            break;
                        case ID_BUTTON_5:
                            AddBldcmPWM(0, 5);
                            FlowTitFlow.bPWMAddOrFlag = TRUE;
                            break;
                        case ID_BUTTON_6:
                            AddBldcmPWM(0, 10);
                            FlowTitFlow.bPWMAddOrFlag = TRUE;
                            break;
                        case ID_BUTTON_7:
                            AddBldcmPWM(0, 50);
                            FlowTitFlow.bPWMAddOrFlag = TRUE;
                            break;
                        case ID_BUTTON_8:
                            // USER START (Optionally insert code for reacting on notification message)
                            s8tmp = g_U8FlowCaliType;
                            if(g_U8FlowCaliType > 3)
                            {
                                g_U8FlowCaliType -= 4;
                            }
#if (LCD_TYPE == LCD_5_TFT)
                            TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_24);
#else
                            TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_18);
#endif
                            TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_WHITE);
                            g_U8FlowCaliType = s8tmp;
                            if(GetSensorType() == ADP800 || GetSensorType() == HDP800)
                            {
                                if(g_U8FlowCaliType == 5)
                                {
                                    g_ConfigSave.SetParameter(TIT140FLOW, GetTitFlowCode(), 0);
                                }
                                else
                                {
                                    g_ConfigSave.SetParameter((ECONFIGTYPE)(TIT0FLOW + g_U8FlowCaliType), GetTitFlowCode(), 0);
                                }
                                g_ConfigSave.SetParameter(TITFLOWTIME, GetCurrentDateTime());

                                CalcADCodeToFlow();     //定标后，实时更新曲线，方便复测
                                g_ConfigSave.SetParameter(TITFLOWCALIMODE, 1);//debug界面重新定标后即为新定标方式。
                                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_16),
                                                GetFlowCalibrationErrFlag() ? GetMultiLanguageString(DEBUG_FLOW_CALI_ERROR_IDX) : "");
                                sprintf(str, "%d:%s%d", Adp801FlowArray[g_U8FlowCaliType], GetMultiLanguageString(CALI_FLOW_IDX),
                                                (int16_t)TitConfig->Adp801TitFlow[g_U8FlowCaliType]);
                                s8tmp = g_U8FlowCaliType;
                                if(g_U8FlowCaliType > 3)
                                {
                                    g_U8FlowCaliType -= 4;
                                }
                                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), str);
                                g_U8FlowCaliType = s8tmp;
                                g_U8FlowCaliType = (g_U8FlowCaliType + 1) % 6;
                                sprintf(str, "%d:%s%d", Adp801FlowArray[g_U8FlowCaliType], GetMultiLanguageString(CALI_FLOW_IDX),
                                                (int16_t)TitConfig->Adp801TitFlow[g_U8FlowCaliType]);
                                s8tmp = g_U8FlowCaliType;
                                if(g_U8FlowCaliType > 3)
                                {
                                    g_U8FlowCaliType -= 4;
                                }
#if (LCD_TYPE == LCD_5_TFT)
                                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_24);
#else
                                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_18);
#endif
                                TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_BLUE);
                                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), str);
                                g_U8FlowCaliType = s8tmp;

                                tmV = Time_ConvUnixToCalendar(TitConfig->TitFlowTime);
                                sprintf(str, "%s%04d-%02d-%02d %02d:%02d:%02d", GetMultiLanguageString(CALI_TIME_IDX), 1900 + tmV.tm_year,
                                                1 + tmV.tm_mon, tmV.tm_mday, tmV.tm_hour, tmV.tm_min, tmV.tm_sec);
                                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_15), str);

                                switch(g_U8FlowCaliType)
                                {
                                    case 0:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 210);
                                        break;
                                    }
                                    case 1:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 455);
                                        break;
                                    }
                                    case 2:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 900);
                                        break;
                                    }
                                    case 3:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 1250);
                                        break;
                                    }
                                    case 4:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 1850);
                                        break;
                                    }
                                    case 5:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 2550);
                                        break;
                                    }
                                    default:
                                        break;
                                }
                            }
                            else if(GetSensorType() == SDP810)
                            {
                                g_ConfigSave.SetParameter((ECONFIGTYPE)(TIT30FLOW + ((g_U8FlowCaliType + 3) % 4)), GetTitFlowCode(), 0);
                                g_ConfigSave.SetParameter(TITFLOWTIME, GetCurrentDateTime());

                                CalcADCodeToFlow();     //定标后，实时更新曲线，方便复测
                                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_16),
                                                GetFlowCalibrationErrFlag() ? GetMultiLanguageString(DEBUG_FLOW_CALI_ERROR_IDX) : "");
                                sprintf(str, "%d:%s%d", FlowArray[g_U8FlowCaliType], GetMultiLanguageString(CALI_FLOW_IDX),
                                                (int16_t)TitConfig->TitFlow[(g_U8FlowCaliType + 3) % 4]);
                                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), str);
                                g_U8FlowCaliType = (g_U8FlowCaliType + 1) % 4;
#if (LCD_TYPE == LCD_5_TFT)
                                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_24);
#else
                                TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), FONT_18);
#endif
                                TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_BLUE);

                                tmV = Time_ConvUnixToCalendar(TitConfig->TitFlowTime);
                                sprintf(str, "%s%04d-%02d-%02d %02d:%02d:%02d", GetMultiLanguageString(CALI_TIME_IDX), 1900 + tmV.tm_year,
                                                1 + tmV.tm_mon, tmV.tm_mday, tmV.tm_hour, tmV.tm_min, tmV.tm_sec);
                                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_15), str);
                                switch(g_U8FlowCaliType)
                                {
                                    case 0:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 0);
                                        break;
                                    }
                                    case 1:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 530);
                                        break;
                                    }
                                    case 2:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 1480);
                                        break;
                                    }
                                    case 3:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 1830);
                                        break;
                                    }
                                    default:
                                        break;
                                }
                            }
                            //将校准类型区分标识修改为手动定标方式
                            g_ConfigSave.update_save_auto_flow_calib_type(FLOW_CALIB_TYPE_MANUAL);
                            break;
                        case ID_BUTTON_9:
                            ClearFilterData(TIT_FLWO_CODE, GetRealFlowCode());
//        // USER START (Optionally insert code for reacting on notification message)
                            s8tmp = g_U8FlowCaliType;
                            if(g_U8FlowCaliType > 3)
                            {
                                g_U8FlowCaliType -= 4;
                            }
                            TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_WHITE);
                            g_U8FlowCaliType = s8tmp;
                            if(GetSensorType() == ADP800 || GetSensorType() == HDP800)
                            {
                                g_U8FlowCaliType = ((g_U8FlowCaliType - 1) < 0) ? 5 : (g_U8FlowCaliType - 1);
                                sprintf(str, "%d:%s%d", Adp801FlowArray[g_U8FlowCaliType], GetMultiLanguageString(CALI_FLOW_IDX),
                                                (int16_t)TitConfig->Adp801TitFlow[g_U8FlowCaliType]);
                                s8tmp = g_U8FlowCaliType;
                                if(g_U8FlowCaliType > 3)
                                {
                                    g_U8FlowCaliType -= 4;
                                }
                                TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_BLUE);
                                TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), str);
                                g_U8FlowCaliType = s8tmp;
                                switch(g_U8FlowCaliType)
                                {
                                    case 0:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 210);
                                        break;
                                    }
                                    case 1:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 455);
                                        break;
                                    }
                                    case 2:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 900);
                                        break;
                                    }
                                    case 3:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 1250);
                                        break;
                                    }
                                    case 4:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 1850);
                                        break;
                                    }
                                    case 5:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 2550);
                                        break;
                                    }
                                    default:
                                        break;
                                }
                            }
                            else
                            {
                                g_U8FlowCaliType = (g_U8FlowCaliType + 3) % 4;
                                TEXT_SetTextColor(WM_GetDialogItem(pMsg->hWin, ID_TEXT_11 + g_U8FlowCaliType), GUI_BLUE);

                                switch(g_U8FlowCaliType)
                                {
                                    case 0:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 0);
                                        break;
                                    }
                                    case 1:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 530);
                                        break;
                                    }
                                    case 2:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 1480);
                                        break;
                                    }
                                    case 3:
                                    {
                                        g_ConfigSave.StartFlowCali(1, 1830);
                                        break;
                                    }
                                    default:
                                        break;
                                }
                            }
                            // USER END
                            break;
                        case ID_BUTTON_10:
                            ShowFlowCali(0);
                            break;
                        default:
                            WM_DefaultProc(pMsg);
                            break;
                    }
                    break;
                case WM_NOTIFICATION_RELEASED:
                    break;
                default:
                    break;
            }
        // USER START (Optionally insert additional message handling)
        // USER END
        default:
            WM_DefaultProc(pMsg);
            break;
    }
}
    

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/

// USER START (Optionally insert additional public code)
void ShowFlowCali(U8 Flag)
{
    if(Flag)
    {
        if(g_FlowCaliHwin == NULL)
        {
            g_FlowCaliHwin = GUI_CreateDialogBox(_aDialogCreate, GUI_COUNTOF(_aDialogCreate), _cbDialog, WM_HBKWIN, 0, 0);
        }
        ShowProcess();
        g_WindowDrv.PushWindow(&g_FlowCaliWInfo);
        if(GetSensorType() == ADP800 || GetSensorType() == HDP800)
        {
            g_ConfigSave.StartFlowCali(Flag, 210);
        }
        else
        {
            g_ConfigSave.StartFlowCali(Flag, 0);
        }
    }
    else
    {
        GUI_TIMER_DELETE(g_RefreshTimer);
        WM_HideWindow(g_FlowCaliHwin);
        // WM_DeleteWindow(g_FlowCaliHwin);
        // g_FlowCaliHwin = NULL;
        g_WindowDrv.PopWindow(&g_FlowCaliWInfo);
        g_ConfigSave.StartFlowCali(Flag, 0);
    }
}

static void ShowProcess(void)
{
    U8 i;
    g_U8FlowCaliType = 0;

    if(GetSensorType() == ADP800 || GetSensorType() == HDP800)
    {
        for(i = 1; i < 5; i++)
        {
            TEXT_SetTextColor(WM_GetDialogItem(g_FlowCaliHwin, ID_TEXT_11 + i), GUI_WHITE);
        }
    }
    else if(GetSensorType() == SDP810)
    {
        for(i = 1; i < 4; i++)
        {
            TEXT_SetTextColor(WM_GetDialogItem(g_FlowCaliHwin, ID_TEXT_11 + i), GUI_WHITE);
        }
    }
    TEXT_SetTextColor(WM_GetDialogItem(g_FlowCaliHwin, ID_TEXT_11 + g_U8FlowCaliType), GUI_BLUE);
    WM_SetFocus(WM_GetDialogItem(g_FlowCaliHwin, ID_BUTTON_0));
    WM_ShowWindow(g_FlowCaliHwin);
    CreateRefreshTimer();
}
// USER END

/*************************** End of file ****************************/

