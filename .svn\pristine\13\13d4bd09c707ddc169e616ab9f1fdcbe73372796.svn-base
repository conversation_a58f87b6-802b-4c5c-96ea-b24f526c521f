#ifndef __STACK_H__
#define __STACK_H__

#include "DataType.h"
#include <assert.h>

#define GUI_TIMER_CREATE(typeTimer, callBackFun, time, context, flags) ((typeTimer == NULL) ? (typeTimer = GUI_TIMER_Create((G<PERSON>_TIMER_CALLBACK *)callBackFun, time, context, flags)) : NULL)
#define GUI_TIMER_DELETE(typeTimer) if(typeTimer != NULL) \
									{ \
										GUI_TIMER_Delete(typeTimer);\
										typeTimer = NULL ; \
									} \

#define STACK_SIZE		16

typedef enum EWindowID
{
    MAINSCREEN_ID = 1,
    MON<PERSON>ORVIEW_ID,
    WAVE<PERSON>EW_ID,
    PREHEATSETTING_ID,
    ALARMSETTING_ID,
    INFOSETTING_ID,
    SYSSETTING_ID,
    PARAMETERSETTING_ID,

    MANAGERSETTING_ID,
    ADMIN_PRESSCALI_ID,
    ADMIN_FLOWCALI_ID,

    MASK<PERSON>EARINGTEST_ID,
    
    SSIDSETTING_ID,
    W<PERSON><PERSON><PERSON><PERSON><PERSON>D_ID,
    
    <PERSON><PERSON><PERSON><PERSON><PERSON>TING_ID,
    
    <PERSON><PERSON>UG_ID,
    <PERSON><PERSON><PERSON><PERSON>I_ID,
    <PERSON>OWCALI_ID,
    AUTO_FLOWCALI_ID,
    RETEST_ID,
    DEBUGSYSRESET_ID,
    DEBUGSINGLEFAULTPRESSURETEST_ID,
    DEBUGSETOFFSET_ID,
    DEBUGMOREOPTION_ID,
    DEBUGHEATINGTEST_ID,
    DEVICEINFO_ID,
    BUZZERTEST_ID,
    AUTORPRESCALI_ID,

    PERFORMANCETEST_ID,
    ALARMPOPUP_ID,
    SYSRESETPOPUP_ID,
    
    WINDOWID_NONE,
}EWINDOWID;

typedef struct TWindowInfo
{
	void (*Init)();
	void (*ProcessKey)(U8 Key);
	void (*Exit)();
    void (*Repaint)();
    void (*ShowProcess)();
    void (*OtherProcess)();  //窗口下沉时的处理，顶部又有新的窗口显示
    U8 U8CurFocusID;
    U8 U8OldFocusID;
    U8 U8WindowID;
}WINDOWINFO, *PWINDOWINFO;

template <class T, int size>
class CStack
{
public:
	CStack() { Clear(); }
	virtual ~CStack() {}
	void Push(T p)
	{
		if (m_I8Top < size - 1)
        {
            m_Item[++m_I8Top] = p;
        }
	}
	T Pop()
	{
		if (m_I8Top >= 0)
		{
            return m_Item[m_I8Top--];
        }
        return NULL;
	}
    T Top()
    {
        if (m_I8Top >= 0)
		{
            return m_Item[m_I8Top];
        }
        return NULL;
    }
	T Bottom()
	{
        if (m_I8Top >= 0)
		{
            return m_Item[0];
        }
        return NULL;
	}
	int Count()
	{
		return m_I8Top + 1;
	}
	void Clear()
	{ 
		m_I8Top = -1;
	}
public:
	T m_Item[size];
	I8	m_I8Top;
};

class CWindowDrv
{
public:
	CWindowDrv();
	virtual ~CWindowDrv();
    PWINDOWINFO GetTopWindow();
    void ProcessKey(U8 Key);
    bool PushWindow(PWINDOWINFO pWind, U8 ForceRepaint = 0);
    bool PopWindow(PWINDOWINFO pWind);
    bool PopWindowNoRepaint(PWINDOWINFO pWind);
    bool ReturnMainWindow();
    void SetCurWindow(PWINDOWINFO pWind);
    bool ShowIsSysSetting();
    bool ShowIsMainScreen();
    bool ShowIsWaveMonitor();
    bool ShowIsAlarm();
    bool ShowIsMonitorView();
    bool ShowHaveWaveMonitorAndQuickSetting();
    bool ShowHaveWaveMonitor();
    bool ShowHaveMonitorView();
    bool ShowIsRetestWindow();

	bool ShowIsDebugScreen();

    bool ShowIsDebugOrFlowCaliOrPresCali();

    bool CheckRepaintParameterSetting();

protected:
    CStack<void*, STACK_SIZE> m_Stack;
    PWINDOWINFO m_pCurrWindow;
};

extern CWindowDrv g_WindowDrv;

U8 GetFocus(U8* pKeyValue);

#endif
