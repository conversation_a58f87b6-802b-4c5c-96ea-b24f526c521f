#include "InfoSettingScreen.h"
#include "Led.h"
#include <stdio.h>
#include "Key.h"
#include "ConfigSave.h"
#include "MultiLanguage.h"
#include "GlobalVariable.h"
#include "StaterBar.h"
#include "AppBMP.h"
#include "stack.h"
#include "EdfDataSave.h"

void RepaintInfoSettingScreen(void);
static void InfoSettingScreenProcess(U8 Key);
static void ShowQRcode(void);
static uint8_t SeparateNightConvert(void);
static void QRDataConvert(char* pBuf, uint32_t Data, uint8_t Length);
static void OnEnterClick_QRCode(void);

WINDOWINFO g_InfoSettingScreenWInfo = {NULL, InfoSettingScreenProcess, NULL, RepaintInfoSettingScreen, NULL, NULL, 0, 0, INFOSETTING_ID};

static uint8_t g_sU8CurEditState_InfoSetting = FALSE;

#if (LCD_TYPE == LCD_28_TFT)
static const ESTRINGIDX IdType_Info_95TH[][3] = {{USECYCLE_IDX, USETIME_IDX, AVGPRESS_IDX} 
                                                ,{PRESSURE_95TH_IDX, AVGLEAKAGE_IDX, AHI_IDX}
                                                ,{CSI_IDX, USEDAYS_IDX, RUNHRS_IDX}
                                                ,{QRCODE_IDX}};
static const ESTRINGIDX IdType_Info_No95TH[][3] = {{USECYCLE_IDX, USETIME_IDX, AVGPRESS_IDX} 
                                                  ,{AVGLEAKAGE_IDX, AHI_IDX, CSI_IDX}
                                                  ,{USEDAYS_IDX, RUNHRS_IDX, QRCODE_IDX}};
#else
//static uint8_t g_sU8OldInfoSettingScreenFocus = 0;
static const ESTRINGIDX IdType_Info_95TH[][5] = {{USECYCLE_IDX, USETIME_IDX, AVGPRESS_IDX, PRESSURE_95TH_IDX, AVGLEAKAGE_IDX}
                                                ,{AHI_IDX, CSI_IDX, USEDAYS_IDX, RUNHRS_IDX, QRCODE_IDX}};
static const ESTRINGIDX IdType_Info_No95TH[][5] = {{USECYCLE_IDX, USETIME_IDX, AVGPRESS_IDX, AVGLEAKAGE_IDX, AHI_IDX}
                                                  ,{CSI_IDX, USEDAYS_IDX, RUNHRS_IDX, QRCODE_IDX}};
#endif

static const uint8_t g_sU8InfoSettingMaxFocus[] = 
{
    MAX_FOCUS_NUM_INFOSETTING_No95TH, MAX_FOCUS_NUM_INFOSETTING_95TH, MAX_FOCUS_NUM_INFOSETTING_95TH, MAX_FOCUS_NUM_INFOSETTING_No95TH,
    MAX_FOCUS_NUM_INFOSETTING_No95TH, MAX_FOCUS_NUM_INFOSETTING_No95TH, MAX_FOCUS_NUM_INFOSETTING_95TH, MAX_FOCUS_NUM_INFOSETTING_No95TH,
    MAX_FOCUS_NUM_INFOSETTING_No95TH, MAX_FOCUS_NUM_INFOSETTING_No95TH, MAX_FOCUS_NUM_INFOSETTING_No95TH, MAX_FOCUS_NUM_INFOSETTING_No95TH
    
};
                                                  
static void ChangeFocusInfoSetting_Right(void);
static void ChangeFocusInfoSetting_Left(void);
static void DrawInformationSettingScreen(uint8_t u8Page, uint8_t u8IsFirst);

static void RefreshInformationSettingScreen_Left(void);
static void RefreshInformationSettingScreen_Right(void);
static void OnEnterClick_InformationSetting(void);

static void DrawOptionInfoSettingKeyLeft(void);
static void DrawOptionInfoSettingKeyRight(void);

static void InfoSettingScreenProcess(U8 Key)
{       
	switch (Key)
	{
        case GUI_KEY_BACKTAB:            
            if (g_sU8CurEditState_InfoSetting == FALSE)
            {                
                g_InfoSettingScreenWInfo.U8CurFocusID = g_InfoSettingScreenWInfo.U8CurFocusID <= 0 
                                             ? (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1) 
                                             : (g_InfoSettingScreenWInfo.U8CurFocusID - 1);
                ChangeFocusInfoSetting_Left();                
            }
            else
            {
                if (g_InfoSettingScreenWInfo.U8CurFocusID == 0 && g_sU8CurEditState_InfoSetting)
                    DrawOptionInfoSettingKeyLeft();
            }
            break;
        case GUI_KEY_TAB:
            if (g_sU8CurEditState_InfoSetting == FALSE)
            {
                g_InfoSettingScreenWInfo.U8CurFocusID = g_InfoSettingScreenWInfo.U8CurFocusID >= (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1) 
                                             ? 0
                                             : (g_InfoSettingScreenWInfo.U8CurFocusID + 1);                
                ChangeFocusInfoSetting_Right();
            }
            else
            {
                if (g_InfoSettingScreenWInfo.U8CurFocusID == 0 && g_sU8CurEditState_InfoSetting)
                    DrawOptionInfoSettingKeyRight();
            }
            break;
        case GUI_KEY_ENTER:             
        case GUI_KEY_F1:
            OnEnterClick_InformationSetting();
        default:
            break;
    }
}

static void ChangeFocusInfoSetting_Right(void)
{
    WM_SelectWindow(WM_HBKWIN);
    
    GUI_SetTextMode(GUI_TM_TRANS);  //设置背景透明
    
    RefreshInformationSettingScreen_Right();
        
#if (LCD_TYPE != LCD_28_TFT)
    g_InfoSettingScreenWInfo.U8OldFocusID = g_InfoSettingScreenWInfo.U8CurFocusID;
#endif
}

static void ChangeFocusInfoSetting_Left(void)
{   
    WM_SelectWindow(WM_HBKWIN);
    
    GUI_SetTextMode(GUI_TM_TRANS);  //设置背景透明

    RefreshInformationSettingScreen_Left();

#if (LCD_TYPE != LCD_28_TFT)
    g_InfoSettingScreenWInfo.U8OldFocusID = g_InfoSettingScreenWInfo.U8CurFocusID;
#endif    
}

static void RefreshInformationSettingScreen_Left(void)
{ 
#if (LCD_TYPE == LCD_28_TFT)   
    //if (g_InfoSettingScreenWInfo.U8CurFocusID == g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1 && g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE == 2)   
    if (g_InfoSettingScreenWInfo.U8CurFocusID == g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1  
        && (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1) % MAX_PAGE_LINE != 0)   
        DrawInformationSettingScreen(g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, 3);
    else
        DrawInformationSettingScreen(g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, 0);
#else
	if (g_InfoSettingScreenWInfo.U8CurFocusID == g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1)
        DrawInformationSettingScreen((g_InfoSettingScreenWInfo.U8CurFocusID - 1) / 5, FALSE); //翻到第2页,并选择该页的最后一项  
    else if (g_InfoSettingScreenWInfo.U8CurFocusID % 5 == 4
            && (g_InfoSettingScreenWInfo.U8CurFocusID / 5 < (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 2) / 5))
        DrawInformationSettingScreen(g_InfoSettingScreenWInfo.U8CurFocusID / 5, FALSE);   //翻到第1页,并选择该页的最后一项
    else
    {
        if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
            || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
            || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
        {
            ChangeFocusNoPageTurning_ChildScreen(g_InfoSettingScreenWInfo.U8CurFocusID
                                                , g_InfoSettingScreenWInfo.U8OldFocusID
                                                , g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)]
                                                , IdType_Info_95TH
                                                , g_sU8CurEditState_InfoSetting);        
        }
        else
        {
            ChangeFocusNoPageTurning_ChildScreen(g_InfoSettingScreenWInfo.U8CurFocusID
                                                , g_InfoSettingScreenWInfo.U8OldFocusID
                                                , g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)]
                                                , IdType_Info_No95TH
                                                , g_sU8CurEditState_InfoSetting);
        }
    }
#endif
}

static void RefreshInformationSettingScreen_Right(void)
{
    U8 sU8WorkMode = g_ConfigSave.GetParameter(WORKMODE);
#if (LCD_TYPE == LCD_28_TFT)
    //if (g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE == 0 && g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE == 2)
    if (g_InfoSettingScreenWInfo.U8CurFocusID == (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 2)/MAX_PAGE_LINE * MAX_PAGE_LINE  
       && (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1) % MAX_PAGE_LINE != 0)
       DrawInformationSettingScreen(g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, 3);
    else
       DrawInformationSettingScreen(g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, 0);
#else 
	if (g_InfoSettingScreenWInfo.U8CurFocusID % 5 == 0 && g_InfoSettingScreenWInfo.U8CurFocusID < (g_sU8InfoSettingMaxFocus[sU8WorkMode] - 1))
        DrawInformationSettingScreen(g_InfoSettingScreenWInfo.U8CurFocusID / 5, TRUE); //翻到第1页，并选中该页的第1行
    else
    {
        if (sU8WorkMode == SYS_WM_APAP || sU8WorkMode == SYS_WM_IAPAP || sU8WorkMode == SYS_WM_AUTOB)
        {
            ChangeFocusNoPageTurning_ChildScreen(g_InfoSettingScreenWInfo.U8CurFocusID
                                                , g_InfoSettingScreenWInfo.U8OldFocusID
                                                , g_sU8InfoSettingMaxFocus[sU8WorkMode]
                                                , IdType_Info_95TH
                                                , g_sU8CurEditState_InfoSetting);        
        }
        else
        {
            ChangeFocusNoPageTurning_ChildScreen(g_InfoSettingScreenWInfo.U8CurFocusID
                                                , g_InfoSettingScreenWInfo.U8OldFocusID
                                                , g_sU8InfoSettingMaxFocus[sU8WorkMode]
                                                , IdType_Info_No95TH
                                                , g_sU8CurEditState_InfoSetting);
        }
    }
#endif   
}

static uint8_t SeparateNightConvert(void)
{
    uint8_t u8Index = 0;
    uint8_t U8SeparateNightArray[] = {0, 120, 180, 240};
    for (u8Index = 0; u8Index < 4; u8Index++)
    {
        if (U8SeparateNightArray[u8Index] == g_ConfigSave.GetParameter(SEPARATENIGHT))
            break;
    }
    return u8Index;
}

static void QRDataConvert(char* pBuf, uint32_t Data, uint8_t Length)
{
	uint32_t sU32TmpData = Data;
	int32_t sU32TmpLength = (Length - 1);
	for (uint8_t i = 0; i < Length; i++)
	{
		if (sU32TmpLength)
		{
			pBuf[i] = 'A' + (sU32TmpData) / (uint32_t)(pow(10.0, sU32TmpLength));
		}
		else
		{
			pBuf[i] = 'A' + sU32TmpData;
		}
		sU32TmpData = (sU32TmpData) % ((uint32_t)pow(10.0, sU32TmpLength));
		sU32TmpLength--;
	}
}

RTC_TimeTypeDef1 RTC_Time;
static void ShowQRcode(void)
{
    GUI_HMEM QR_CODE;
    uint16_t u16UseCycle;
    uint8_t i = 0;
    uint16_t u16Tmp = 0;
    uint16_t u16Lenth = 0;
    char QRCodestr[250] = {0};
    char str_tmp[12] = {0};
    char str_Data[12];
    
    RTC_ReadTime(&RTC_Time);

    //1:   序列号  11位
    memset(QRCodestr, 0, strlen(QRCodestr));
    u16Lenth += sprintf(&QRCodestr[u16Lenth], "%s", g_I8Sn);
    
    //2:  当前设备时间    %10d
    QRDataConvert(QRCodestr + 11, RTC_Time.year - 2000, 2);
    QRDataConvert(QRCodestr + 13, RTC_Time.mon, 2);
    QRDataConvert(QRCodestr + 15, RTC_Time.day, 2);
    QRDataConvert(QRCodestr + 17, RTC_Time.hour, 2);
    QRDataConvert(QRCodestr + 19, RTC_Time.minu, 2);

    //3:  使用周期:对应云平台/微信的选择天数(1-1天,2-7天,3-30天，4-3月，5-6月，6-12月) %01d
    QRDataConvert(QRCodestr + 21, g_ConfigSave.GetUseCycleForQRCode(), 1);

    //4:  使用时间: 使用周期内的所有秒数  %08d
    QRDataConvert(QRCodestr + 22, g_EdfDataSave.GetShowEDFData(SHOW_USE_TIME_QRCODE), 8);
    
    //5:  使用时间: 使用周期内的所有秒数(无延时升压)   %08d
    QRDataConvert(QRCodestr + 30, g_EdfDataSave.GetShowEDFData(SHOW_USE_TIME_NORAMP_QRCODE), 8);
    
    //6:  使用天数：使用周期内的 当天内机器有无使用过，当天单次使用超过6分钟；单位:天 %03d
    QRDataConvert(QRCodestr + 38, g_EdfDataSave.GetShowEDFData(SHOW_USE_DAYS_QRCODE), 3);
    
    //7:  有效治疗天数:当天内有无机器累计使用超过4h    %03d
    QRDataConvert(QRCodestr + 41, g_EdfDataSave.GetShowEDFData(SHOW_EFFDAYS), 3);
    
    //8:  低通气次数：使用周期内低通气事件次数    %07d
    QRDataConvert(QRCodestr + 44, g_EdfDataSave.GetShowEDFData(SHOW_HYP_CNT), 7);
    
    //9:  CSAS次数：使用周期内CSAS次数    %07d
    QRDataConvert(QRCodestr + 51, g_EdfDataSave.GetShowEDFData(SHOW_CSAS_CNT), 7);

    //10:  MSAS次数：使用周期内MSAS次数   %07d
    QRDataConvert(QRCodestr + 58, g_EdfDataSave.GetShowEDFData(SHOW_MSAS_CNT), 7);

    //11:  AH次数：使用周期内呼吸暂停和低通气的次数    %07d
    QRDataConvert(QRCodestr + 65, g_EdfDataSave.GetShowEDFData(SHOW_OSAHS_CNT), 7);

    //12:  氧减事件次数：使用周期内氧减事件的次数  %07d
    QRDataConvert(QRCodestr + 72, g_EdfDataSave.GetShowEDFData(SHOW_ODI_CNT), 7);

    //13:  鼾声事件次数：使用周期内鼾声事件的次数  %07d
    QRDataConvert(QRCodestr + 79, g_EdfDataSave.GetShowEDFData(SHOW_SNORE_CNT), 7);

    //14:  潮式呼吸次数：使用周期内潮式呼吸的次数  %07d
    QRDataConvert(QRCodestr + 86, g_EdfDataSave.GetShowEDFData(SHOW_CHEYNE_STOKES_CNT), 7);

    //15  压力最大值 0.1cmH2O为单位，最大工作压力/吸气压力 %03d
    QRDataConvert(QRCodestr + 93, g_EdfDataSave.GetShowEDFData(SHOW_MAXP_QRCODE), 3);

    //16  平均95th    %03d
    QRDataConvert(QRCodestr + 96, g_EdfDataSave.GetShowEDFData(SHOW_AVG_95PRESS), 3);

    //17 平均压力   %03d
    QRDataConvert(QRCodestr + 99, g_EdfDataSave.GetShowEDFData(SHOW_AVG_PRESS), 3);                

    //18 最大漏气量  %04d
    QRDataConvert(QRCodestr + 102, g_EdfDataSave.GetShowEDFData(SHOW_MAX_LK_QRCODE), 4);

    //19 平均漏气量  %04d
    QRDataConvert(QRCodestr + 106, g_EdfDataSave.GetShowEDFData(SHOW_AVG_LEAK_FLOW), 4);

    //20  最大分钟通气量   %03d
    QRDataConvert(QRCodestr + 110, g_EdfDataSave.GetShowEDFData(SHOW_MAX_MV_QRCODE), 3);

    //21  平均分钟通气量   %03d
    QRDataConvert(QRCodestr + 113, g_EdfDataSave.GetShowEDFData(SHOW_AVG_MV_QRCODE), 3);

    //22  最大潮气量 %05d
    QRDataConvert(QRCodestr + 116, g_EdfDataSave.GetShowEDFData(SHOW_MAX_VT_QRCODE), 5);

    //23  平均潮气量 %05d
    QRDataConvert(QRCodestr + 121, g_EdfDataSave.GetShowEDFData(SHOW_AVG_VT_QRCODE), 5);

    //24  最大BPM %02d
    QRDataConvert(QRCodestr + 126, g_EdfDataSave.GetShowEDFData(SHOW_MAX_BPM_QRCODE), 2);

    //25  平均BPM %02d
    QRDataConvert(QRCodestr + 128, g_EdfDataSave.GetShowEDFData(SHOW_AVG_BPM_QRCODE), 2);

    //26  最大SpO2 %03d
    QRDataConvert(QRCodestr + 130, g_EdfDataSave.GetShowEDFData(SHOW_MAX_SPO2_QRCODE), 3);

    //27  平均SpP2 %03d
    QRDataConvert(QRCodestr + 133, g_EdfDataSave.GetShowEDFData(SHOW_AVG_SPO2_QRCODE), 3);

    //28  最小SpO2 %03d
    QRDataConvert(QRCodestr + 136, g_EdfDataSave.GetShowEDFData(SHOW_MIN_SPO2_QRCODE), 3);

    //29  最大PR  %03d
    QRDataConvert(QRCodestr + 139, g_EdfDataSave.GetShowEDFData(SHOW_MAX_PR_QRCODE), 3);

    //30  平均PR  %03d
    QRDataConvert(QRCodestr + 142, g_EdfDataSave.GetShowEDFData(SHOW_AVG_PR_QRCODE), 3);

    //31  最小PR  %03d
    QRDataConvert(QRCodestr + 145, g_EdfDataSave.GetShowEDFData(SHOW_MIN_PR_QRCODE), 3);
 
    //32: 工作模式; %01d
    QRDataConvert(QRCodestr + 148, g_ConfigSave.GetParameter(WORKMODE), 1);

    //33: 延时升压; %02d
    QRDataConvert(QRCodestr + 149, g_ConfigSave.GetParameter(RAMP), 2);

    //34: 自动开机; %01d
    QRDataConvert(QRCodestr + 151, g_ConfigSave.GetParameter(AUTOON), 1);

    //35: 自动关机  %01d
    QRDataConvert(QRCodestr + 152, g_ConfigSave.GetParameter(AUTOOFF), 1);

    //36: ISens;     %01d
    QRDataConvert(QRCodestr + 153, g_ConfigSave.GetParameter(ISENS), 1);

    //37: ESens;     %01d
    QRDataConvert(QRCodestr + 154, g_ConfigSave.GetParameter(ESENS), 1);

    //38: StartP;    %03d
    QRDataConvert(QRCodestr + 155, g_ConfigSave.GetParameter(STARTPRESSURE), 3);

    //39: EPAP;      %03d
    QRDataConvert(QRCodestr + 158, g_ConfigSave.GetParameter(EPAP), 3);

    //40: IPAP;      %03d
    QRDataConvert(QRCodestr + 161, g_ConfigSave.GetParameter(IPAP), 3);

    //41: MinEPAP;  %03d
    QRDataConvert(QRCodestr + 164, g_ConfigSave.GetParameter(MINEPAP), 3);

    //42: MaxIPAP;  %03d
    QRDataConvert(QRCodestr + 167, g_ConfigSave.GetParameter(MAXIPAP), 3);

    //43: MaxPS;    %02d
    QRDataConvert(QRCodestr + 170, g_ConfigSave.GetParameter(MAXPS), 2);

    //44: MinIPAP   %03d
    QRDataConvert(QRCodestr + 172, g_ConfigSave.GetParameter(MINIPAP), 3);

    //45: MinP;     %03d
    QRDataConvert(QRCodestr + 175, g_ConfigSave.GetParameter(MINPRESS), 3);

    //46: MaxP;     %03d
    QRDataConvert(QRCodestr + 178, g_ConfigSave.GetParameter(MAXPRESS), 3);

    //47: WorkP     %03d
    QRDataConvert(QRCodestr + 181, g_ConfigSave.GetParameter(WORKPRESS), 3);

    //48: 目标VT;             %04d
    QRDataConvert(QRCodestr + 184, g_ConfigSave.GetParameter(VT), 4);

    //49: InspTime;           %02d
    QRDataConvert(QRCodestr + 188, g_ConfigSave.GetParameter(INSPTIME), 2);

    //50: MaxInspTime;        %02d
    QRDataConvert(QRCodestr + 190, g_ConfigSave.GetParameter(MAXINSPTIME), 2);

    //51: MinInspTime         %02d
    QRDataConvert(QRCodestr + 192, g_ConfigSave.GetParameter(MININSPTIME), 2);

    //52: ISlop;    %01d
    QRDataConvert(QRCodestr + 194, g_ConfigSave.GetParameter(ISLOP), 1);

    //53: BPM;      %02d
    QRDataConvert(QRCodestr + 195, g_ConfigSave.GetParameter(BPM), 2);

    //54: Belex;    %01d
    QRDataConvert(QRCodestr + 197, g_ConfigSave.GetParameter(BELEX), 1);

    //55: AutoRamp; %01d
    QRDataConvert(QRCodestr + 198, g_ConfigSave.GetParameter(IRAMP), 1);

    //56: SmartP;   %03d
    QRDataConvert(QRCodestr + 199, g_ConfigSave.GetParameter(SMARTPRESSURE), 3);

    //57: 分夜      %01d
    QRDataConvert(QRCodestr + 202, SeparateNightConvert(), 1);

    //58: 升压灵敏度;    %01d
    QRDataConvert(QRCodestr + 203, g_ConfigSave.GetParameter(BOOTSTSENSITIVITY), 1);

    //59: 流量    %02d
    QRDataConvert(QRCodestr + 204, g_ConfigSave.GetParameter(TARGETHIGHFLOW), 2);

    //60: 预留: %041d
    //61: 校验字 %03d
    u16Tmp = 0;//校验
    for(i = 0; i < 206; i++)
    {
        u16Tmp += (uint16_t)QRCodestr[i];
    }
    sprintf(&QRCodestr[206], "%044d", u16Tmp % 1000);


    #if (LCD_TYPE == LCD_5_TFT)
        QR_CODE = GUI_QR_Create(QRCodestr, 4, GUI_QR_ECLEVEL_H, 10);
        GUI_QR_Draw(QR_CODE, 260, 135);
    #elif (LCD_TYPE == LCD_35_TFT)
        QR_CODE = GUI_QR_Create(QRCodestr, 3, GUI_QR_ECLEVEL_H, 8);
        GUI_QR_Draw(QR_CODE, 150, 90);
    #else
        QR_CODE = GUI_QR_Create(QRCodestr, 2, GUI_QR_ECLEVEL_H, 8);
        GUI_QR_Draw(QR_CODE, 50, STATEBAR_TITLE_HEIGHT + 50);
    #endif
    
    GUI_QR_Delete(QR_CODE);
}

void DrawQRCodeScreen(void)
{
    //GUI_SetColor(BACKCOLOR_DEFAULT);//设置界面背景色-黑色(0, 0, 0)
    GUI_SetColor(GUI_WHITE);
    
    #if (LCD_TYPE == LCD_28_TFT)
        //GUI_FillRect(0, STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, Return_Y + STATEBAR_TITLE_HEIGHT - 20);
        GUI_FillRect(0, STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, SCREEN_HEIGHT);
//        SetUiItemText(15
//                  , 10 + STATEBAR_TITLE_HEIGHT
//                  , QRCODE_IDX
//                  , GUI_BLACK
//                  , FONT_18
//                  , FONT_18
//                  , g_sU8CurEditState_InfoSetting
//                  ,FALSE);    
    #else
        GUI_FillRect(0, STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, CHILDSETTINGSCREEN_HEIGHT + STATEBAR_TITLE_HEIGHT);
//        #if (LCD_TYPE == LCD_5_TFT)
//            SetUiItemText(74
//                          , 3 + STATEBAR_TITLE_HEIGHT
//                          , QRCODE_IDX
//                          , GUI_BLACK//GUI_WHITE
//                          , FONT_32
//                          , FONT_32
//                          , FALSE);
//        #elif (LCD_TYPE == LCD_35_TFT)
//            SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
//                          , 2 + STATEBAR_TITLE_HEIGHT
//                          , QRCODE_IDX
//                          , GUI_BLACK//GUI_WHITE
//                          , FONT_24
//                          , FONT_24
//                          , FALSE);
//        #endif
    #endif

    GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
    #if (LCD_TYPE == LCD_28_TFT)
        GUI_SetColor(GREEN);
        GUI_AA_FillCircle(Return_X,Return_Y + STATEBAR_TITLE_HEIGHT,20);
        DrawBMP(BMP_RETURNFOCU, Return_X-12, Return_Y-10 + STATEBAR_TITLE_HEIGHT);
    #else
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_AA_FillCircle(655, 408 + 33 - 70 + STATEBAR_TITLE_HEIGHT, 33);
            GUI_FillRect(655, 408 - 70 + STATEBAR_TITLE_HEIGHT, 655 + 133, 408 - 70 + 33 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
        #elif (LCD_TYPE == LCD_35_TFT)
            GUI_AA_FillCircle(393, 272 + 22 - 46 + STATEBAR_TITLE_HEIGHT, 22);
            GUI_FillRect(393, 272 - 46 + STATEBAR_TITLE_HEIGHT, 393 + 80, 272 - 46 + 22 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
        #endif
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(GetMultiLanguageString(MASKTEST_EXIT_IDX), &g_MenuIconRect, GUI_TA_HCENTER | GUI_TA_VCENTER);

        #if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(BMP_EXITICON2, 652, 356 + STATEBAR_TITLE_HEIGHT);
        #else
            DrawBMP(BMP_EXITICON2, 381, 232 + STATEBAR_TITLE_HEIGHT);
        #endif
    
    #endif   
    

////    #if (LCD_TYPE == LCD_5_TFT)
////        DrawMenuIcon(656, 429 - 70 + STATEBAR_TITLE_HEIGHT);
////    #elif (LCD_TYPE == LCD_35_TFT)
////        DrawMenuIcon(384, 286 - 46 + STATEBAR_TITLE_HEIGHT);
////    #endif
    
    ShowQRcode();
}

void RepaintInfoSettingScreen(void)
{
    if (((g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] == MAX_FOCUS_NUM_INFOSETTING_No95TH 
        && IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == QRCODE_IDX)
        || (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] == MAX_FOCUS_NUM_INFOSETTING_95TH
        && IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == QRCODE_IDX))
        && g_sU8CurEditState_InfoSetting)
    {
        DrawQRCodeScreen();
    }
    else
    {
        DrawInformationSettingScreen(g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, 1);    
    }
}

#if (LCD_TYPE == LCD_28_TFT)
static void DrawInformationSettingScreen(uint8_t u8Page, uint8_t u8IsFirst)
{
	uint8_t i,MAX;
    static uint8_t Disp[4] = {0, 0, 0, 0};
    static uint8_t LastPage = 0;
    WM_SelectWindow(WM_HBKWIN);

    if(u8Page > (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 2) /MAX_PAGE_LINE)
    {
        u8Page = (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 2) /MAX_PAGE_LINE;
    }

    if (u8IsFirst)
    {
        for (i = 0; i < 4; i++)
        {
            Disp[i] = 0;
        }
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);//设置界面背景色-黑色(0, 0, 0)
        if(u8IsFirst == 1)
        {
            LastPage = u8Page;
            GUI_FillRect(0, 0 + STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, MAINSCREEN_HEIGHT + STATEBAR_TITLE_HEIGHT);
        }
        else
            GUI_FillRect(0, 0 + STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, Return_Y-25 + STATEBAR_TITLE_HEIGHT);
    }
    if(LastPage != u8Page)
    {
        u8IsFirst = 2;//翻页不刷新返回
        LastPage = u8Page;
    }
    GUI_SetTextMode(GUI_TM_TRANS);  //设置背景透明
    GUI_SetFont(FONT_18);

    //if (u8Page != 2)
    if(u8Page != 2 || (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] -1) % MAX_PAGE_LINE == 0)
    {
        for(i=0;i<3;i++)
        {
            if(u8IsFirst 
               || (i == g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE 
               && g_InfoSettingScreenWInfo.U8CurFocusID < g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1) 
               || Disp[i] == 1)
            {
                if(i == g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE
                  && g_InfoSettingScreenWInfo.U8CurFocusID < g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1)
                {
                    if(u8IsFirst == 1)
                         GUI_SetColor(GREEN);
                    else
                        DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                    Disp[i] = 1;
                }
                else
                {
                    if(u8IsFirst == 1)
                        GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                    else
                        DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);
                    Disp[i] = 0;
                }
                if(u8IsFirst == 1)
                    GUI_AA_FillRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,234,59+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED);
                
                if(i == g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE
                   && g_InfoSettingScreenWInfo.U8CurFocusID < g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1)
                {
                    if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_95TH[u8Page][i]
                                      , g_sU8CurEditState_InfoSetting ? DEFAULT_MENU_FOCUSCOLOR : GUI_WHITE
                                      , FONT_18
                                      , g_sU8CurEditState_InfoSetting ? FONT_24 : FONT_18
                                      , g_sU8CurEditState_InfoSetting,g_sU8CurEditState_InfoSetting);
                    }
                    else
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_No95TH[u8Page][i]
                                      , g_sU8CurEditState_InfoSetting ? DEFAULT_MENU_FOCUSCOLOR : GUI_WHITE
                                      , FONT_18
                                      , g_sU8CurEditState_InfoSetting ? FONT_24 : FONT_18
                                      , g_sU8CurEditState_InfoSetting,g_sU8CurEditState_InfoSetting);
                    }
                }
                else
                {
                    if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_95TH[u8Page][i]
                                      , GUI_WHITE
                                      , FONT_18
                                      , FONT_18
                                      , FALSE,FALSE);                    
                    }
                    else
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_No95TH[u8Page][i]
                                      , GUI_WHITE
                                      , FONT_18
                                      , FONT_18
                                      , FALSE,FALSE);
                   }
                }
            }
        }
    }
    else
    {
        if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
            || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
            || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
        {
            MAX = 3;
        }
        else
        {
            MAX = 2;
        }
        for(i=0;i<MAX;i++)
        {
            if(u8IsFirst || (i == g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) || Disp[i] == 1)
            {
                if(i == g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE)
                {
                    if(u8IsFirst == 1)
                         GUI_SetColor(GREEN);
                    else
                        DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                    Disp[i] = 1;
                }
                else
                {
                    if(u8IsFirst == 1)
                        GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                    else
                        DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);
                    Disp[i] = 0;
                }
                if(u8IsFirst == 1)
                    GUI_AA_FillRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,234,59+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED);
                
                if(i == g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE
                   && g_InfoSettingScreenWInfo.U8CurFocusID < g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1)
                {
                    if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_95TH[u8Page][i]
                                      , g_sU8CurEditState_InfoSetting ? DEFAULT_MENU_FOCUSCOLOR : GUI_WHITE
                                      , FONT_18
                                      , g_sU8CurEditState_InfoSetting ? FONT_24 : FONT_18
                                      , g_sU8CurEditState_InfoSetting,g_sU8CurEditState_InfoSetting);                    
                    }
                    else
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_No95TH[u8Page][i]
                                      , g_sU8CurEditState_InfoSetting ? DEFAULT_MENU_FOCUSCOLOR : GUI_WHITE
                                      , FONT_18
                                      , g_sU8CurEditState_InfoSetting ? FONT_24 : FONT_18
                                      , g_sU8CurEditState_InfoSetting,g_sU8CurEditState_InfoSetting);
                    }
                }
                else
                {
                    if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_95TH[u8Page][i]
                                      , GUI_WHITE
                                      , FONT_18
                                      , FONT_18
                                      , FALSE,FALSE);
                    }
                    else
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_No95TH[u8Page][i]
                                      , GUI_WHITE
                                      , FONT_18
                                      , FONT_18
                                      , FALSE,FALSE);
                    }
                }
            }
        }
    }
    if (g_InfoSettingScreenWInfo.U8CurFocusID != g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] -1)
    {
        if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
            || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
            || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
        {
            SetUiItemValueAndUnitsText(IdType_Info_95TH[u8Page][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE], g_U8ChangeParaOnEditState);
        }
        else
        {
            SetUiItemValueAndUnitsText(IdType_Info_No95TH[u8Page][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE], g_U8ChangeParaOnEditState);
        }
    }
    
    if(u8IsFirst == 1|| (g_InfoSettingScreenWInfo.U8CurFocusID == g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] -1) || Disp[3] == 1)
    {
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
        GUI_FillRect(0, Return_Y + STATEBAR_TITLE_HEIGHT - 20, SCREEN_WIDTH, SCREEN_HEIGHT);
        
        if(g_InfoSettingScreenWInfo.U8CurFocusID == g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] -1)
        {
            GUI_SetColor(GREEN);
            Disp[3] = 1;
            GUI_AA_FillCircle(Return_X,Return_Y + STATEBAR_TITLE_HEIGHT,20);
            DrawBMP(BMP_RETURNFOCU, Return_X-12, Return_Y-10 + STATEBAR_TITLE_HEIGHT);
        }
        else
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            Disp[3] = 0;
            GUI_AA_FillCircle(Return_X,Return_Y + STATEBAR_TITLE_HEIGHT,20);
            DrawBMP(BMP_RETURN, Return_X-12, Return_Y-10 + STATEBAR_TITLE_HEIGHT);
        }

        DrawPageCircle(u8Page, 3);      
    }
    if(u8IsFirst == 2)
    DrawPageCircle(u8Page, 3);
    
   
}
#else
static void DrawInformationSettingScreen(uint8_t u8Page, uint8_t u8IsFirst)
{
	uint8_t i;
    uint8_t j;    
    
    //绘制背景条纹框
    DrawChildSettingScreenBackground();    

    GUI_SetTextMode(GUI_TM_TRANS);  //设置背景透明   
          
    if (g_InfoSettingScreenWInfo.U8CurFocusID != g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1)
    {
        GUI_SetColor(CHILD_INTERFACE_LIST_BORDERCOLOR_SELECTED);
        for (j = 0; j < 2; j++)
        {
            GUI_FillRect(LIST_X_POS
                        , (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + EACHLIST_HEIGHT * j + STATEBAR_TITLE_HEIGHT
                        , SCREEN_WIDTH - LIST_X_POS
                        , (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + EACHLIST_HEIGHT * j + LIST_BORDER_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
        }

        GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
        GUI_FillRect(LIST_X_POS
                    , (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + STATEBAR_TITLE_HEIGHT
                    , SCREEN_WIDTH - LIST_X_POS
                    , (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
        
        #if (LCD_TYPE == LCD_5_TFT)
            for (i = 0 + g_InfoSettingScreenWInfo.U8CurFocusID / 5 * 5; 
                 i < (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1) && i < (g_InfoSettingScreenWInfo.U8CurFocusID / 5 + 1) * 5; 
                 i++)
            {
                if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                    || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                    || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
                {
                    if (g_InfoSettingScreenWInfo.U8CurFocusID == g_InfoSettingScreenWInfo.U8CurFocusID / 5 * 5 + i % 5)
                        SetUiItemText(74
                                      , 3 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_95TH[u8Page][i % 5]
                                      , g_sU8CurEditState_InfoSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                      , FONT_32
                                      , g_sU8CurEditState_InfoSetting ? FONT_38 : FONT_32
                                      , g_sU8CurEditState_InfoSetting);
                    else
                        SetUiItemText(74
                                      , 3 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_95TH[u8Page][i % 5]
                                      , GUI_WHITE
                                      , FONT_32
                                      , FONT_32
                                      , FALSE);
                }
                else
                {
                    if (g_InfoSettingScreenWInfo.U8CurFocusID == g_InfoSettingScreenWInfo.U8CurFocusID / 5 * 5 + i % 5)
                        SetUiItemText(74
                                      , 3 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_No95TH[u8Page][i % 5]
                                      , g_sU8CurEditState_InfoSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                      , FONT_32
                                      , g_sU8CurEditState_InfoSetting ? FONT_38 : FONT_32
                                      , g_sU8CurEditState_InfoSetting);
                    else
                        SetUiItemText(74
                                      , 3 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_No95TH[u8Page][i % 5]
                                      , GUI_WHITE
                                      , FONT_32
                                      , FONT_32
                                      , FALSE);
                }

            }        
        #else
            for (i = 0 + g_InfoSettingScreenWInfo.U8CurFocusID / 5 * 5; 
                 i < (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1) && i < (g_InfoSettingScreenWInfo.U8CurFocusID / 5 + 1) * 5; 
                 i++)
            {
                if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                    || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                    || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
                {
                    if (g_InfoSettingScreenWInfo.U8CurFocusID == g_InfoSettingScreenWInfo.U8CurFocusID / 5 * 5 + i % 5)
                        SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                      , 2 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_95TH[u8Page][i % 5]
                                      , g_sU8CurEditState_InfoSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                      , FONT_24
                                      , g_sU8CurEditState_InfoSetting ? FONT_32 : FONT_24
                                      , g_sU8CurEditState_InfoSetting);
                    else
                        SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                      , 2 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_95TH[u8Page][i % 5]
                                      , GUI_WHITE
                                      , FONT_24
                                      , FONT_24
                                      , FALSE);
                }
                else
                {
                    if (g_InfoSettingScreenWInfo.U8CurFocusID == g_InfoSettingScreenWInfo.U8CurFocusID / 5 * 5 + i % 5)
                        SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                      , 2 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_No95TH[u8Page][i % 5]
                                      , g_sU8CurEditState_InfoSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                      , FONT_24
                                      , g_sU8CurEditState_InfoSetting ? FONT_32 : FONT_24
                                      , g_sU8CurEditState_InfoSetting);
                    else
                        SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                      , 2 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Info_No95TH[u8Page][i % 5]
                                      , GUI_WHITE
                                      , FONT_24
                                      , FONT_24
                                      , FALSE);
                }

            }
        #endif

//        if (g_U8ChangeParaOnEditState == FALSE)
        {
            if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
            {
                SetUiItemValueAndUnitsText(IdType_Info_95TH[u8Page][g_InfoSettingScreenWInfo.U8CurFocusID % 5], g_U8ChangeParaOnEditState);
            }
            else
            {
                SetUiItemValueAndUnitsText(IdType_Info_No95TH[u8Page][g_InfoSettingScreenWInfo.U8CurFocusID % 5], g_U8ChangeParaOnEditState);
            }
        }

        //菜单图标
        GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_AA_FillCircle(655, 408 + 33 - 70 + STATEBAR_TITLE_HEIGHT, 33);
            GUI_FillRect(655, 408 - 70 + STATEBAR_TITLE_HEIGHT, 655 + 133, 408 - 70 + 33 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
        #else
            GUI_AA_FillCircle(393, 272 + 22 - 46 + STATEBAR_TITLE_HEIGHT, 22);
            GUI_FillRect(393, 272 - 46 + STATEBAR_TITLE_HEIGHT, 393 + 80, 272 - 46 + 22 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
        #endif
    }
    else if (g_InfoSettingScreenWInfo.U8CurFocusID == g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1)
    {
        #if (LCD_TYPE == LCD_5_TFT)
            if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
            {
                for(i = 0; i < 5; i++)
                    SetUiItemText(74
                                  , 3 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_95TH[u8Page][i]
                                  , GUI_WHITE
                                  , FONT_32
                                  , FONT_32
                                  , g_sU8CurEditState_InfoSetting);
            }
            else
            {
                for(i = 0; i < 4; i++)
                    SetUiItemText(74
                                  , 3 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_No95TH[u8Page][i]
                                  , GUI_WHITE
                                  , FONT_32
                                  , FONT_32
                                  , g_sU8CurEditState_InfoSetting);
            }
        #else
            if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
            {
                for(i = 0; i < 5; i++)
                {
                    SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                  , 2 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_95TH[u8Page][i]
                                  , GUI_WHITE
                                  , FONT_24
                                  , FONT_24
                                  , g_sU8CurEditState_InfoSetting);
                }
            }
            else
            {
                for(i = 0; i < 4; i++)
                {
                    SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                  , 2 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_No95TH[u8Page][i]
                                  , GUI_WHITE
                                  , FONT_24
                                  , FONT_24
                                  , g_sU8CurEditState_InfoSetting);
                }
            }
        #endif

        GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_AA_FillCircle(655, 408 + 33 - 70 + STATEBAR_TITLE_HEIGHT, 33);
            GUI_FillRect(655, 408 - 70 + STATEBAR_TITLE_HEIGHT, 655 + 133, 408 - 70 + 33 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
        #else
            GUI_AA_FillCircle(393, 272 + 22 - 46 + STATEBAR_TITLE_HEIGHT, 22);
            GUI_FillRect(393, 272 - 46 + STATEBAR_TITLE_HEIGHT, 393 + 80, 272 - 46 + 22 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
        #endif        
    }

    //底下的页面小圆点
    DrawPageCircle(u8Page, 2);    
    
    GUI_SetColor(GUI_WHITE);
    GUI_DispStringInRect(GetMultiLanguageString(MENU_IDX), &g_MenuIconRect, GUI_TA_HCENTER | GUI_TA_VCENTER);
    #if (LCD_TYPE == LCD_5_TFT)
        DrawMenuIcon(656, 429 - 70 + STATEBAR_TITLE_HEIGHT);
    #else
        DrawMenuIcon(384, 286 - 46 + STATEBAR_TITLE_HEIGHT);
    #endif
}
#endif

static void OnEnterClick_InformationSetting(void)
{
    if(g_InfoSettingScreenWInfo.U8CurFocusID == g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] - 1)
    {        
        ShowInfoSettingScreen(0);

        g_InfoSettingScreenWInfo.U8CurFocusID = 0;
#if (LCD_TYPE != LCD_28_TFT)
        g_InfoSettingScreenWInfo.U8OldFocusID = 0;
#endif
    }
    else if(g_InfoSettingScreenWInfo.U8CurFocusID == 0)
    {
        WM_SelectWindow(WM_HBKWIN);
	#if (LCD_TYPE == LCD_28_TFT)
        DrawRoundedRect(5,10+ STATEBAR_TITLE_HEIGHT+58*(g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE),235,60+ STATEBAR_TITLE_HEIGHT+58*(g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE),ROUNDED,GREEN); 
	#else
		GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
        GUI_FillRect(LIST_X_POS
                    , LIST_BORDER_HEIGHT + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                    , EACHLIST_WIDTH - 1
                    , LIST_BORDER_HEIGHT + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
	#endif
        
        if (g_sU8CurEditState_InfoSetting == FALSE)
        {
            if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
            {
                #if (LCD_TYPE == LCD_5_TFT)
                    SetUiItemText(74
                                  , 3 + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / 5][g_InfoSettingScreenWInfo.U8CurFocusID % 5]
                                  , FONTCOLOR_SELECTED
                                  , FONT_32
                                  , FONT_38
                                  , g_sU8CurEditState_InfoSetting);
                #elif (LCD_TYPE == LCD_28_TFT) 
                    SetUiItemText(15
                              , 28 + (g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                              , IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                              , DEFAULT_MENU_FOCUSCOLOR
                              , FONT_18
                              , FONT_24
                              , g_sU8CurEditState_InfoSetting,TRUE);
                #else
                    SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                  , 2 + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / 5][g_InfoSettingScreenWInfo.U8CurFocusID % 5]
                                  , FONTCOLOR_SELECTED
                                  , FONT_24
                                  , FONT_32
                                  , g_sU8CurEditState_InfoSetting);
                #endif            
            }
            else
            {
                #if (LCD_TYPE == LCD_5_TFT)
                    SetUiItemText(74
                                  , 3 + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / 5][g_InfoSettingScreenWInfo.U8CurFocusID % 5]
                                  , FONTCOLOR_SELECTED
                                  , FONT_32
                                  , FONT_38
                                  , g_sU8CurEditState_InfoSetting);
                #elif (LCD_TYPE == LCD_28_TFT) 
                    SetUiItemText(15
                              , 28 + (g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                              , IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                              , DEFAULT_MENU_FOCUSCOLOR
                              , FONT_18
                              , FONT_24
                              , g_sU8CurEditState_InfoSetting,TRUE);
                #else
                    SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                  , 2 + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / 5][g_InfoSettingScreenWInfo.U8CurFocusID % 5]
                                  , FONTCOLOR_SELECTED
                                  , FONT_24
                                  , FONT_32
                                  , g_sU8CurEditState_InfoSetting);
                #endif
            }
                
            
            g_sU8CurEditState_InfoSetting = TRUE;
        }
        else
        {
            //退出编辑状态，将参数写入系统
            if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
            {
                #if (LCD_TYPE == LCD_5_TFT)
                    SetUiItemText(74
                                  , 3 + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / 5][g_InfoSettingScreenWInfo.U8CurFocusID % 5]
                                  , FONTCOLOR_DEFAULT
                                  , FONT_32
                                  , FONT_32
                                  , g_sU8CurEditState_InfoSetting);
                #elif (LCD_TYPE == LCD_28_TFT) 
                    SetUiItemText(15
                                  , 28 + (g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                                  , GUI_WHITE
                                  , FONT_18
                                  , FONT_18
                                  , g_sU8CurEditState_InfoSetting,FALSE);
                #else
                    SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                  , 2 + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / 5][g_InfoSettingScreenWInfo.U8CurFocusID % 5]
                                  , FONTCOLOR_DEFAULT
                                  , FONT_24
                                  , FONT_24
                                  , g_sU8CurEditState_InfoSetting);
                #endif            
            }
            else
            {
                #if (LCD_TYPE == LCD_5_TFT)
                    SetUiItemText(74
                                  , 3 + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / 5][g_InfoSettingScreenWInfo.U8CurFocusID % 5]
                                  , FONTCOLOR_DEFAULT
                                  , FONT_32
                                  , FONT_32
                                  , g_sU8CurEditState_InfoSetting);
                #elif (LCD_TYPE == LCD_28_TFT) 
                    SetUiItemText(15
                                  , 28 + (g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                                  , GUI_WHITE
                                  , FONT_18
                                  , FONT_18
                                  , g_sU8CurEditState_InfoSetting,FALSE);
                #else
                    SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                  , 2 + (g_InfoSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / 5][g_InfoSettingScreenWInfo.U8CurFocusID % 5]
                                  , FONTCOLOR_DEFAULT
                                  , FONT_24
                                  , FONT_24
                                  , g_sU8CurEditState_InfoSetting);
                #endif
            }
			
            
            g_sU8CurEditState_InfoSetting = FALSE;
            
            //将参数写入系统
            if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
                || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
            {
                SaveTheValueChanged(IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]);                            
            }
            else
            {
                SaveTheValueChanged(IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]);                            
            }
            RepaintInfoSettingScreen(); //刷新对应参数
        }
    }
    else
    {
        OnEnterClick_QRCode();
    }
}

static void OnEnterClick_QRCode(void)
{
    if ((g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] == MAX_FOCUS_NUM_INFOSETTING_No95TH 
        && IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == QRCODE_IDX)
        || (g_sU8InfoSettingMaxFocus[g_ConfigSave.GetParameter(WORKMODE)] == MAX_FOCUS_NUM_INFOSETTING_95TH
        && IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == QRCODE_IDX))
    {
        WM_SelectWindow(WM_HBKWIN);

        if (g_sU8CurEditState_InfoSetting == FALSE)
        {
            UpdateTopStateBarMenuString(GetMultiLanguageString(QRCODE_IDX));
            DrawQRCodeScreen();
            g_sU8CurEditState_InfoSetting = TRUE;
        }
        else
        {
            g_sU8CurEditState_InfoSetting = FALSE;

            UpdateTopStateBarMenuString(GetMultiLanguageString(INFOSETTING_IDX));

            #if (LCD_TYPE == LCD_28_TFT)
                DrawInformationSettingScreen(g_InfoSettingScreenWInfo.U8CurFocusID / 3, TRUE);
            #else
                DrawInformationSettingScreen(g_InfoSettingScreenWInfo.U8CurFocusID / 5, FALSE);
            #endif
        }
    }
}

static void DrawOptionInfoSettingKeyLeft(void)
{
    if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
    {
        DrawingOptions(TRUE
                     , IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                     , g_InfoSettingScreenWInfo.U8CurFocusID);    
    }
    else
    {
        DrawingOptions(TRUE
                     , IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                     , g_InfoSettingScreenWInfo.U8CurFocusID);
    }
}

static void DrawOptionInfoSettingKeyRight(void)
{
    if (g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_APAP 
        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_IAPAP
        || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_AUTOB)
    {
        DrawingOptions(FALSE
                     , IdType_Info_95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                     , g_InfoSettingScreenWInfo.U8CurFocusID);    
    }
    else
    {        
        DrawingOptions(FALSE
                     , IdType_Info_No95TH[g_InfoSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_InfoSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                     , g_InfoSettingScreenWInfo.U8CurFocusID);
    }
}

void ShowInfoSettingScreen(uint8_t Flag)
{
	if (Flag)
	{
        g_sU8CurEditState_InfoSetting = FALSE;
        g_InfoSettingScreenWInfo.U8CurFocusID = 0;
        g_InfoSettingScreenWInfo.U8OldFocusID = 0;
        ChangeIdx = CHECKING_IDX;

        UpdateTopStateBarMenuString(GetMultiLanguageString(INFOSETTING_IDX));
//        RepaintInfoSettingScreen();
        g_WindowDrv.PushWindow(&g_InfoSettingScreenWInfo);
	}
	else
	{
        UpdateTopStateBarMenuString(GetMultiLanguageString(MENU_IDX));
        g_WindowDrv.PopWindow(&g_InfoSettingScreenWInfo);
	}
    UpdateIconStatus(DISPLAY_LOCKED_ICON_INDICATION_BIT, !Flag);    
}

