#ifndef  __INCLUDES_H__
#define  __INCLUDES_H__

/*
*********************************************************************************************************
*                                         标准库
*********************************************************************************************************
*/

#include  <stdarg.h>
#include  <stdio.h>
#include  <stdlib.h>
#include  <math.h>
#include <stdint.h>


/*
*********************************************************************************************************
*                                         其它库
*********************************************************************************************************
*/

#include  <cpu.h>
#include  <lib_def.h>
#include  <lib_ascii.h>
#include  <lib_math.h>
#include  <lib_mem.h>
#include  <lib_str.h>
#include  <app_cfg.h>


/*
*********************************************************************************************************
*                                           OS
*********************************************************************************************************
*/

#include  <os.h>


/*
*********************************************************************************************************
*                                           宏定义
*********************************************************************************************************
*/




/*
*********************************************************************************************************
*                                        APP / BSP
*********************************************************************************************************
*/

//#include  <bsp.h>
#include "bsp_os.h"

/*
*********************************************************************************************************
*                                          变量和函数
*********************************************************************************************************
*/
/* 方便RTOS里面使用 */
extern void SysTick_ISR(void);

#define bsp_ProPer1ms  SysTick_ISR


#endif

