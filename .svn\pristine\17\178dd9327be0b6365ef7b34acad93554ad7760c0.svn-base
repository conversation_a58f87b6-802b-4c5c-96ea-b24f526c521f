#ifndef __STARTINTERFACE_H__
#define __STARTINTERFACE_H__

#include "GUI.h"
#include "DIALOG.h"
#include "WM.h"
#include "BUTTON.h"
#include "CHECKBOX.h"
#include "DROPDOWN.h"
#include "EDIT.h"
#include "FRAMEWIN.h"
#include "LISTBOX.h"
#include "MULTIEDIT.h"
#include "RADIO.h"
#include "SLIDER.h"
#include "TEXT.h"
#include "PROGBAR.h"
#include "SCROLLBAR.h"
#include "LISTVIEW.h"
#include "GRAPH.h"
#include "MENU.h"
#include "MULTIPAGE.h"
#include "ICONVIEW.h"
#include "TREEVIEW.h"
#include "MultiLanguage.h"

#include "DataType.h"

#if (LCD_TYPE == LCD_5_TFT)
#define LOGO_XPOS   ((800 - 296) / 2)
#define LOGO_YPOS   150
#define STARTUP_REFRESH_XPOS    500
#else
#define LOGO_XPOS   ((480 - 178) / 2)
#define LOGO_YPOS   100
#define STARTUP_REFRESH_XPOS    318
#endif

extern void DisplayLogoVersion();

#endif


