#ifndef __EDFSAVE_H__
#define __EDFSAVE_H__

#include "includes.h"
#include "stdbool.h"
#include "bsp_rtc_ds1302.h"
#define ABNORMAL_OUTAGE_FLAG         0xAA
#define EDF_STORAGE_DAYS             365
#define DATA_VALIDITY_TIME           36000
#define CLEAR_DATA_BUF_SIZE          4096
typedef enum CLEAREDFDADAType
{  
	CLEAR_12HOUR_EVENT,
	CLEAR_RUN_12HOUR_EVENT_OR_RECOVER,
	START_CLEAR_EDF,
}CLEAREDFDATA;
typedef enum SHOWINFODATA
{  
	SHOWAVGPRESS,
	SHOWAVG95PRESS,
	SHOWAVELEAKFLOW,
	SHOWAHI,
	SHOWEFFDAYS,
	SHOWUSETIME,
}SHOWINFODATA;
typedef struct
{
	uint32_t u32ShowUseTime;
    uint32_t u32ShowAvgPress;
    uint32_t u32AvgFlowLeak;
    uint32_t u32ShowAhi;
    uint32_t u32ShowEffTreatDays;
    uint32_t u32Show95th;
}EefStaticShowData;
//#pragma pack(1)
typedef struct
{
    uint8_t u8Poweroff;
    uint32_t u32PowerOnDuration;   //
	uint16_t u16RtcEdfBackAddr;
} DATA_SAVE_BackUp_Typedef;
//#pragma pack()

typedef struct
{
	uint32_t u32TotalPree;
	uint32_t u32TotalFlowLeak;
	uint32_t u32TotalSO2;
	uint32_t u32TotalVT;
	uint32_t u32TotalMV;
	uint32_t u32s32TotalBmp;
	uint32_t u32InspNumber;
	uint32_t u32ColNumber;
}RUNTOTALDATA;
typedef struct
{
    uint32_t u32UseTime; //
    uint16_t u16UseDays;
    uint16_t u16EffectUseDays; //

    uint16_t u16AI;
    uint16_t u16HI;
    uint16_t u16AHI;

    union
    {
        uint16_t u16MaxWorkPress;
        uint16_t u16MaxIPAP;
    } MaxPress;
    union
    {
        uint16_t u16_95thWorkPress;
        uint16_t u16_95thIPAP;
    } _95thPress;
    union
    {
        uint16_t u16AvgWorkPress;
        uint16_t u16AvgIPAP;
    } AvgPress;


    uint16_t u16MaxFlowLeak;
    uint16_t u16AvgFlowLeak;

    uint16_t u16MaxVt;
    uint16_t u16AvgVt;

    uint16_t u16MaxMv;
    uint16_t u16AvgMv;

    uint16_t u16MaxBpm;
    uint16_t u16AvgBpm;

    uint16_t u16MaxSPO2;
    uint16_t u16AvgSPO2;
    uint16_t u16MinSPO2;
    
    uint16_t u16_95thSmartWorkPress;        //IAPAP智能压力
    uint32_t u32NoRampUseTime;
    
    /*uint8_t u16MaxPR;
    uint8_t u16AvgPR;
    uint8_t u16MinPR;
    
    uint16_t u16CheyneStokes;   //潮式呼吸
    uint16_t u16Snore;          //鼾声事件
    uint16_t u16Central;        //中枢性*/
    
    uint16_t u16Odi;            //氧减指数
	uint8_t  u8CheckNum;
} StatisticInfo_Typedef;     //结构体最大长度是64个字节

#pragma pack(1)
typedef struct
{
	uint8_t  u8DataType;
	char     EdfFlag[3];
	uint16_t u16RrecordNumber;
	uint8_t  u8StartYear;
	uint8_t  u8StartMon;
	uint8_t  u8Startday;
	uint8_t  u8StartHour;
	uint8_t  u8StartMin;
	uint8_t  u8StartSec;
	uint16_t  u16lastYearNumberOfRecords;
	uint8_t u8CheckNum;
}EefHeadData;
#pragma pack()
typedef struct
{
	uint16_t  u16NumberOfRecords;
	uint16_t  u16lastYearNumberOfRecords;
	uint16_t  u16EdfBackAddr;
	uint32_t  u32LastWriteAddrForTodayStaData;
	uint32_t  u32WriteTodayStorageAddr;
}EDFRUNSYSINFO;
extern EefStaticShowData RunShowData;
extern DATA_SAVE_BackUp_Typedef g_VarForDataSave;
extern RUNTOTALDATA EdfRunTotal;
extern EDFRUNSYSINFO EdfRunInfo;
void WriteEdfBackData(void);
void ShutdownSaveEdf(void);
uint16_t Cal95thPress(uint16_t * g_dotCntAddr);
void EdfInit(void);
void BackUpDataForPowerLost(uint8_t u8PowrFlag,uint32_t u32RunTime,uint16_t u16EdfBackAddr);
void DATA_SAVE_BackUpDataForPowerLost(uint32_t u32SysTickTimer);
void TimeStored(uint32_t u32RunTime, uint32_t u32RampTime);
void TimeSaveEdfBackData(void);
void PostAcrossDayEvent(void);
void AcrossDayEvent(void);
void SticalAvgWorkPress(int16_t s16Press);
void SticalAvgFlowLeakAndMaxFlowLeak(int16_t s16FlowLeak);
void SticalMaxWorkPrss(int16_t s16Press);
void SticalSPO2(void);
void SticalBpm(uint16_t u16Bmp);
void SticalMV(uint16_t u16MV);
void SticalVT(uint16_t u16InspTV);
void StutdownDisposeEdf(uint32_t u32UseTime);
void SetCompenRunTime(uint32_t u32ime);
void ClearStaticData(CLEAREDFDATA Type);
void CalEdfStatisticalData(void);
uint32_t GetSticEdfData(SHOWINFODATA ShowType);
void StatiAiAndAhi(void);
void SystemRecovEdfClear(void);
I16 AcrossDayEdfDtaDispose(RTC_TimeTypeDef1 RTC_Time, EDFRUNSYSINFO *RunInfo);
void PVD_Save_Edf_Data(void);
uint16_t CalSmartPressure(void);
uint8_t UtilFunSort(uint16_t *u16Arr, uint8_t u8Len);
void UpdateSmartPressure(void);

extern EefStaticShowData RunShowData;

#endif
