#ifndef __BSP_SPO2_UART_H_
#define __BSP_SPO2_UART_H_
#include <stdint.h>
#include "buffer/buffer.h"
#pragma pack(1)
typedef struct
{
#define SPO2_PROTOCOL_HEADR             (0xfcfa)    
#define SPO2_DATA_LEN                   (6)    
#define SPO2_DECODE_PROTOCOL_LEN        (12)    
    uint16_t hPackHead;
    uint8_t  bCmdCode;
    uint8_t  bDefault;
    uint8_t  bLen;
    uint8_t  data[6];
    uint8_t  bCkSum;
} SPO2ModuleProtocol_t;
#pragma pack()

int task_spo2_event_poll(RingBuffer_t *pRbBuff);


#endif



