/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * <h2><center>&copy; Copyright (c) 2022 STMicroelectronics.
 * All rights reserved.</center></h2>
 *
 * This software component is licensed by ST under Ultimate Liberty license
 * SLA0044, the "License"; You may not use this file except in compliance with
 * the License. You may obtain a copy of the License at:
 *                             www.st.com/SLA0044
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "fatfs.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "DataType.h"
#include "bsp_os.h"
#include "os.h"
#include "bsp.h"
#include "SSD1963.h"

#include "includes.h"
#include "GuiTask.h"
#include "Key.h"
#include "bsp_rtc_ds1302.h"
#include "bsp_MX25L256.h"
#include "OxiModule.h"
#include "AlarmManager.h"
#include "ConfigSave.h"
#include "GlobalVariable.h"
#include "bsp_bldcm_control.h"
#include "sdp800.h"
#include "SignalCondition.h"
#include "parameters_calc.h"
#include "Humidi.h"
#include "PCUartModule.h"
#include "SdcardDataSave.h"
#include "LCDConf_Lin.h"
#include "bsp_sdcard.h"
#if (NET_COMMUNICATION_TYPE == 0)
    #include "WifiUartModule.h"
#else
    #include "SIM7600Module.h"
#endif
#include "PipeAndHumCtr.h"
#include "SelfTest.h"
#include "EdfDataSave.h"
#include "TaskResourceManager.h"
#include "PipeDispose.h"
#include "bsp_mcu_uart.h"
#include "stm32f4xx_hal.h"

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
__IO U32 g_U32ADC1Value[16]; // __attribute__((section(".ARM.__at_0x24000000")));
__IO U32 g_U32ADC1Idx = 0;
//__IO U32 g_U32ADC2Value[8];// __attribute__((section(".ARM.__at_0x24000000")));
//__IO U32 g_U32ADC2Idx = 0;
//__attribute__((section (".RW_IRAM2"))) __IO U32 g_U32ADC3Value[32];// __attribute__((section(".ARM.__at_0x24000000")));
__IO U16 g_U16ADC3Value[40]; // __attribute__((section(".ARM.__at_0x24000000")));// __attribute__((section(".ARM.__at_0x24000000")));
__IO U16 g_U16PressValue[8];
__IO U32 g_U32ADC3Idx = 0;
#if (ADC3_SAMPLE_STYLE == 1)
    __IO U32 g_U32ADC3Idx = 0;
#endif
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
static void AppTaskStart(void *p_arg);
static void AppTaskUserIF(void *p_arg);
#ifdef __USER_DEBUG
    Jscope_TypeDef scope;
#endif
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
ADC_HandleTypeDef hadc1;
// ADC_HandleTypeDef hadc2;
ADC_HandleTypeDef hadc3;
DMA_HandleTypeDef hdma_adc3;
CRC_HandleTypeDef hcrc;

DAC_HandleTypeDef hdac;

I2C_HandleTypeDef hi2c2;

RTC_HandleTypeDef hrtc;

SPI_HandleTypeDef hspi1;
SPI_HandleTypeDef hspi2;
DMA_HandleTypeDef hdma_spi1_rx;
DMA_HandleTypeDef hdma_spi1_tx;
DMA_HandleTypeDef hdma_spi2_rx;
DMA_HandleTypeDef hdma_spi2_tx;

DMA_HandleTypeDef hdma_usart3_rx;

TIM_HandleTypeDef htim1;
TIM_HandleTypeDef htim2;
TIM_HandleTypeDef htim3;
TIM_HandleTypeDef htim5;
TIM_HandleTypeDef htim6;
TIM_HandleTypeDef htim7;
TIM_HandleTypeDef htim8;
TIM_HandleTypeDef htim9;
TIM_HandleTypeDef htim13;
TIM_HandleTypeDef htim14;

UART_HandleTypeDef huart1; // wifi
UART_HandleTypeDef huart3; // 血氧
UART_HandleTypeDef huart6; // 蓝牙

SRAM_HandleTypeDef hsram1;

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_DMA_Init(void);
static void MX_CRC_Init(void);
static void MX_FSMC_Init(void);
static void MX_ADC3_Init(void);
static void MX_ADC1_Init(void);
// static void MX_ADC2_Init(void);
static void MX_DAC_Init(void);
static void MX_I2C2_Init(void);
static void MX_SPI1_Init(void);
static void MX_SPI2_Init(void);
static void MX_TIM2_Init(void);
static void MX_TIM3_Init(void);
static void MX_TIM5_Init(void);
static void MX_TIM7_Init(void);
static void MX_TIM8_Init(void);
static void MX_TIM9_Init(void);
static void MX_TIM14_Init(void);
static void MX_USART1_UART_Init(void);
static void MX_USART3_UART_Init(void);
static void MX_USART6_UART_Init(void);
static void MX_RTC_Init(void);
static void MX_TIM6_Init(void);
static void MX_TIM1_Init(void);
static void MX_TIM13_Init(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
static OS_TCB AppTaskStartTCB;
static CPU_STK AppTaskStartStk[APP_CFG_TASK_START_STK_SIZE];

#if (DEBUG_TASK_RESOURCE == 1)
    static OS_TCB AppTaskUserIFTCB;
    static CPU_STK AppTaskUserIFStk[APP_CFG_TASK_USER_IF_STK_SIZE];

    static OS_SEM AppPrintfSemp; /* 用于printf互斥 */
#endif

static void AppTaskStart(void *p_arg);
// static  void  AppTaskUserIF         (void     *p_arg);
static void DispTaskInfo(void);
static void AppObjCreate(void);
// static  void  App_Printf (CPU_CHAR *format, ...);

/**
 * @brief  串口3波特率设置
 * @param  wNewBaudRate 波特率
 * @param
 * @return
 */
void uart3_change_baudrate(uint32_t wNewBaudRate)
{
    if(HAL_UART_DeInit(&huart3) != HAL_OK)
    {
        Error_Handler();
    }
    HAL_DMA_Abort(&hdma_usart3_rx); // 停止DMA传输

    huart3.Instance = USART3;
    huart3.Init.BaudRate = wNewBaudRate;
    huart3.Init.WordLength = UART_WORDLENGTH_8B;
    huart3.Init.StopBits = UART_STOPBITS_1;
    huart3.Init.Parity = UART_PARITY_NONE;
    huart3.Init.Mode = UART_MODE_TX_RX;
    huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart3.Init.OverSampling = UART_OVERSAMPLING_16;
    if(HAL_UART_Init(&huart3) != HAL_OK)
    {
        Error_Handler();
    }

    // 开启DMA传输
    SET_BIT(huart3.Instance->CR3, USART_CR3_DMAR);
    HAL_DMA_Start(&hdma_usart3_rx, (uint32_t)&huart3.Instance->DR, (uint32_t)g_mcuUartRxBuff, MCU_UART_RX_BUF_LEN);
}

void Uart3ReInit(void)
{
    if(HAL_UART_DeInit(&huart3) != HAL_OK)
    {
        Error_Handler();
    }
    MX_USART3_UART_Init();
}

void SPI2ReInit(void)
{
    if(HAL_SPI_DeInit(&hspi2) != HAL_OK)
    {
        Error_Handler();
    }
    MX_SPI2_Init();
}

volatile U32 g_StartUpStatus = 0;


void Set_ReadOutProtection(uint8_t level) {
    HAL_FLASH_OB_Unlock();  // 解锁选项字节

    FLASH_OBProgramInitTypeDef OBInit;
    HAL_FLASHEx_OBGetConfig(&OBInit);  // 获取当前选项字节配置

    OBInit.OptionType = OPTIONBYTE_RDP;  // 设置选项字节类型为RDP（读保护）
    OBInit.RDPLevel = level;  // 设置读保护等级

    HAL_FLASHEx_OBProgram(&OBInit);  // 编程选项字节

    HAL_FLASH_OB_Launch();  // 重新加载选项字节

    HAL_FLASH_OB_Lock();  // 锁定选项字节
}

/* USER CODE END 0 */
/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{
    /* USER CODE BEGIN 1 */
    OS_ERR err;

#if (ENABLE_VECT_OFFSET == 1)
    __set_FAULTMASK(0);
#endif

    g_StartUpStatus = 1;

    /* USER CODE END 1 */

    /* MCU Configuration--------------------------------------------------------*/

    /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
    HAL_Init();

    /* USER CODE BEGIN Init */



    /* USER CODE END Init */

    /* Configure the system clock */
    SystemClock_Config();
#if (__SWD_DISABLE == 1)
    // 设置读保护等级为Level 1
    Set_ReadOutProtection(OB_RDP_LEVEL_1);  // 设置读保护等级为Level 1
#else
    Set_ReadOutProtection(OB_RDP_LEVEL_0);  // 设置读保护等级为Level 0
#endif  

    /* USER CODE BEGIN SysInit */
    MX_CRC_Init();
    // 申请串口缓存数据
    mcu_cp_port_buffer_init();
    extern void upgrade_boot_by_app(void);
    upgrade_boot_by_app();
    g_StartUpStatus = 2;
    /* USER CODE END SysInit */

    /* Initialize all configured peripherals */
    MX_GPIO_Init();
    MX_DMA_Init();
    g_StartUpStatus = 3;
    MX_USART3_UART_Init();
    g_StartUpStatus = 4;
    // printf("g_StartUpStatus=%d\r\n", g_StartUpStatus);
    MX_TIM1_Init();
    MX_TIM2_Init();
    MX_TIM3_Init();
    MX_TIM5_Init();
    MX_TIM7_Init();
    MX_TIM8_Init();
    MX_TIM9_Init();
//  MX_TIM14_Init();
#if (MOTOR_DRIVER_TYPE == 0)
    MX_TIM6_Init();
#endif

    MX_FSMC_Init();
    MX_FATFS_Init();
    MX_ADC3_Init();
    MX_ADC1_Init();
    //  MX_ADC2_Init();
    //  MX_DAC_Init();
    MX_I2C2_Init();
    MX_SPI1_Init();
    MX_SPI2_Init();
    MX_USART1_UART_Init();
    //  MX_RTC_Init();
    MX_TIM13_Init();
    /* USER CODE BEGIN 2 */
    g_StartUpStatus = 5;
    // printf("g_StartUpStatus=%d\r\n", g_StartUpStatus);
#if (ENABLE_VECT_OFFSET == 1)
    __set_FAULTMASK(0);
#endif
    g_StartUpStatus = 6;
    // printf("g_StartUpStatus=%d\r\n", g_StartUpStatus);

    HAL_ADC_Start_IT(&hadc1);
    HAL_ADC_Start(&hadc1);

    g_StartUpStatus = 7;
    // printf("g_StartUpStatus=%d\r\n", g_StartUpStatus);
    if(HAL_ADC_Start_DMA(&hadc3, (uint32_t *)g_U16ADC3Value, 40))
    {
        Error_Handler();
    }
    g_StartUpStatus = 8;
    // printf("g_StartUpStatus=%d\r\n", g_StartUpStatus);

    HAL_GPIO_WritePin(YY_DATA_GPIO_Port, YY_DATA_Pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(GPIOE, GPIO_PIN_6, GPIO_PIN_SET);

    HAL_UART_Receive_IT(&huart1, g_U8UartWifiRcvBuf, WIFI_ISR_RCV_LENGTH);
    __HAL_UART_ENABLE_IT(&huart1, UART_IT_RXNE);
    g_StartUpStatus = 9;

    g_U16PipePWM = 0;


    /* 内核开启前关闭HAL的时间基准 */
    HAL_SuspendTick();

    /* 初始化滴答时钟，在启动任务里面开启 */
    BSP_OS_TickInit();

    /* 初始化uC/OS-III 内核 */
    OSInit(&err);

    /* 创建一个启动任务（也就是主任务）。启动任务会创建所有的应用程序任务 */
    OSTaskCreate((OS_TCB *)&AppTaskStartTCB,                     /* 任务控制块地址 */
                    (CPU_CHAR *)"App Task Start",                   /* 任务名 */
                    (OS_TASK_PTR)AppTaskStart,                      /* 启动任务函数地址 */
                    (void *)0,                                      /* 传递给任务的参数 */
                    (OS_PRIO)APP_CFG_TASK_START_PRIO,               /* 任务优先级 */
                    (CPU_STK *)&AppTaskStartStk[0],                 /* 堆栈基地址 */
                    (CPU_STK_SIZE)APP_CFG_TASK_START_STK_SIZE / 10, /* 堆栈监测区，这里表示后10%作为监测区 */
                    (CPU_STK_SIZE)APP_CFG_TASK_START_STK_SIZE,      /* 堆栈空间大小 */
                    (OS_MSG_QTY)0,                                  /* 本任务支持接受的最大消息数 */
                    (OS_TICK)0,                                     /* 设置时间片 */
                    (void *)0,                                      /* 堆栈空间大小 */
#if (DEBUG_TASK_RESOURCE == 1)
                    (OS_OPT)(OS_OPT_TASK_STK_CHK | OS_OPT_TASK_STK_CLR),
#else
                    (OS_OPT)(OS_OPT_TASK_STK_CLR),
#endif

                    /*  定义如下：
                     OS_OPT_TASK_STK_CHK      使能检测任务栈，统计任务栈已用的和未用的
                     OS_OPT_TASK_STK_CLR      在创建任务时，清零任务栈
                     OS_OPT_TASK_SAVE_FP      如果CPU有浮点寄存器，则在任务切换时保存浮点寄存器的内容
                    */
                    (OS_ERR *)&err);

    /* 启动多任务系统，控制权交给uC/OS-III */
    OSStart(&err);
    /* USER CODE END 2 */

    /* Infinite loop */
    /* USER CODE BEGIN WHILE */
    while(1)
    {
        /* USER CODE END WHILE */

        /* USER CODE BEGIN 3 */
    }
    /* USER CODE END 3 */
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
    RCC_PeriphCLKInitTypeDef PeriphClkInitStruct = {0};

    /** Configure the main internal regulator output voltage
     */
    __HAL_RCC_PWR_CLK_ENABLE();
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
    /** Initializes the RCC Oscillators according to the specified parameters
     * in the RCC_OscInitTypeDef structure.
     */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI | RCC_OSCILLATORTYPE_HSE;
    RCC_OscInitStruct.HSEState = RCC_HSE_ON;
    RCC_OscInitStruct.LSIState = RCC_LSI_ON;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLM = 25;
    RCC_OscInitStruct.PLL.PLLN = 336;
    RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
    RCC_OscInitStruct.PLL.PLLQ = 4;
    if(HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
    {
        Error_Handler();
    }
    /** Initializes the CPU, AHB and APB buses clocks
     */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

    if(HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
    {
        Error_Handler();
    }
    PeriphClkInitStruct.PeriphClockSelection = RCC_PERIPHCLK_RTC;
    PeriphClkInitStruct.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
    if(HAL_RCCEx_PeriphCLKConfig(&PeriphClkInitStruct) != HAL_OK)
    {
        Error_Handler();
    }
}

/**
 * @brief ADC1 Initialization Function
 * @param None
 * @retval None
 */
static void MX_ADC1_Init(void)
{

    /* USER CODE BEGIN ADC1_Init 0 */

    /* USER CODE END ADC1_Init 0 */

    //  ADC_ChannelConfTypeDef sConfig = {0};
    ADC_MultiModeTypeDef multimode = {0};
    ADC_InjectionConfTypeDef sConfigInjected = {0};

    /* USER CODE BEGIN ADC1_Init 1 */

    /* USER CODE END ADC1_Init 1 */
    /** Configure the global features of the ADC (Clock, Resolution, Data Alignment and number of conversion)
     */
    hadc1.Instance = ADC1;
    hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV8;
    hadc1.Init.Resolution = ADC_RESOLUTION_12B;
    hadc1.Init.ScanConvMode = ENABLE;
    hadc1.Init.ContinuousConvMode = ENABLE;
    hadc1.Init.DiscontinuousConvMode = DISABLE;
    hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
    hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc1.Init.NbrOfConversion = 1;
    hadc1.Init.DMAContinuousRequests = DISABLE;
    hadc1.Init.EOCSelection = ADC_EOC_SEQ_CONV;
    if(HAL_ADC_Init(&hadc1) != HAL_OK)
    {
        Error_Handler();
    }
    /** Configure the ADC multi-mode
     */
    multimode.Mode = ADC_MODE_INDEPENDENT;
    if(HAL_ADCEx_MultiModeConfigChannel(&hadc1, &multimode) != HAL_OK)
    {
        Error_Handler();
    }
    ////  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
    ////  */
    ////  sConfig.Channel = ADC_CHANNEL_15;
    ////  sConfig.Rank = 1;
    ////  sConfig.SamplingTime = ADC_SAMPLETIME_144CYCLES;
    ////  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    ////  {
    ////    Error_Handler();
    ////  }
    /** Configures for the selected ADC injected channel its corresponding rank in the sequencer and its sample time
     */
    sConfigInjected.InjectedChannel = ADC_CHANNEL_10; // 环境光线
    sConfigInjected.InjectedRank = 1;
    sConfigInjected.InjectedNbrOfConversion = 2;
    sConfigInjected.InjectedSamplingTime = ADC_SAMPLETIME_144CYCLES;
    sConfigInjected.ExternalTrigInjecConvEdge = ADC_EXTERNALTRIGINJECCONVEDGE_NONE;
    sConfigInjected.ExternalTrigInjecConv = ADC_INJECTED_SOFTWARE_START;
    sConfigInjected.AutoInjectedConv = ENABLE;
    sConfigInjected.InjectedDiscontinuousConvMode = DISABLE;
    sConfigInjected.InjectedOffset = 0;
    if(HAL_ADCEx_InjectedConfigChannel(&hadc1, &sConfigInjected) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN ADC1_Init 2 */
    /** Configures for the selected ADC injected channel its corresponding rank in the sequencer and its sample time
     */
    ////  sConfigInjected.InjectedChannel = ADC_CHANNEL_11; //MOTOR_T电机温度采样(MOTOR1)
    ////  sConfigInjected.InjectedRank = 2;
    ////  if (HAL_ADCEx_InjectedConfigChannel(&hadc1, &sConfigInjected) != HAL_OK)
    ////  {
    ////    Error_Handler();
    ////  }

    sConfigInjected.InjectedChannel = ADC_CHANNEL_15; // YLCGQ_REF 压力保护参考电压
    sConfigInjected.InjectedRank = 2;
    if(HAL_ADCEx_InjectedConfigChannel(&hadc1, &sConfigInjected) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE END ADC1_Init 2 */
}

/**
 * @brief ADC2 Initialization Function
 * @param None
 * @retval None
 */
// static void MX_ADC2_Init(void)
//{

////  /* USER CODE BEGIN ADC2_Init 0 */

////  /* USER CODE END ADC2_Init 0 */

////  ADC_ChannelConfTypeDef sConfig = {0};
////  ADC_InjectionConfTypeDef sConfigInjected = {0};

////  /* USER CODE BEGIN ADC2_Init 1 */

////  /* USER CODE END ADC2_Init 1 */
////  /** Configure the global features of the ADC (Clock, Resolution, Data Alignment and number of conversion)
////  */
////  hadc2.Instance = ADC2;
////  hadc2.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV6;
////  hadc2.Init.Resolution = ADC_RESOLUTION_12B;
////  hadc2.Init.ScanConvMode = ENABLE;
////  hadc2.Init.ContinuousConvMode = ENABLE;
////  hadc2.Init.DiscontinuousConvMode = DISABLE;
////  hadc2.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
////  hadc2.Init.ExternalTrigConv = ADC_SOFTWARE_START;
////  hadc2.Init.DataAlign = ADC_DATAALIGN_RIGHT;
////  hadc2.Init.NbrOfConversion = 1;
////  hadc2.Init.DMAContinuousRequests = DISABLE;
////  hadc2.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
////  if (HAL_ADC_Init(&hadc2) != HAL_OK)
////  {
////    Error_Handler();
////  }
////  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
////  */
////  sConfig.Channel = ADC_CHANNEL_11;
////  sConfig.Rank = 1;
////  sConfig.SamplingTime = ADC_SAMPLETIME_144CYCLES;
////  if (HAL_ADC_ConfigChannel(&hadc2, &sConfig) != HAL_OK)
////  {
////    Error_Handler();
////  }
////  /** Configures for the selected ADC injected channel its corresponding rank in the sequencer and its sample time
////  */
////  sConfigInjected.InjectedChannel = ADC_CHANNEL_6;
////  sConfigInjected.InjectedRank = 1;
////  sConfigInjected.InjectedNbrOfConversion = 3;
////  sConfigInjected.InjectedSamplingTime = ADC_SAMPLETIME_56CYCLES;
////  sConfigInjected.ExternalTrigInjecConvEdge = ADC_EXTERNALTRIGINJECCONVEDGE_NONE;
////  sConfigInjected.ExternalTrigInjecConv = ADC_INJECTED_SOFTWARE_START;
////  sConfigInjected.AutoInjectedConv = ENABLE;
////  sConfigInjected.InjectedDiscontinuousConvMode = DISABLE;
////  sConfigInjected.InjectedOffset = 0;
////  if (HAL_ADCEx_InjectedConfigChannel(&hadc2, &sConfigInjected) != HAL_OK)
////  {
////    Error_Handler();
////  }
////  /** Configures for the selected ADC injected channel its corresponding rank in the sequencer and its sample time
////  */
////  sConfigInjected.InjectedChannel = ADC_CHANNEL_13;
////  sConfigInjected.InjectedRank = 2;
////  if (HAL_ADCEx_InjectedConfigChannel(&hadc2, &sConfigInjected) != HAL_OK)
////  {
////    Error_Handler();
////  }
////  /** Configures for the selected ADC injected channel its corresponding rank in the sequencer and its sample time
////  */
////  sConfigInjected.InjectedChannel = ADC_CHANNEL_12;
////  sConfigInjected.InjectedRank = 3;
////  if (HAL_ADCEx_InjectedConfigChannel(&hadc2, &sConfigInjected) != HAL_OK)
////  {
////    Error_Handler();
////  }
////  /* USER CODE BEGIN ADC2_Init 2 */

////  /* USER CODE END ADC2_Init 2 */

////}

/**
 * @brief ADC3 Initialization Function
 * @param None
 * @retval None
 */
static void MX_ADC3_Init(void)
{

    /* USER CODE BEGIN ADC3_Init 0 */

    /* USER CODE END ADC3_Init 0 */

    ADC_ChannelConfTypeDef sConfig = {0};
    ADC_InjectionConfTypeDef sConfigInjected = {0};

    /* USER CODE BEGIN ADC3_Init 1 */

    /* USER CODE END ADC3_Init 1 */
    /** Configure the global features of the ADC (Clock, Resolution, Data Alignment and number of conversion)
     */
    hadc3.Instance = ADC3;
    hadc3.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV8;
    hadc3.Init.Resolution = ADC_RESOLUTION_12B;
    hadc3.Init.ScanConvMode = ENABLE;
    hadc3.Init.ContinuousConvMode = ENABLE;
    hadc3.Init.DiscontinuousConvMode = DISABLE;
    hadc3.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
    hadc3.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc3.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc3.Init.NbrOfConversion = 5;
    hadc3.Init.DMAContinuousRequests = ENABLE; // DISABLE;
    hadc3.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    if(HAL_ADC_Init(&hadc3) != HAL_OK)
    {
        Error_Handler();
    }
    /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
     */
    sConfig.Channel = ADC_CHANNEL_15; // ADC_24V电源适配器电压检测(低电压)
    sConfig.Rank = 1;
    sConfig.SamplingTime = ADC_SAMPLETIME_56CYCLES;
    if(HAL_ADC_ConfigChannel(&hadc3, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    sConfig.Channel = ADC_CHANNEL_9; // VADC 加热板温度
    sConfig.Rank = 2;
    if(HAL_ADC_ConfigChannel(&hadc3, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    //  sConfig.Channel = ADC_CHANNEL_14; //YLCGQ 压力传感器AD采样
    //  sConfig.Rank = 3;
    //  if (HAL_ADC_ConfigChannel(&hadc3, &sConfig) != HAL_OK)
    //  {
    //    Error_Handler();
    //  }

    sConfig.Channel = ADC_CHANNEL_4; // ADC_GL 管路温度采样
    sConfig.Rank = 3;
    if(HAL_ADC_ConfigChannel(&hadc3, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    //  sConfig.Channel = ADC_CHANNEL_5; //MOTOR3 电机电流采样
    //  sConfig.Rank = 5;
    //  if (HAL_ADC_ConfigChannel(&hadc3, &sConfig) != HAL_OK)
    //  {
    //    Error_Handler();
    //  }

    sConfig.Channel = ADC_CHANNEL_6; // 24V电源(Motor2)
    sConfig.Rank = 4;
    if(HAL_ADC_ConfigChannel(&hadc3, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    sConfig.Channel = ADC_CHANNEL_8; // 电机温度MotorT(Motor1)
    sConfig.Rank = 5;
    if(HAL_ADC_ConfigChannel(&hadc3, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /** Configures for the selected ADC injected channel its corresponding rank in the sequencer and its sample time
     */
    sConfigInjected.InjectedChannel = ADC_CHANNEL_5;
    sConfigInjected.InjectedRank = 1;
    sConfigInjected.InjectedNbrOfConversion = 2;
    sConfigInjected.InjectedSamplingTime = ADC_SAMPLETIME_56CYCLES;
    sConfigInjected.ExternalTrigInjecConvEdge = ADC_EXTERNALTRIGINJECCONVEDGE_NONE;
    sConfigInjected.ExternalTrigInjecConv = ADC_INJECTED_SOFTWARE_START;
    sConfigInjected.AutoInjectedConv = ENABLE;
    sConfigInjected.InjectedDiscontinuousConvMode = DISABLE;
    sConfigInjected.InjectedOffset = 0;
    if(HAL_ADCEx_InjectedConfigChannel(&hadc3, &sConfigInjected) != HAL_OK)
    {
        Error_Handler();
    }

    sConfigInjected.InjectedChannel = ADC_CHANNEL_14;
    sConfigInjected.InjectedRank = ADC_INJECTED_RANK_2;
    if(HAL_ADCEx_InjectedConfigChannel(&hadc3, &sConfigInjected) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN ADC3_Init 2 */

    /* USER CODE END ADC3_Init 2 */
}

/**
 * @brief CRC Initialization Function
 * @param None
 * @retval None
 */
static void MX_CRC_Init(void)
{

    /* USER CODE BEGIN CRC_Init 0 */

    /* USER CODE END CRC_Init 0 */

    /* USER CODE BEGIN CRC_Init 1 */

    /* USER CODE END CRC_Init 1 */
    hcrc.Instance = CRC;
    if(HAL_CRC_Init(&hcrc) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN CRC_Init 2 */

    /* USER CODE END CRC_Init 2 */
}

/**
 * @brief DAC Initialization Function
 * @param None
 * @retval None
 */
static void MX_DAC_Init(void)
{

    /* USER CODE BEGIN DAC_Init 0 */

    /* USER CODE END DAC_Init 0 */

    DAC_ChannelConfTypeDef sConfig = {0};

    /* USER CODE BEGIN DAC_Init 1 */

    /* USER CODE END DAC_Init 1 */
    /** DAC Initialization
     */
    hdac.Instance = DAC;
    if(HAL_DAC_Init(&hdac) != HAL_OK)
    {
        Error_Handler();
    }
    /** DAC channel OUT1 config
     */
    sConfig.DAC_Trigger = DAC_TRIGGER_NONE;
    sConfig.DAC_OutputBuffer = DAC_OUTPUTBUFFER_ENABLE;
    if(HAL_DAC_ConfigChannel(&hdac, &sConfig, DAC_CHANNEL_1) != HAL_OK)
    {
        Error_Handler();
    }
    /** DAC channel OUT2 config
     */
    if(HAL_DAC_ConfigChannel(&hdac, &sConfig, DAC_CHANNEL_2) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN DAC_Init 2 */

    /* USER CODE END DAC_Init 2 */
}

/**
 * @brief I2C2 Initialization Function
 * @param None
 * @retval None
 */
static void MX_I2C2_Init(void)
{

    /* USER CODE BEGIN I2C2_Init 0 */

    /* USER CODE END I2C2_Init 0 */

    /* USER CODE BEGIN I2C2_Init 1 */

    /* USER CODE END I2C2_Init 1 */
    hi2c2.Instance = I2C2;
    hi2c2.Init.ClockSpeed = 400000;
    hi2c2.Init.DutyCycle = I2C_DUTYCYCLE_2;
    hi2c2.Init.OwnAddress1 = 0;
    hi2c2.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
    hi2c2.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
    hi2c2.Init.OwnAddress2 = 0;
    hi2c2.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
    hi2c2.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
    if(HAL_I2C_Init(&hi2c2) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN I2C2_Init 2 */

    /* USER CODE END I2C2_Init 2 */
}

/**
 * @brief RTC Initialization Function
 * @param None
 * @retval None
 */
static void MX_RTC_Init(void)
{

    /* USER CODE BEGIN RTC_Init 0 */

    /* USER CODE END RTC_Init 0 */

    /* USER CODE BEGIN RTC_Init 1 */

    /* USER CODE END RTC_Init 1 */
    /** Initialize RTC Only
     */
    hrtc.Instance = RTC;
    hrtc.Init.HourFormat = RTC_HOURFORMAT_24;
    hrtc.Init.AsynchPrediv = 127;
    hrtc.Init.SynchPrediv = 255;
    hrtc.Init.OutPut = RTC_OUTPUT_DISABLE;
    hrtc.Init.OutPutPolarity = RTC_OUTPUT_POLARITY_HIGH;
    hrtc.Init.OutPutType = RTC_OUTPUT_TYPE_OPENDRAIN;
    if(HAL_RTC_Init(&hrtc) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN RTC_Init 2 */

    /* USER CODE END RTC_Init 2 */
}
#if (SPI_TYPE == 0)
/**
 * @brief SPI1 Initialization Function
 * @param None
 * @retval None
 */
static void MX_SPI1_Init(void)
{

    /* USER CODE BEGIN SPI1_Init 0 */

    /* USER CODE END SPI1_Init 0 */

    /* USER CODE BEGIN SPI1_Init 1 */

    /* USER CODE END SPI1_Init 1 */
    /* SPI1 parameter configuration*/
    hspi1.Instance = SPI1;
    hspi1.Init.Mode = SPI_MODE_MASTER;
    hspi1.Init.Direction = SPI_DIRECTION_2LINES;
    hspi1.Init.DataSize = SPI_DATASIZE_8BIT;
    hspi1.Init.CLKPolarity = SPI_POLARITY_LOW;
    hspi1.Init.CLKPhase = SPI_PHASE_1EDGE;
    hspi1.Init.NSS = SPI_NSS_SOFT;
    hspi1.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_2;
    hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;
    hspi1.Init.TIMode = SPI_TIMODE_DISABLE;
    hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
    hspi1.Init.CRCPolynomial = 10;
    if(HAL_SPI_Init(&hspi1) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN SPI1_Init 2 */

    /* USER CODE END SPI1_Init 2 */
}

/**
 * @brief SPI2 Initialization Function
 * @param None
 * @retval None
 */
static void MX_SPI2_Init(void)
{

    /* USER CODE BEGIN SPI2_Init 0 */

    /* USER CODE END SPI2_Init 0 */

    /* USER CODE BEGIN SPI2_Init 1 */

    /* USER CODE END SPI2_Init 1 */
    /* SPI2 parameter configuration*/
    hspi2.Instance = SPI2;
    hspi2.Init.Mode = SPI_MODE_MASTER;
    hspi2.Init.Direction = SPI_DIRECTION_2LINES;
    hspi2.Init.DataSize = SPI_DATASIZE_8BIT;
    hspi2.Init.CLKPolarity = SPI_POLARITY_LOW;
    hspi2.Init.CLKPhase = SPI_PHASE_1EDGE;
    hspi2.Init.NSS = SPI_NSS_SOFT;
    hspi2.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_2;
    hspi2.Init.FirstBit = SPI_FIRSTBIT_MSB;
    hspi2.Init.TIMode = SPI_TIMODE_DISABLE;
    hspi2.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
    hspi2.Init.CRCPolynomial = 10;
    if(HAL_SPI_Init(&hspi2) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN SPI2_Init 2 */
    __HAL_SPI_DISABLE(&hspi2); // 失能SPI外设
    SPI2->CR1 &= 0XFFC7;
    SPI2->CR1 |= SPI_BAUDRATEPRESCALER_2;
    __HAL_SPI_ENABLE(&hspi2);
    //    SPI_Flash_Enter4BMode();//进入4B地址模式
    /* USER CODE END SPI2_Init 2 */
}
#else
/**
 * @brief SPI1 Initialization Function
 * @param None
 * @retval None
 */
static void MX_SPI1_Init(void)
{

    /* USER CODE BEGIN SPI1_Init 0 */

    /* USER CODE END SPI1_Init 0 */

    /* USER CODE BEGIN SPI1_Init 1 */

    /* USER CODE END SPI1_Init 1 */
    /* SPI1 parameter configuration*/
    hspi1.Instance = SPI1;
    hspi1.Init.Mode = SPI_MODE_MASTER;
    hspi1.Init.Direction = SPI_DIRECTION_2LINES;
    hspi1.Init.DataSize = SPI_DATASIZE_8BIT;
    hspi1.Init.CLKPolarity = SPI_POLARITY_HIGH;
    hspi1.Init.CLKPhase = SPI_PHASE_2EDGE;
    hspi1.Init.NSS = SPI_NSS_SOFT;
    hspi1.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_128;
    hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;
    hspi1.Init.TIMode = SPI_TIMODE_DISABLE;
    hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
    hspi1.Init.CRCPolynomial = 10;
    if(HAL_SPI_Init(&hspi1) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN SPI2_Init 2 */
    __HAL_SPI_DISABLE(&hspi1); // 失能SPI外设
    SPI1->CR1 &= 0XFFC7;
    SPI1->CR1 |= SPI_BAUDRATEPRESCALER_2;
    __HAL_SPI_ENABLE(&hspi1);
    /* USER CODE BEGIN SPI1_Init 2 */

    /* USER CODE END SPI1_Init 2 */
}

/**
 * @brief SPI2 Initialization Function
 * @param None
 * @retval None
 */
static void MX_SPI2_Init(void)
{

    /* USER CODE BEGIN SPI2_Init 0 */

    /* USER CODE END SPI2_Init 0 */

    /* USER CODE BEGIN SPI2_Init 1 */

    /* USER CODE END SPI2_Init 1 */
    /* SPI2 parameter configuration*/
    hspi2.Instance = SPI2;
    hspi2.Init.Mode = SPI_MODE_MASTER;
    hspi2.Init.Direction = SPI_DIRECTION_2LINES;
    hspi2.Init.DataSize = SPI_DATASIZE_8BIT;
    hspi2.Init.CLKPolarity = SPI_POLARITY_LOW;
    hspi2.Init.CLKPhase = SPI_PHASE_1EDGE;
    hspi2.Init.NSS = SPI_NSS_SOFT;
    hspi2.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_2;
    hspi2.Init.FirstBit = SPI_FIRSTBIT_MSB;
    hspi2.Init.TIMode = SPI_TIMODE_DISABLE;
    hspi2.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
    hspi2.Init.CRCPolynomial = 10;
    if(HAL_SPI_Init(&hspi2) != HAL_OK)
    {
        Error_Handler();
    }
    //    SPI_Flash_Enter4BMode();//进入4B地址模式
    /* USER CODE END SPI2_Init 2 */
}
#endif

/**
 * @brief TIM1 Initialization Function
 * @param None
 * @retval None
 */
static void MX_TIM1_Init(void)
{

    /* USER CODE BEGIN TIM1_Init 0 */
    // 10KHz
    /* USER CODE END TIM1_Init 0 */

    TIM_ClockConfigTypeDef sClockSourceConfig = {0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    TIM_IC_InitTypeDef sConfigIC = {0};

    /* USER CODE BEGIN TIM1_Init 1 */

    /* USER CODE END TIM1_Init 1 */
    htim1.Instance = TIM1;
    htim1.Init.Prescaler = 1679;
    htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim1.Init.Period = 9999;
    htim1.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim1.Init.RepetitionCounter = 0;
    htim1.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if(HAL_TIM_Base_Init(&htim1) != HAL_OK)
    {
        Error_Handler();
    }
    sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
    if(HAL_TIM_ConfigClockSource(&htim1, &sClockSourceConfig) != HAL_OK)
    {
        Error_Handler();
    }
    if(HAL_TIM_IC_Init(&htim1) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if(HAL_TIMEx_MasterConfigSynchronization(&htim1, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    sConfigIC.ICPolarity = TIM_INPUTCHANNELPOLARITY_BOTHEDGE;
    sConfigIC.ICSelection = TIM_ICSELECTION_DIRECTTI;
    sConfigIC.ICPrescaler = TIM_ICPSC_DIV1;
    sConfigIC.ICFilter = 10;
    if(HAL_TIM_IC_ConfigChannel(&htim1, &sConfigIC, TIM_CHANNEL_2) != HAL_OK)
    {
        Error_Handler();
    }
    if(HAL_TIM_IC_ConfigChannel(&htim1, &sConfigIC, TIM_CHANNEL_3) != HAL_OK)
    {
        Error_Handler();
    }
    if(HAL_TIM_IC_ConfigChannel(&htim1, &sConfigIC, TIM_CHANNEL_4) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM1_Init 2 */
    HAL_TIM_Base_Stop_IT(&htim1);
    /* USER CODE END TIM1_Init 2 */
}

/**
 * @brief TIM3 Initialization Function
 * @param None
 * @retval None
 */
static void MX_TIM3_Init(void)
{

    /* USER CODE BEGIN TIM3_Init 0 */

    /* USER CODE END TIM3_Init 0 */

    TIM_MasterConfigTypeDef sMasterConfig = {0};

    /* USER CODE BEGIN TIM3_Init 1 */

    /* USER CODE END TIM3_Init 1 */
    htim3.Instance = TIM3;
    htim3.Init.Prescaler = 83; // 1M
    htim3.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim3.Init.Period = 0xffff;
    htim3.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if(HAL_TIM_Base_Init(&htim3) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if(HAL_TIMEx_MasterConfigSynchronization(&htim3, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM3_Init 2 */
    HAL_TIM_Base_Start(&htim3);
    /* USER CODE END TIM3_Init 2 */
}

/**
 * @brief TIM5 Initialization Function
 * @param None
 * @retval None
 */
/* TIM3 init function */
static void MX_TIM2_Init(void)
{

    /* USER CODE BEGIN TIM5_Init 0 */

    /* USER CODE END TIM5_Init 0 */

    TIM_MasterConfigTypeDef sMasterConfig = {0};

    /* USER CODE BEGIN TIM5_Init 1 */

    /* USER CODE END TIM5_Init 1 */
    htim2.Instance = TIM2;
    htim2.Init.Prescaler = 7;
    htim2.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim2.Init.Period = 1049;
    htim2.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if(HAL_TIM_Base_Init(&htim2) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if(HAL_TIMEx_MasterConfigSynchronization(&htim2, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM3_Init 2 */
    HAL_TIM_Base_Start_IT(&htim2);
    /* USER CODE END TIM3_Init 2 */
}
static void MX_TIM5_Init(void)
{

    /* USER CODE BEGIN TIM5_Init 0 */

    /* USER CODE END TIM5_Init 0 */

    TIM_MasterConfigTypeDef sMasterConfig = {0};
    TIM_OC_InitTypeDef sConfigOC = {0};

    /* USER CODE BEGIN TIM5_Init 1 */

    /* USER CODE END TIM5_Init 1 */
    htim5.Instance = TIM5;
    htim5.Init.Prescaler = 83; // TIM5在APB1 42MHz * 2 = 84MHz
    htim5.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim5.Init.Period = 999;
    htim5.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim5.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if(HAL_TIM_PWM_Init(&htim5) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if(HAL_TIMEx_MasterConfigSynchronization(&htim5, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 499;
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    if(HAL_TIM_PWM_ConfigChannel(&htim5, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
    {
        Error_Handler();
    }
    if(HAL_TIM_PWM_ConfigChannel(&htim5, &sConfigOC, TIM_CHANNEL_2) != HAL_OK)
    {
        Error_Handler();
    }
    if(HAL_TIM_PWM_ConfigChannel(&htim5, &sConfigOC, TIM_CHANNEL_3) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM5_Init 2 */

    /* USER CODE END TIM5_Init 2 */
    HAL_TIM_MspPostInit(&htim5);
}

/**
 * @brief TIM14 Initialization Function
 * @param None
 * @retval None
 */
// static void MX_TIM14_Init(void)
//{

//  /* USER CODE BEGIN TIM14_Init 0 */

//  /* USER CODE END TIM14_Init 0 */

//  TIM_MasterConfigTypeDef sMasterConfig = {0};
//  TIM_OC_InitTypeDef sConfigOC = {0};

//  /* USER CODE BEGIN TIM14_Init 1 */

//  /* USER CODE END TIM14_Init 1 */
//  htim14.Instance = TIM14;
//  htim14.Init.Prescaler = 839; //10Hz   //TIM14在APB1 42MHz * 2 = 84MHz
//  htim14.Init.CounterMode = TIM_COUNTERMODE_UP;
//  htim14.Init.Period = 9999;
//  htim14.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
//  htim14.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
//  if (HAL_TIM_PWM_Init(&htim14) != HAL_OK)
//  {
//    Error_Handler();
//  }
//  sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
//  sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
//  if (HAL_TIMEx_MasterConfigSynchronization(&htim14, &sMasterConfig) != HAL_OK)
//  {
//    Error_Handler();
//  }
//  sConfigOC.OCMode = TIM_OCMODE_PWM1;
//  sConfigOC.Pulse = 0;
//  sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
//  sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
//  if (HAL_TIM_PWM_ConfigChannel(&htim14, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
//  {
//    Error_Handler();
//  }
//  /* USER CODE BEGIN TIM14_Init 2 */

//  /* USER CODE END TIM14_Init 2 */
//  HAL_TIM_MspPostInit(&htim14);

//}

/**
 * @brief TIM6 Initialization Function
 * @param None
 * @retval None
 */
static void MX_TIM6_Init(void)
{

    /* USER CODE BEGIN TIM6_Init 0 */

    /* USER CODE END TIM6_Init 0 */

    TIM_MasterConfigTypeDef sMasterConfig = {0};

    /* USER CODE BEGIN TIM6_Init 1 */

    /* USER CODE END TIM6_Init 1 */
    htim6.Instance = TIM6;
    htim6.Init.Prescaler = 27;
    htim6.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim6.Init.Period = 2999;
    htim6.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if(HAL_TIM_Base_Init(&htim6) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if(HAL_TIMEx_MasterConfigSynchronization(&htim6, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM6_Init 2 */

    /* USER CODE END TIM6_Init 2 */
}

/**
 * @brief TIM7 Initialization Function
 * @param None
 * @retval None
 */
static void MX_TIM7_Init(void)
{

    /* USER CODE BEGIN TIM7_Init 0 */

    /* USER CODE END TIM7_Init 0 */

    TIM_MasterConfigTypeDef sMasterConfig = {0};

    /* USER CODE BEGIN TIM7_Init 1 */

    /* USER CODE END TIM7_Init 1 */
    htim7.Instance = TIM7;
    htim7.Init.Prescaler = 41;
    htim7.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim7.Init.Period = 99;
    htim7.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if(HAL_TIM_Base_Init(&htim7) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if(HAL_TIMEx_MasterConfigSynchronization(&htim7, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM7_Init 2 */

    /* USER CODE END TIM7_Init 2 */
}

/**
 * @brief TIM8 Initialization Function
 * @param None
 * @retval None
 */
static void MX_TIM8_Init(void)
{

    /* USER CODE BEGIN TIM8_Init 0 */

    /* USER CODE END TIM8_Init 0 */

    TIM_ClockConfigTypeDef sClockSourceConfig = {0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    TIM_OC_InitTypeDef sConfigOC = {0};
    TIM_BreakDeadTimeConfigTypeDef sBreakDeadTimeConfig = {0};

    /* USER CODE BEGIN TIM8_Init 1 */

    /* USER CODE END TIM8_Init 1 */
    htim8.Instance = TIM8;
    htim8.Init.Prescaler = 2;
    htim8.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim8.Init.Period = 2799;
    htim8.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim8.Init.RepetitionCounter = 0;
    htim8.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if(HAL_TIM_Base_Init(&htim8) != HAL_OK)
    {
        Error_Handler();
    }
    sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
    if(HAL_TIM_ConfigClockSource(&htim8, &sClockSourceConfig) != HAL_OK)
    {
        Error_Handler();
    }
    if(HAL_TIM_PWM_Init(&htim8) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if(HAL_TIMEx_MasterConfigSynchronization(&htim8, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0;
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCNPolarity = TIM_OCNPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    sConfigOC.OCIdleState = TIM_OCIDLESTATE_RESET;
    sConfigOC.OCNIdleState = TIM_OCNIDLESTATE_RESET;
    if(HAL_TIM_PWM_ConfigChannel(&htim8, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
    {
        Error_Handler();
    }
    if(HAL_TIM_PWM_ConfigChannel(&htim8, &sConfigOC, TIM_CHANNEL_2) != HAL_OK)
    {
        Error_Handler();
    }
    if(HAL_TIM_PWM_ConfigChannel(&htim8, &sConfigOC, TIM_CHANNEL_3) != HAL_OK)
    {
        Error_Handler();
    }
#if (BLDC_BRAKE_TYPE == 0)
    sBreakDeadTimeConfig.OffStateRunMode = TIM_OSSR_DISABLE;
    sBreakDeadTimeConfig.OffStateIDLEMode = TIM_OSSI_DISABLE;
    sBreakDeadTimeConfig.LockLevel = TIM_LOCKLEVEL_OFF;
    sBreakDeadTimeConfig.DeadTime = 0;
    sBreakDeadTimeConfig.BreakState = TIM_BREAK_DISABLE;
    sBreakDeadTimeConfig.BreakPolarity = TIM_BREAKPOLARITY_LOW;
    sBreakDeadTimeConfig.AutomaticOutput = TIM_AUTOMATICOUTPUT_DISABLE;
#else
    sBreakDeadTimeConfig.OffStateRunMode = TIM_OSSR_ENABLE;
    sBreakDeadTimeConfig.OffStateIDLEMode = TIM_OSSI_ENABLE;
    sBreakDeadTimeConfig.LockLevel = TIM_LOCKLEVEL_OFF;
    sBreakDeadTimeConfig.DeadTime = 50;
    sBreakDeadTimeConfig.BreakState = TIM_BREAK_DISABLE;
    sBreakDeadTimeConfig.BreakPolarity = TIM_BREAKPOLARITY_LOW;
    sBreakDeadTimeConfig.AutomaticOutput = TIM_AUTOMATICOUTPUT_DISABLE;
#endif
    if(HAL_TIMEx_ConfigBreakDeadTime(&htim8, &sBreakDeadTimeConfig) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM8_Init 2 */
    //    TIM8->BDTR |= TIM_BDTR_MOE;
    TIM8->CCER = 0x0000;
    __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_1, 0);
    __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_2, 0);
    __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_3, 0);
    TIM8->CR1 |= TIM_CR1_CEN;
    //  /* 开启定时器通道1输出PWM */
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_1);

    /* 开启定时器通道2输出PWM */
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_2);

    /* 开启定时器通道3输出PWM */
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_3);
    /* USER CODE END TIM8_Init 2 */
    HAL_TIM_MspPostInit(&htim8);
}

/**
 * @brief TIM9 Initialization Function
 * @param None
 * @retval None
 */
static void MX_TIM9_Init(void)
{

    /* USER CODE BEGIN TIM9_Init 0 */

    /* USER CODE END TIM9_Init 0 */

    TIM_OC_InitTypeDef sConfigOC = {0};

    /* USER CODE BEGIN TIM9_Init 1 */

    /* USER CODE END TIM9_Init 1 */
    htim9.Instance = TIM9;
    htim9.Init.Prescaler = 83; // 1KHz     //TIM9在APB2 84MHz
    htim9.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim9.Init.Period = 999;
    htim9.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim9.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if(HAL_TIM_PWM_Init(&htim9) != HAL_OK)
    {
        Error_Handler();
    }
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0;
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    if(HAL_TIM_PWM_ConfigChannel(&htim9, &sConfigOC, TIM_CHANNEL_2) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM9_Init 2 */

    /* USER CODE END TIM9_Init 2 */
    HAL_TIM_MspPostInit(&htim9);
}

/**
 * @brief TIM13 Initialization Function
 * @param None
 * @retval None
 */
static void MX_TIM13_Init(void)
{

    /* USER CODE BEGIN TIM13_Init 0 */

    /* USER CODE END TIM13_Init 0 */

    /* USER CODE BEGIN TIM13_Init 1 */

    /* USER CODE END TIM13_Init 1 */
    htim13.Instance = TIM13;
    htim13.Init.Prescaler = 167;
    htim13.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim13.Init.Period = 65535;
    htim13.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim13.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if(HAL_TIM_Base_Init(&htim13) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN TIM13_Init 2 */

    /* USER CODE END TIM13_Init 2 */
}

/**
 * @brief USART1 Initialization Function
 * @param None
 * @retval None
 */
static void MX_USART1_UART_Init(void)
{

    /* USER CODE BEGIN USART1_Init 0 */

    /* USER CODE END USART1_Init 0 */

    /* USER CODE BEGIN USART1_Init 1 */

    /* USER CODE END USART1_Init 1 */
    huart1.Instance = USART1;
    huart1.Init.BaudRate = 115200;
    huart1.Init.WordLength = UART_WORDLENGTH_8B;
    huart1.Init.StopBits = UART_STOPBITS_1;
    huart1.Init.Parity = UART_PARITY_NONE;
    huart1.Init.Mode = UART_MODE_TX_RX;
    huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart1.Init.OverSampling = UART_OVERSAMPLING_16;
    if(HAL_UART_Init(&huart1) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN USART1_Init 2 */

    /* USER CODE END USART1_Init 2 */
}

/**
 * @brief USART3 Initialization Function
 * @param None
 * @retval None
 */
static void MX_USART3_UART_Init(void)
{

    /* USER CODE BEGIN USART3_Init 0 */

    /* USER CODE END USART3_Init 0 */

    /* USER CODE BEGIN USART3_Init 1 */

    /* USER CODE END USART3_Init 1 */
    huart3.Instance = USART3;
    huart3.Init.BaudRate = 115200;
    huart3.Init.WordLength = UART_WORDLENGTH_8B;
    huart3.Init.StopBits = UART_STOPBITS_1;
    huart3.Init.Parity = UART_PARITY_NONE;
    huart3.Init.Mode = UART_MODE_TX_RX;
    huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart3.Init.OverSampling = UART_OVERSAMPLING_16;
    if(HAL_UART_Init(&huart3) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN USART3_Init 2 */
    SET_BIT(huart3.Instance->CR3, USART_CR3_DMAR);
    HAL_DMA_Start(&hdma_usart3_rx, (uint32_t)&huart3.Instance->DR, (uint32_t)g_mcuUartRxBuff, MCU_UART_RX_BUF_LEN);
    /* USER CODE END USART3_Init 2 */
}

/**
 * Enable DMA controller clock
 */
static void MX_DMA_Init(void)
{

    /* DMA controller clock enable */
    __HAL_RCC_DMA2_CLK_ENABLE();
    __HAL_RCC_DMA1_CLK_ENABLE();

    /* DMA interrupt init */
    /* DMA1_Stream1_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(DMA1_Stream1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(DMA1_Stream1_IRQn);
    /* DMA1_Stream3_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(DMA1_Stream3_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(DMA1_Stream3_IRQn);
    /* DMA1_Stream4_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(DMA1_Stream4_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(DMA1_Stream4_IRQn);
    /* DMA2_Stream2_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(DMA2_Stream2_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(DMA2_Stream2_IRQn);
    /* DMA2_Stream1_IRQn interrupt configuration */
    //  HAL_NVIC_SetPriority(DMA2_Stream1_IRQn, 0, 0);
    //  HAL_NVIC_EnableIRQ(DMA2_Stream1_IRQn);
    /* DMA2_Stream5_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(DMA2_Stream5_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(DMA2_Stream5_IRQn);
}

/**
 * @brief USART6 Initialization Function
 * @param None
 * @retval None
 */
static void MX_USART6_UART_Init(void)
{

    /* USER CODE BEGIN USART6_Init 0 */

    /* USER CODE END USART6_Init 0 */

    /* USER CODE BEGIN USART6_Init 1 */

    /* USER CODE END USART6_Init 1 */
    huart6.Instance = USART6;
    huart6.Init.BaudRate = 115200;
    huart6.Init.WordLength = UART_WORDLENGTH_8B;
    huart6.Init.StopBits = UART_STOPBITS_1;
    huart6.Init.Parity = UART_PARITY_NONE;
    huart6.Init.Mode = UART_MODE_TX_RX;
    huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart6.Init.OverSampling = UART_OVERSAMPLING_16;
    if(HAL_UART_Init(&huart6) != HAL_OK)
    {
        Error_Handler();
    }
    /* USER CODE BEGIN USART6_Init 2 */

    /* USER CODE END USART6_Init 2 */
}

/**
 * @brief GPIO Initialization Function
 * @param None
 * @retval None
 */
static void MX_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* GPIO Ports Clock Enable */
    __HAL_RCC_GPIOE_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOF_CLK_ENABLE();
    __HAL_RCC_GPIOH_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();
    __HAL_RCC_GPIOG_CLK_ENABLE();

    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(GPIOE, YLCGQ_CLE_Pin | BJ_LED1_Pin | YY_DATA_Pin, GPIO_PIN_RESET);

    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(GPIOC, SC_Ctr_Pin | I2C_POWER_Pin | POWER_24_Pin, GPIO_PIN_RESET);

    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(GPIOF, SPK_D_Pin | SPK_CLK_Pin | RTC_SDA_Pin | RTC_CLK_Pin | RTC_RST_Pin, GPIO_PIN_RESET);

#if (BLDC_BRAKE_TYPE == 0)
    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(PWM1L_GPIO_Port, PWM1L_Pin, GPIO_PIN_RESET);

    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(GPIOB, PWM2L_Pin | PWM3L_Pin, GPIO_PIN_RESET);
#endif

    HAL_GPIO_WritePin(Flash_CS_GPIO_Port, Flash_CS_Pin, GPIO_PIN_SET);

    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(GPIOD, Flash_RST_Pin | FSMC_TE_Pin | FSMC_RST_Pin, GPIO_PIN_RESET);

    /*Configure GPIO pin Output Level */
#if (SPI_TYPE == 0)
    HAL_GPIO_WritePin(GPIOG, Break_Pin | LCD_ON_OFF_Pin | SD_CS_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOG, WIFI_CONTROL_Pin, GPIO_PIN_SET);
#else
    HAL_GPIO_WritePin(GPIOG, Break_Pin | LCD_ON_OFF_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOG, WIFI_CONTROL_Pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(SD_CS_GPIO_Port, SD_CS_Pin, GPIO_PIN_RESET);
#endif

    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(FLOW_SDA_GPIO_Port, FLOW_SDA_Pin, GPIO_PIN_SET);

    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(FLOW_CLK_GPIO_Port, FLOW_CLK_Pin, GPIO_PIN_SET);

    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(SPK_1_GPIO_Port, SPK_1_Pin, GPIO_PIN_SET);

    /*Configure GPIO pins : YLCGQ_CLE_Pin BJ_LED1_Pin YY_DATA_Pin */
    GPIO_InitStruct.Pin = YLCGQ_CLE_Pin | BJ_LED1_Pin | YY_DATA_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

    /*Configure GPIO pins : LEFT_KEY_Pin RIGHT_KEY_Pin */
    GPIO_InitStruct.Pin = LEFT_KEY_Pin | RIGHT_KEY_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

    /*Configure GPIO pin : ENTER_KEY_Pin */
    GPIO_InitStruct.Pin = ENTER_KEY_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(ENTER_KEY_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pins : LED_ALARM_Pin SC_Ctr_Pin I2C_POWER_Pin */
    GPIO_InitStruct.Pin = LED_ALARM_Pin | SC_Ctr_Pin | I2C_POWER_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    /*Configure GPIO pins : SPK_D_Pin SPK_CLK_Pin */
    GPIO_InitStruct.Pin = SPK_D_Pin | SPK_CLK_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOF, &GPIO_InitStruct);

#if (BLDC_BRAKE_TYPE == 0)
    /*Configure GPIO pin : PWM1L_Pin */
    GPIO_InitStruct.Pin = PWM1L_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(PWM1L_GPIO_Port, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = PWM2L_Pin | PWM3L_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
#endif

    /*Configure GPIO pin : POWER_24_Pin */
    GPIO_InitStruct.Pin = POWER_24_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    HAL_GPIO_Init(POWER_24_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pins : PWM2L_Pin PWM3L_Pin Flash_CS_Pin */
    GPIO_InitStruct.Pin = Flash_CS_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(Flash_CS_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pins : RTC_SDA_Pin RTC_CLK_Pin RTC_RST_Pin */
    GPIO_InitStruct.Pin = RTC_SDA_Pin | RTC_CLK_Pin | RTC_RST_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOF, &GPIO_InitStruct);

    /*Configure GPIO pins : Flash_RST_Pin FSMC_RST_Pin */
    GPIO_InitStruct.Pin = Flash_RST_Pin | FSMC_RST_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

    /*Configure GPIO pin : FSMC_TE_Pin */
    GPIO_InitStruct.Pin = FSMC_TE_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    HAL_GPIO_Init(FSMC_TE_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pins : Break_Pin WIFI_CONTROL_Pin SD_CS_Pin */
#if (SPI_TYPE == 0)
    GPIO_InitStruct.Pin = Break_Pin | WIFI_CONTROL_Pin | SD_CS_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);
#else
    GPIO_InitStruct.Pin = Break_Pin | WIFI_CONTROL_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = SD_CS_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(SD_CS_GPIO_Port, &GPIO_InitStruct);
#endif

    /*Configure GPIO pins : ALARM_OFF_Pin YY_Busy_Pin SD_CD_Pin */
    GPIO_InitStruct.Pin = ALARM_OFF_Pin | YY_Busy_Pin | SD_CD_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);

    /*Configure GPIO pin : LCD_ON_OFF_Pin */
    GPIO_InitStruct.Pin = LCD_ON_OFF_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    HAL_GPIO_Init(LCD_ON_OFF_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pin : FLOW_SDA_Pin */
    GPIO_InitStruct.Pin = FLOW_SDA_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(FLOW_SDA_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pin : FLOW_CLK_Pin */
    GPIO_InitStruct.Pin = FLOW_CLK_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(FLOW_CLK_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pin : POW_ON_OFF_Pin */
    GPIO_InitStruct.Pin = POW_ON_OFF_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(POW_ON_OFF_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pin : FSMC_RST_Pin */
    GPIO_InitStruct.Pin = FSMC_RST_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(FSMC_RST_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pins : PG9 PG14 */
    GPIO_InitStruct.Pin = GPIO_PIN_9 | GPIO_PIN_14;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_USART6;
    HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);

    /*Configure GPIO pin : SPK_1_Pin */
    GPIO_InitStruct.Pin = SPK_1_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    HAL_GPIO_Init(SPK_1_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pins : PA3 */ // 加热板
    GPIO_InitStruct.Pin = BOARD_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(BOARD_GPIO_Port, &GPIO_InitStruct);

    /*Configure GPIO pins : PA2 */ // 管路
    GPIO_InitStruct.Pin = PIPE_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(PIPE_GPIO_Port, &GPIO_InitStruct);
}

void MX_FSMC_ReInit(void)
{

    /* USER CODE BEGIN FSMC_Init 0 */

    /* USER CODE END FSMC_Init 0 */

    FSMC_NORSRAM_TimingTypeDef Timing = {0};
    FSMC_NORSRAM_TimingTypeDef ExtTiming = {0};

    /* USER CODE BEGIN FSMC_Init 1 */

    /* USER CODE END FSMC_Init 1 */

    /** Perform the SRAM1 memory initialization sequence
     */
    hsram1.Instance = FSMC_NORSRAM_DEVICE;
    hsram1.Extended = FSMC_NORSRAM_EXTENDED_DEVICE;
    /* hsram1.Init */
    hsram1.Init.NSBank = FSMC_NORSRAM_BANK1;
    hsram1.Init.DataAddressMux = FSMC_DATA_ADDRESS_MUX_DISABLE;
    hsram1.Init.MemoryType = FSMC_MEMORY_TYPE_SRAM;
    hsram1.Init.MemoryDataWidth = FSMC_NORSRAM_MEM_BUS_WIDTH_16;
    hsram1.Init.BurstAccessMode = FSMC_BURST_ACCESS_MODE_DISABLE;
    hsram1.Init.WaitSignalPolarity = FSMC_WAIT_SIGNAL_POLARITY_LOW;
    hsram1.Init.WrapMode = FSMC_WRAP_MODE_DISABLE;
    hsram1.Init.WaitSignalActive = FSMC_WAIT_TIMING_BEFORE_WS;
    hsram1.Init.WriteOperation = FSMC_WRITE_OPERATION_ENABLE;
    hsram1.Init.WaitSignal = FSMC_WAIT_SIGNAL_DISABLE;
    hsram1.Init.ExtendedMode = FSMC_EXTENDED_MODE_ENABLE;
    hsram1.Init.AsynchronousWait = FSMC_ASYNCHRONOUS_WAIT_DISABLE;
    hsram1.Init.WriteBurst = FSMC_WRITE_BURST_DISABLE;
    hsram1.Init.PageSize = FSMC_PAGE_SIZE_NONE;
    /* Timing */
    Timing.AddressSetupTime = 0;
    Timing.AddressHoldTime = 0;
    Timing.DataSetupTime = 0;
    Timing.BusTurnAroundDuration = 0;

    Timing.CLKDivision = 1;
    Timing.DataLatency = 1;
    Timing.AccessMode = FSMC_ACCESS_MODE_A;
    /* ExtTiming */
    ExtTiming.AddressSetupTime = 0;
    ExtTiming.AddressHoldTime = 0;
    ExtTiming.DataSetupTime = 0;
    ExtTiming.BusTurnAroundDuration = 0;
    ExtTiming.CLKDivision = 1;
    ExtTiming.DataLatency = 1;
    ExtTiming.AccessMode = FSMC_ACCESS_MODE_A;

    if(HAL_SRAM_Init(&hsram1, &Timing, &ExtTiming) != HAL_OK)
    {
        Error_Handler();
    }

    /* USER CODE BEGIN FSMC_Init 2 */

    /* USER CODE END FSMC_Init 2 */
}

/* FSMC initialization function */
static void MX_FSMC_Init(void)
{

    /* USER CODE BEGIN FSMC_Init 0 */

    /* USER CODE END FSMC_Init 0 */

    FSMC_NORSRAM_TimingTypeDef Timing = {0};
    FSMC_NORSRAM_TimingTypeDef ExtTiming = {0};

    /* USER CODE BEGIN FSMC_Init 1 */

    /* USER CODE END FSMC_Init 1 */

    /** Perform the SRAM1 memory initialization sequence
     */
    hsram1.Instance = FSMC_NORSRAM_DEVICE;
    hsram1.Extended = FSMC_NORSRAM_EXTENDED_DEVICE;
    /* hsram1.Init */
    hsram1.Init.NSBank = FSMC_NORSRAM_BANK1;
    hsram1.Init.DataAddressMux = FSMC_DATA_ADDRESS_MUX_DISABLE;
    hsram1.Init.MemoryType = FSMC_MEMORY_TYPE_SRAM;
    hsram1.Init.MemoryDataWidth = FSMC_NORSRAM_MEM_BUS_WIDTH_16;
    hsram1.Init.BurstAccessMode = FSMC_BURST_ACCESS_MODE_DISABLE;
    hsram1.Init.WaitSignalPolarity = FSMC_WAIT_SIGNAL_POLARITY_LOW;
    hsram1.Init.WrapMode = FSMC_WRAP_MODE_DISABLE;
    hsram1.Init.WaitSignalActive = FSMC_WAIT_TIMING_BEFORE_WS;
    hsram1.Init.WriteOperation = FSMC_WRITE_OPERATION_ENABLE;
    hsram1.Init.WaitSignal = FSMC_WAIT_SIGNAL_DISABLE;
    hsram1.Init.ExtendedMode = FSMC_EXTENDED_MODE_ENABLE;
    hsram1.Init.AsynchronousWait = FSMC_ASYNCHRONOUS_WAIT_DISABLE;
    hsram1.Init.WriteBurst = FSMC_WRITE_BURST_DISABLE;
    hsram1.Init.PageSize = FSMC_PAGE_SIZE_NONE;
#if (LCD_TYPE == LCD_5_TFT)
    /* Timing */
    Timing.AddressSetupTime = 15;
    Timing.AddressHoldTime = 15;
    Timing.DataSetupTime = 255;
    Timing.BusTurnAroundDuration = 15;
    Timing.CLKDivision = 16;
    Timing.DataLatency = 17;
    Timing.AccessMode = FSMC_ACCESS_MODE_A;
    /* ExtTiming */
    ExtTiming.AddressSetupTime = 15;
    ExtTiming.AddressHoldTime = 15;
    ExtTiming.DataSetupTime = 255;
    ExtTiming.BusTurnAroundDuration = 15;
    ExtTiming.CLKDivision = 16;
    ExtTiming.DataLatency = 17;
    ExtTiming.AccessMode = FSMC_ACCESS_MODE_A;
#endif
#if (LCD_TYPE == LCD_35_TFT)
    Timing.AddressSetupTime =
                    15; // AddressSetup+AddressHold+DataSetupTime>=450ns,相当于ILI9488的RD高电平持续时间，为90ns
    Timing.AddressHoldTime = 1;
    Timing.DataSetupTime =
                    60; // 数据保持时间，等于: DATAST(+1)个HCLK时钟周期，DATAST最大为255。相当于ILI9488的RD低电平持续时间,最小355ns，1/168M=5.952ns
    Timing.BusTurnAroundDuration = 0;
    Timing.CLKDivision = 16;
    Timing.DataLatency = 17;
    Timing.AccessMode = FSMC_ACCESS_MODE_A;
    /* ExtTiming */
    ExtTiming.AddressSetupTime =
                    2; // AddressSetup+AddressHold+DataSetupTime>=30ns 相当于ILI9488的WR高电平持续时间，为15ns
    ExtTiming.AddressHoldTime = 1;
    ExtTiming.DataSetupTime =
                    2; // 数据保持时间，等于: DATAST(+1)个HCLK时钟周期，DATAST最大为255。相当于ILI9488的WR低电平持续时间,最小15ns，1/168M=5.952ns
    ExtTiming.BusTurnAroundDuration = 0;
    ExtTiming.CLKDivision = 16;
    ExtTiming.DataLatency = 17;
    ExtTiming.AccessMode = FSMC_ACCESS_MODE_A;

#endif

#if (LCD_TYPE == LCD_28_TFT)
    //    Timing.AddressSetupTime = 3;          //AddressSetup+AddressHold+DataSetupTime>=450ns,相当于ILI9488的RD高电平持续时间，为90ns
    //  Timing.AddressHoldTime = 30;
    //  Timing.DataSetupTime = 10;            //数据保持时间，等于: DATAST(+1)个HCLK时钟周期，DATAST最大为255。相当于ILI9488的RD低电平持续时间,最小355ns，1/168M=5.952ns
    //  Timing.BusTurnAroundDuration = 0;
    //  Timing.CLKDivision = 16;
    //  Timing.DataLatency = 17;
    //  Timing.AccessMode = FSMC_ACCESS_MODE_A;
    //  /* ExtTiming */
    //  ExtTiming.AddressSetupTime = 3;       //AddressSetup+AddressHold+DataSetupTime>=30ns 相当于ILI9488的WR高电平持续时间，为15ns
    //  ExtTiming.AddressHoldTime = 30;
    //  ExtTiming.DataSetupTime = 10;          //数据保持时间，等于: DATAST(+1)个HCLK时钟周期，DATAST最大为255。相当于ILI9488的WR低电平持续时间,最小15ns，1/168M=5.952ns
    //  ExtTiming.BusTurnAroundDuration = 0;
    //  ExtTiming.CLKDivision = 16;
    //  ExtTiming.DataLatency = 17;
    //  ExtTiming.AccessMode = FSMC_ACCESS_MODE_A;

    Timing.AddressSetupTime =
                    18; // AddressSetup+AddressHold+DataSetupTime>=450ns,相当于ILI9488的RD高电平持续时间，为90ns
    Timing.AddressHoldTime = 3;
    Timing.DataSetupTime =
                    62; // 数据保持时间，等于: DATAST(+1)个HCLK时钟周期，DATAST最大为255。相当于ILI9488的RD低电平持续时间,最小355ns，1/168M=5.952ns
    Timing.BusTurnAroundDuration = 0;
    Timing.CLKDivision = 16;
    Timing.DataLatency = 17;
    Timing.AccessMode = FSMC_ACCESS_MODE_A;
    // ExtTiming
    ExtTiming.AddressSetupTime =
                    7; // AddressSetup+AddressHold+DataSetupTime>=30ns 相当于ILI9488的WR高电平持续时间，为15ns
    ExtTiming.AddressHoldTime = 3;
    ExtTiming.DataSetupTime =
                    7; // 数据保持时间，等于: DATAST(+1)个HCLK时钟周期，DATAST最大为255。相当于ILI9488的WR低电平持续时间,最小15ns，1/168M=5.952ns
    ExtTiming.BusTurnAroundDuration = 0;
    ExtTiming.CLKDivision = 16;
    ExtTiming.DataLatency = 17;
    ExtTiming.AccessMode = FSMC_ACCESS_MODE_A;
#endif
    if(HAL_SRAM_Init(&hsram1, &Timing, &ExtTiming) != HAL_OK)
    {
        Error_Handler();
    }

    /* USER CODE BEGIN FSMC_Init 2 */

    /* USER CODE END FSMC_Init 2 */
}

/* USER CODE BEGIN 4 */
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
    __HAL_UART_CLEAR_OREFLAG(huart);

  //    if(huart->Lock == HAL_LOCKED)
   // {
    //   __HAL_UNLOCK(huart);
    // }

    if(huart->Instance == USART1)
    {
        HAL_UART_Receive_IT_Nolock(&huart1, g_U8UartWifiRcvBuf, WIFI_ISR_RCV_LENGTH);
        //__HAL_UART_ENABLE_IT(&huart1, UART_IT_RXNE);
    }

}
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if(huart->Instance == USART1)
    {
        U8 i;
        for(i = 0; i < WIFI_ISR_RCV_LENGTH; i++)
        {
#if (NET_COMMUNICATION_TYPE == 0)
            g_WifiUartModule.AddRcvData(g_U8UartWifiRcvBuf[i]);
#else
            g_Sim7600UartModule.AddRcvData(g_U8UartWifiRcvBuf[i]);
#endif
        }
        // g_WifiUartModule.AddRcvData(g_U8Uart7RcvBuf[0]);
        HAL_UART_Receive_IT_Nolock(&huart1, g_U8UartWifiRcvBuf, WIFI_ISR_RCV_LENGTH);
    }
}

#if (DEBUG_TASK_RESOURCE == 1) //1:调试各任务CPU占用率与栈使用情况 0:不调试
#define MONITOR_TASK_PRIO  10
#define MONITOR_TASK_STK_SIZE  256

static OS_TCB MonitorTaskTCB;
static CPU_STK MonitorTaskStk[MONITOR_TASK_STK_SIZE];
void PrintSystemInfo(void) {
#if OS_CFG_DBG_EN > 0
    CPU_STK_SIZE free;
    CPU_STK_SIZE used;
    OS_ERR err;

    CPU_SR cpu_sr = 0u;
    OS_TCB *p_tcb;
    CPU_CRITICAL_ENTER();
    p_tcb = OSTaskDbgListPtr;
    CPU_CRITICAL_EXIT();
    printf("\r\nUsed Stack(B)   Free Stack(B)   Stack Usage(%%)  Priority  CPU Usage(%%)   Max CPU Usage(%%)   Context Switches   Task State        Task Name\r\n");
    for (;;) {
        if (p_tcb != (OS_TCB *)0) {
            OSTaskStkChk(p_tcb, &free, &used, &err);
            if (err == OS_ERR_NONE) {
                printf("%4d        %4d         %3d%%        %3d  %10.2f%%      %10.2f%%       %8d       %-15s %s   \r\n",
                       used, free, (used * 100) / (used + free), p_tcb->Prio,
                       p_tcb->CPUUsage / 100.0, p_tcb->CPUUsageMax / 100.0, p_tcb->CtxSwCtr,
                       (p_tcb->TaskState == OS_TASK_STATE_RDY) ? "Ready" :
                       (p_tcb->TaskState == OS_TASK_STATE_DLY) ? "Delayed" :
                       (p_tcb->TaskState == OS_TASK_STATE_SUSPENDED) ? "Suspended" :
                       (p_tcb->TaskState == OS_TASK_STATE_PEND) ? "Pending" :
                       (p_tcb->TaskState == OS_TASK_STATE_PEND_TIMEOUT) ? "Pend Timeout" :
                       (p_tcb->TaskState == OS_TASK_STATE_DLY_SUSPENDED) ? "Delayed & Suspended" :
                       (p_tcb->TaskState == OS_TASK_STATE_PEND_SUSPENDED) ? "Pending & Suspended" :
                       (p_tcb->TaskState == OS_TASK_STATE_PEND_TIMEOUT_SUSPENDED) ? "Pend Timeout & Suspended" : "Unknown",
                       p_tcb->NamePtr);
            } else {
                printf("Error checking stack for task %s: %d\n", p_tcb->NamePtr, err);
            }
            CPU_CRITICAL_ENTER();
            p_tcb = p_tcb->DbgNextPtr; // 指向下一个任务的TCB
            CPU_CRITICAL_EXIT();
        } else {
            break;
        }
    }
#endif
}
// 在适当的地方调用这个函数，例如在一个监控任务中周期性地调用它
void MonitorTask(void *p_arg) {
    OS_ERR err;
    (void)p_arg;

    while (DEF_TRUE) {
        PrintSystemInfo();
        OSTimeDlyHMSM(0, 0, 10, 0, OS_OPT_TIME_HMSM_STRICT, &err);  // 每10秒打印一次
    }
}

void CreateMonitorTask(void) {
    OS_ERR err;

    OSTaskCreate((OS_TCB     *)&MonitorTaskTCB,
                 (CPU_CHAR   *)"Monitor Task",
                 (OS_TASK_PTR ) MonitorTask,
                 (void       *) 0,
                 (OS_PRIO     ) MONITOR_TASK_PRIO,
                 (CPU_STK    *)&MonitorTaskStk[0],
                 (CPU_STK_SIZE)(MONITOR_TASK_STK_SIZE / 10u),
                 (CPU_STK_SIZE) MONITOR_TASK_STK_SIZE,
                 (OS_MSG_QTY  ) 0u,
                 (OS_TICK     ) 0u,
                 (void       *) 0,
                 (OS_OPT      )(OS_OPT_TASK_STK_CHK | OS_OPT_TASK_STK_CLR),
                 (OS_ERR     *)&err);

    if (err != OS_ERR_NONE) {
        printf("Error creating monitor task: %d\n", err);
    }
}
#endif
static void AppTaskStart(void *p_arg)
{
    OS_ERR err;

    (void)p_arg;

    g_StartUpStatus = 10;
    // printf("g_StartUpStatus=%d\r\n", g_StartUpStatus);
    HAL_ResumeTick();

    CPU_Init(); /* 此函数要优先调用，因为外设驱动中使用的us和ms延迟是基于此函数的 */
    BSP_OS_TickEnable();

#if OS_CFG_STAT_TASK_EN > 0u
    OSStatTaskCPUUsageInit(&err);
#endif

#ifdef CPU_CFG_INT_DIS_MEAS_EN
    CPU_IntDisMeasMaxCurReset();
#endif
    // 创建标志
    FlagInit();

    // 创建互斥量
    g_StartUpStatus = 11;
    // printf("g_StartUpStatus=%d\r\n", g_StartUpStatus);
    g_ConfigSave.CreateLock();
    g_TaskManager.CreateLock();
    g_OneSecondTaskManager.CreateLock();
    g_UpdateTaskManager.CreateLock();
    g_FastBlinkTaskManager.CreateLock();
    g_SdCardDataSave.CreateLock();
    g_AlarmManager.CreateLock();
    g_KeyQueueBuf.CreateLock();

    MutexInit();

    // 创建消息队列
    MessageQueueInit();

    // 创建信号量
    SemaphoreInit();

    //
    HAL_GPIO_WritePin(Flash_RST_GPIO_Port, Flash_RST_Pin, GPIO_PIN_SET);
    HAL_Delay(2);
    HAL_GPIO_WritePin(GPIOG, WIFI_CONTROL_Pin, GPIO_PIN_RESET);

    g_StartUpStatus = 12;
    // printf("g_StartUpStatus=%d\r\n", g_StartUpStatus);
    SetFLASHCheckFlag(!SPI_Flash_ReadID());
    SPI_Flash_Enter4BMode();
    InitSystem();
    RTC_Init();
    g_EdfDataSave.Init();
    g_StartUpStatus = 13;
	g_ConfigSave.NoMaskLeakParameterCheck();
    // printf("g_StartUpStatus=%d\r\n", g_StartUpStatus);

    //    g_SlowBlinkTaskManager.CreateLock();

    // 创建任务
    TaskInit();

    /* 创建任务间通信机制 */
    // AppObjCreate();
#if (DEBUG_TASK_RESOURCE == 1)
    CreateMonitorTask();
#endif
    OSTaskDel(0, &err); // 删除自身

    //    while (1)
    //    {
    //        OSTimeDlyHMSM(0, 0, 0, 10, OS_OPT_TIME_PERIODIC, &err);
    //    }
}

#if (DEBUG_TASK_RESOURCE == 1)
static void App_Printf(CPU_CHAR *format, ...)
{
    CPU_CHAR buf_str[128 + 1];
    va_list v_args;
    OS_ERR os_err;

    va_start(v_args, format);
    (void)vsnprintf((char *)&buf_str[0],
                    (size_t)sizeof(buf_str),
                    (char const *)format,
                    v_args);
    va_end(v_args);

    /* 互斥操作 */
    OSSemPend((OS_SEM *)&AppPrintfSemp,
                    (OS_TICK)0u,
                    (OS_OPT)OS_OPT_PEND_BLOCKING,
                    (CPU_TS *)0,
                    (OS_ERR *)&os_err);

    printf("%s", buf_str);

    (void)OSSemPost((OS_SEM *)&AppPrintfSemp,
                    (OS_OPT)OS_OPT_POST_1,
                    (OS_ERR *)&os_err);
}

static void DispTaskInfo(void)
{
    OS_TCB *p_tcb; /* 定义一个任务控制块指针, TCB = TASK CONTROL BLOCK */
    float CPU = 0.0f;
    CPU_SR_ALLOC();

    CPU_CRITICAL_ENTER();
    p_tcb = OSTaskDbgListPtr;
    CPU_CRITICAL_EXIT();

    /* 打印标题 */
    App_Printf("===============================================================\r\n");
    App_Printf(" 优先级 使用栈 剩余栈 百分比 利用率   任务名\r\n");
    App_Printf("  Prio   Used  Free   Per    CPU     Taskname\r\n");

    /* 遍历任务控制块列表(TCB list)，打印所有的任务的优先级和名称 */
    while(p_tcb != (OS_TCB *)0)
    {
        CPU = (float)p_tcb->CPUUsage / 100;
        App_Printf("   %2d  %5d  %5d   %02d%%   %5.2f%%   %s\r\n",
                        p_tcb->Prio,
                        p_tcb->StkUsed,
                        p_tcb->StkFree,
                        (p_tcb->StkUsed * 100) / (p_tcb->StkUsed + p_tcb->StkFree),
                        CPU,
                        p_tcb->NamePtr);

        CPU_CRITICAL_ENTER();
        p_tcb = p_tcb->DbgNextPtr;
        CPU_CRITICAL_EXIT();
    }
}

/*
*********************************************************************************************************
*   函 数 名: AppTaskUserIF
*   功能说明: 按键消息处理
*   形    参: p_arg 是在创建该任务时传递的形参
*   返 回 值: 无
  优 先 级: 4
*********************************************************************************************************
*/
static void AppTaskUserIF(void *p_arg)
{
    U8 flag = 0;
    OS_ERR err;

    (void)p_arg;

    while(1)
    {
        DispTaskInfo();

        if(flag == 0)
        {
            HAL_GPIO_WritePin(BJ_LED1_GPIO_Port, BJ_LED1_Pin, GPIO_PIN_SET);
        }
        else
        {
            HAL_GPIO_WritePin(BJ_LED1_GPIO_Port, BJ_LED1_Pin, GPIO_PIN_RESET);
        }
        flag = 1 - flag;
        OSTimeDlyHMSM(0, 0, 1, 0, OS_OPT_TIME_PERIODIC, &err);
    }
}

void CreateUserIFTask(void)
{
    OS_ERR err;
    OSTaskCreate((OS_TCB *)&AppTaskUserIFTCB,
                    (CPU_CHAR *)"App Task UserIF",
                    (OS_TASK_PTR)AppTaskUserIF,
                    (void *)0,
                    (OS_PRIO)APP_CFG_TASK_USER_IF_PRIO,
                    (CPU_STK *)&AppTaskUserIFStk[0],
                    (CPU_STK_SIZE)APP_CFG_TASK_USER_IF_STK_SIZE / 10,
                    (CPU_STK_SIZE)APP_CFG_TASK_USER_IF_STK_SIZE,
                    (OS_MSG_QTY)0,
                    (OS_TICK)0,
                    (void *)0,
#if (DEBUG_TASK_RESOURCE == 1)
                    (OS_OPT)(OS_OPT_TASK_STK_CHK | OS_OPT_TASK_STK_CLR),
#else
                    (OS_OPT)(OS_OPT_TASK_STK_CLR),
#endif
                    (OS_ERR *)&err);
}
#endif

static void AppObjCreate(void)
{
#if (DEBUG_TASK_RESOURCE == 1)
    OS_ERR err;

    OSSemCreate((OS_SEM *)&AppPrintfSemp,
                    (CPU_CHAR *)"AppPrintfSemp",
                    (OS_SEM_CTR)1,
                    (OS_ERR *)&err);
#endif
}

/* USER CODE END 4 */

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void)
{
    /* USER CODE BEGIN Error_Handler_Debug */
    /* User can add his own implementation to report the HAL error return state */
    __disable_irq();
    while(1)
    {
    }
    /* USER CODE END Error_Handler_Debug */
}

#ifdef USE_FULL_ASSERT
/**
 * @brief  Reports the name of the source file and the source line number
 *         where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line)
{
    /* USER CODE BEGIN 6 */
    /* User can add his own implementation to report the file name and line number,
       ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
    /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
