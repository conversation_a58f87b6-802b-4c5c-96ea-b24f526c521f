# 设备配置生成脚本

本目录包含用于生成设备配置文件的脚本。这些脚本支持生成包含证书和令牌信息的设备配置文件。

## 脚本说明

### generate_wifi_device_config_v2.py

该脚本用于生成包含WiFi证书信息的设备配置文件。

```bash
python generate_wifi_device_config_v2.py -d <device_serial> [--assets-dir PATH] [--input-dir PATH] [--output-dir PATH]
```

参数说明：
- `-d, --device`: 设备序列号
- `--assets-dir`: 资产目录路径（包含device和wifi_device目录）
- `--input-dir`: 包含原始hex文件的输入目录
- `--output-dir`: 生成的hex文件的输出目录

生成的配置文件会包含以下信息：
- 0x080FE000: 外部服务公钥长度（2字节）
- 0x080FE002: 外部服务公钥数据（ca.cer）
- 0x080FE800: 设备私钥长度（2字节）
- 0x080FE802: 设备私钥数据（client.key）
- 0x080FF000: 设备证书长度（2字节）
- 0x080FF002: 设备证书数据（client.cer）

### generate_device_token_config.py

该脚本用于为现有的WiFi设备配置文件添加设备令牌（TOKEN）信息。

```bash
python generate_device_token_config.py -d <device_serial> -t <device_token> [--input-dir PATH] [--output-dir PATH]
```

参数说明：
- `-d, --device`: 设备序列号
- `-t, --token`: 设备令牌字符串
- `--input-dir`: 包含WiFi设备配置hex文件的输入目录
- `--output-dir`: 生成的包含令牌的hex文件的输出目录

生成的配置文件会包含以下信息：
- 0x080FD800: 设备令牌长度（2字节）
- 0x080FD802: 设备令牌数据（token字符串）

### batch_generate_tokens.py

该脚本用于批量为多个设备生成包含唯一令牌的配置文件。

```bash
python batch_generate_tokens.py --devices-list <file.csv/json> [--output-dir PATH] [--input-base-dir PATH] [--use-existing-tokens]
```

参数说明：
- `--devices-list`: 包含设备序列号的CSV或JSON文件路径
- `--output-dir`: 生成的文件的基础输出目录
- `--input-base-dir`: 包含WiFi设备配置hex文件的基础输入目录
- `--use-existing-tokens`: 如果可用，使用token_mapping中的现有令牌

## 目录结构

脚本预期的目录结构如下：

```
assets/
├── device/
│   └── <device_serial>/
│       └── device_config-<device_serial>.hex  # 原始设备配置
├── wifi_device/
│   └── <device_serial>/
│       ├── ca.cer                             # CA证书
│       ├── client.cer                         # 客户端证书
│       ├── client.key                         # 客户端私钥
│       └── device_config-<device_serial>.hex  # 包含WiFi证书的设备配置
└── galen_cloud/
    ├── token_mapping.json                     # 设备令牌映射
    └── <device_serial>/
        └── device_config-<device_serial>.hex  # 包含WiFi证书和令牌的设备配置
```

## 使用示例

1. 生成包含WiFi证书的设备配置：

```bash
python generate_wifi_device_config_v2.py -d 100001
```

2. 为设备添加令牌信息：

```bash
python generate_device_token_config.py -d 100001 -t "your-device-token-here"
```

3. 批量为多个设备生成令牌和配置文件：

```bash
python batch_generate_tokens.py --devices-list ../assets/example_devices.json
```

这将会为`example_devices.json`中列出的所有设备生成包含唯一令牌的配置文件。 