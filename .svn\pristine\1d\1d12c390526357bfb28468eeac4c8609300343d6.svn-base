#ifndef CRYPTO_H
#define CRYPTO_H

#include <stdint.h>
#include <stddef.h>
#include "stm32f4xx_hal.h"
#include "cmox_crypto.h"
#include "cmox_ecc.h"
#include "cmox_hash.h"
#include "fatfs.h"

/* Define sizes */
#define IV_SIZE 12
#define TAG_SIZE 16
#define SIGNATURE_SIZE 64
#define MAX_CIPHER_LENGTH 2048
#define EXAMPLE_FILE_SIZE 96

/* Enum for File Status */
typedef enum {
    FILE_UNSET = 0, 
    FILE_OK = 1,
    FILE_DECRYPT_ERROR, 
    FILE_VERIFICATION_ERROR
} eFileStatus_t;

typedef enum {
	AES_LOCAL_DATA_KEY,
	AES_FW_KEY
} eAESKeyType_t;

/* External Variables */
extern const uint8_t AAD[];

/* Function Prototypes */
size_t encrypt_data(eAESKeyType_t key_type, uint8_t *plaintext_buffer, uint8_t *encrypted_buffer, uint16_t length);
size_t decrypt_data(eAESKeyType_t key_type, uint8_t *encrypted_buffer, uint8_t *plaintext_buffer, uint16_t length);
size_t decrypt_data_chunk(eAESKeyType_t key_type, uint8_t *encrypted_data, uint8_t *decrypted_data, uint16_t chunk_length, uint8_t *iv, uint8_t first_chunk);
uint8_t validate_tag(eAESKeyType_t key_type, uint8_t * tag);
eFileStatus_t validate_file_content(FIL* fil, uint32_t fileSize);
eFileStatus_t validate_flash_file(void);

#endif // CRYPTO_H
