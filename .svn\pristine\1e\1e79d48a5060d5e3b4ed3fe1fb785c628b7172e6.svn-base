#include "high_leak.h"
#include "AlarmDetection.h"
#include "parameters_calc.h"
#include "bsp_bldcm_control.h"
#include "bsp_pid.h"
#include "SignalCondition.h"
#include "start_climbing.h"
#include "delay/delay.h"
#include "StaterBar.h"
#include "GlobalVariable.h"

static uint8_t s_bHighLeakStatus = false;
static uint8_t s_bAbnormalHighLeakStatus = false;

/**
  * @brief  获取高漏气状态
  * @param  无
  * @retval 高漏气状态
  */
uint8_t is_high_leak(void)
{
    return s_bHighLeakStatus;
}

/**
  * @brief  设置高漏气状态
  * @param  无
  * @retval 高漏气状态
  */
void set_high_leak_status(uint8_t status)
{
    s_bHighLeakStatus = status;
}

/**
  * @brief  高漏气处理
  * @param  无
  * @retval 高漏气状态
  */
uint8_t high_leak_process(int16_t shRealFlow)
{
    static uint16_t s_hHighLeakCnt = 0, s_hNoHightLeakCnt = 0;
    static float s_fHighLeakPressure = 0;
    static uint8_t s_bMaskDetachmentStatus = 0;
    static float s_fFlowErr = 0;
	if(get_work_mode() == SYS_WM_HIGHFLOW) // 高流量模式不进行高漏气降低压力处理
	{
		return false;
	}
    if(is_high_leak() == true)
    {
        static uint32_t flowAdjustTick = 0;

        if(time_count(tick_get(), flowAdjustTick) > 90)
        {
            flowAdjustTick = tick_get();
			
			s_fFlowErr = (float)(HIGHLEAK_FLOW_VALUE - shRealFlow);
			s_fFlowErr = s_fFlowErr > 100 ? 100: s_fFlowErr;
			if(s_fFlowErr < -500)
			{
				s_fFlowErr = -500;
			}
			s_fHighLeakPressure += ((s_fFlowErr) * 0.01);
			
			if(s_fHighLeakPressure > GetLowPadPress())  //高漏气时的压力不能超过当前设置的压力
			{
				s_fHighLeakPressure = GetLowPadPress();
			}
			if(s_fHighLeakPressure < 40)
			{
				s_fHighLeakPressure = 40;
			}
			set_pid_target(MOTOR_ConvertPress2ADcode((int16_t)s_fHighLeakPressure));
        }

        if(shRealFlow <= EXIT_HIGHLEAK_THRESHOLD)
        {
            s_hHighLeakCnt = 0;

            if(++ s_hNoHightLeakCnt > 150)  //连续检测1.5S没有大漏气，则重新恢复功能
            {
                s_hNoHightLeakCnt = 0;
                s_bMaskDetachmentStatus = 0;
                bldcm_data.BreathState = IN_NO_TRIGGER;
                set_climbing_start_value((int16_t)s_fHighLeakPressure);
				set_high_leak_status(false);
            }
        }
        else
        {
            s_hNoHightLeakCnt = 0;
        }

        return true;
    }
    else
    {
        if(shRealFlow > HIGHLEAK_THRESHOLD)
        {
			set_climbing_target_value(GetLowPadPress());
            if(++ s_hHighLeakCnt > 510)  //大漏气持续4.0S，则进入高漏气状态
            {
                s_hHighLeakCnt = 0;
				set_high_leak_status(true);
                s_fHighLeakPressure = (float)GetLowPadPress();
				
            }
        }
        else
        {
            s_hHighLeakCnt = 0;
        }

        return false;
    }
}

/**
  * @brief  获取高漏气状态
  * @param  无
  * @retval 非故意漏气超过30L/min，并持续30s，则进入高漏气状态
  */
uint8_t Get_AbnormalHighLeakStatus(void)
{
    return s_bAbnormalHighLeakStatus;
}

/**
  * @brief  设置高漏气状态
  * @param  无
  * @retval 非故意漏气超过30L/min，并持续30s，则进入高漏气状态
  */
void Set_AbnormalHighLeakStatus(uint8_t status)
{
    s_bAbnormalHighLeakStatus = status;
}

/**
  * @brief  非故意漏气超过30L/min，并持续30s 检测 
  * @param  无
  * @retval 
  */
void DetectAbnormalHighLeakStatus(uint8_t u8WorkMode)
{
    static uint16_t s_hHighLeak30LCnt = 0, s_hNoHightLeak20LCnt = 0;
    const MACHINECONFIG *pMachineConfig = g_ConfigSave.GetMachineConfig();

//    //kpap只有CPAP和APAP模式
//    if(u8WorkMode > SYS_WM_APAP)
//	{
//        Set_AbnormalHighLeakStatus(false);
//		return;
//	}

    if(u8WorkMode == SYS_WM_HIGHFLOW)
	{
        Set_AbnormalHighLeakStatus(false);
		return;
	}

//    DEBUG_INFO("LeakValue = %d\n", GetDispLeakFlow());
    //非故意漏气超过30L/min，并持续30s，则进入高漏气状态
    if (GetDispLeakFlow() > ABNORMAL_HIGH_LEAK_THRESHOLD_30L)
    {
//        DEBUG_INFO("             s_hHighLeak30LCnt = %d\n", s_hHighLeak30LCnt);
        s_hNoHightLeak20LCnt = 0;
        if (Get_AbnormalHighLeakStatus() == false 
            && (++s_hHighLeak30LCnt > ABNORMAL_HIGH_LEAK_TIME_30S))
        {
//            DEBUG_INFO("Over 30L/min,Enter High Leak!!!!!!!!!!!!!!\n");
            Set_AbnormalHighLeakStatus(true);
            if (u8WorkMode == SYS_WM_APAP || u8WorkMode == SYS_WM_IAPAP)
            {
                //如果非故意漏气超过30L/min，并持续30s
                //进入高漏气状态，将输出压力降低2cmH2O
                RedcutAPAPPress(u8WorkMode, 20);
            }
            else if (u8WorkMode == SYS_WM_AUTOB)
            {
                if((g_ConfigSave.GetMarketAreaType() == MARKET_AREA_FDA && pMachineConfig->ProNum < 2)
                                || (g_ConfigSave.GetMarketAreaType() != MARKET_AREA_FDA && g_u32SysTickTimer > ParameterData->SeparateNight * 60 * 100))
                {

                    if(g_ConfigSave.GetMarketAreaType() == MARKET_AREA_FDA && pMachineConfig->ProNum < 2)
                    {
                        RedcutFDAAutoBPress(20);
                    }
                    else
                    {
                        RedcutAutoBPress(20);
                    }
                }
            }
        }
    }
    else if (GetDispLeakFlow() <= ABNORMAL_HIGH_LEAK_THRESHOLD_30L && GetDispLeakFlow() > ABNORMAL_HIGH_LEAK_THRESHOLD_20L)
    {
        s_hNoHightLeak20LCnt = 0;
        s_hHighLeak30LCnt = 0;
    }
    else
    {
        s_hHighLeak30LCnt = 0;
        //在进入高漏气状态后，非故意漏气低于20L/min，并持续20s，则退出高漏气状态
        if (GetDispLeakFlow() <= ABNORMAL_HIGH_LEAK_THRESHOLD_20L)
        {
//            DEBUG_INFO("                  s_hNoHightLeak20LCnt = %d\n", s_hNoHightLeak20LCnt);
            if (Get_AbnormalHighLeakStatus() 
                && (++s_hNoHightLeak20LCnt > ABNORMAL_HIGH_LEAK_TIME_30S))
            {
//                DEBUG_INFO("Return High Leak!!!!!\n");
                Set_AbnormalHighLeakStatus(false);
                Reset_NoneEventReducePressureTick(g_u32SysTickTimer);//重置无事件200s降压的Tick值
                ClearNullPeriodPara();  //退出高漏气状态时,空窗期15min时间段将重置为0
////                //从高漏气状态退出，将恢复输出压力
////                RegApapPress(u8WorkMode, 20);
            }
        }
    }
}

