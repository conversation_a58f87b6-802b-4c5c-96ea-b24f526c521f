const char* const lang_espanol[] =
{
	// 0 - STR_BASE
	".",
	// 1 - SIMPLIFIEDCHINESE_IDX
	"\xE7\xAE\x80\xE4\xBD\x93\xE4\xB8\xAD\xE6\x96\x87",
	// 2 - ENGLISH_IDX
	"English",
	// 3 - ESPANOL_IDX
	"Espa\xC3\xB1""ol",
	// 4 - ARABIC_IDX
	"\xD8\xA7\xD9\x84\xD8\xB9\xD8\xB1\xD8\xA8\xD9\x8A\xD8\xA9",
	// 5 - TURKISH_IDX
	"T\xC3\xBC""rk\xC3\xA7""e",
	// 6 - PORTUGUESE_IDX
	"Portugu\xC3\xAA""s",
	// 7 - THAI_IDX
	"\xE0\xB8\xA0\xE0\xB8\xB2\xE0\xB8\xA9\xE0\xB8\xB2\xE0\xB9\x84\xE0\xB8\x97\xE0\xB8\xA2",
	// 8 - BULGARRIAN_IDX
	"\xD0\x91\xD1\x8A\xD0\xBB\xD0\xB3\xD0\xB0\xD1\x80\xD1\x81\xD0\xBA\xD0\xB8",
	// 9 - UKRAINIAN_IDX
	"\xD0\xA3\xD0\xBA\xD1\x80\xD0\xB0\xD1\x97\xD0\xBD\xD1\x81\xD1\x8C\xD0\xBA\xD0\xB0",
	// 10 - RUSSIAN_IDX
	"\xD0\xA0\xD1\x83\xD1\x81\xD1\x81\xD0\xBA\xD0\xB8\xD0\xB9",
	// 11 - CHECKING_IDX
	"Comprobando......",
	// 12 - CHECKERROR_IDX
	"Error",
	// 13 - CHECKOK_IDX
	"OK",
	// 14 - SDRAM_ERROR_IDX
	"SDRAM",
	// 15 - MOTOR_ERROR_IDX
	"Motor",
	// 16 - HUMI_TEMP_SENSOR_ERROR_IDX
	"Sensor de temperatura",
	// 17 - FLOW_SENSOR_ERROR_IDX
	"Sensor de flujo",
	// 18 - SPI_FLASH_ERROR_IDX
	"SPI Flash",
	// 19 - EEPROM_ERROR_IDX
	"EEPROM",
	// 20 - RTC_ERROR_IDX  
	"RTC",
	// 21 - YLCGQ_REF_ERROR_IDX
	"Presi\xC3\xB3""n",
	// 22 - MOTOR_POWER_24V_ERROR_IDX
	"Sum. 24V Motor",
	// 23 - ALM_POWERFAULT_IDX
	"Falla de motor",
	// 24 - ALM_HIPRRESS_IDX
	"\xC2\xA1""Presi\xC3\xB3""n Alta!",
	// 25 - ALM_LOWPRESS_IDX
	"\xC2\xA1""Presi\xC3\xB3""n Baja!",
	// 26 - ALM_LOWLEAK_IDX
	"Fuga de aire baja!",
	// 27 - ALM_HIGHLEAK_IDX
	"Fuga de aire alta!",
	// 28 - ALM_LOWMV_IDX
	"MV Bajo!",
	// 29 - ALM_LOWVT_IDX
	"VT Bajo!",
	// 30 - ALM_APNEA_IDX
	"Apnea",
	// 31 - ALM_HUMFAILURE_IDX
	"Fallo humidificador",
	// 32 - ALM_HUMFAULT_IDX
	"Fallo humidificador",
	// 33 - ALM_HEATPIPEFAILURE_IDX
	"Fallo tubo calen",
	// 34 - ALM_FANOVERHEADT_IDX
	"Falla de motor",
	// 35 - ALM_LOWVOLTAGE_IDX
	"Voltaje bajo",
	// 36 - ALM_PRESSURE_ABNORMAL_IDX
	"Anomal\xC3\xAD""a sensor de\npresi\xC3\xB3""n",
	// 37 - ALM_SDFULL_IDX
	"Tarjeta SD llena",
	// 38 - MONITORVIEW_IDX
	"Pantalla",
	// 39 - PFCURVE_IDX
	"Gr\xC3\xA1""fica",
	// 40 - PARASETTING_IDX
	"Ajust.par\xC3\xA1""metros",
	// 41 - SYSSETTING_IDX
	"Sistema",
	// 42 - ALARMSETTING_IDX
	"Ajust. alarma",
	// 43 - INFOSETTING_IDX
	"Info",
	// 44 - PREHEATSETTING_IDX
	"Ajust. precal.",
	// 45 - QUICKSET_IDX
	"Config. r\xC3\xA1""pida",
	// 46 - PRESSUER_IDX
	"Presi\xC3\xB3""n",
	// 47 - FLOW_IDX
	"Flow",
	// 48 - VT_IDX
	"VT",
	// 49 - MV_IDX
	"MV",
	// 50 - MV_2_IDX
	"MV",
	// 51 - BPM_IDX
	"BPM",
	// 52 - INSPTIME_IDX
	"InspTime",
	// 53 - IE_IDX
	"I:E",
	// 54 - LEAKAGE_IDX
	"Fuga",
	// 55 - SPO2_IDX
	"SpO2",
	// 56 - PR_IDX
	"PR",
	// 57 - MENU_IDX
	"Men\xC3\xBA",
	// 58 - MONITOR_TEMP_IDX
	"Tempe\nratura",
	// 59 - MONITOR_MODE_IDX
	"Modo",
	// 60 - AUTOON_IDX
	"Auto encendido",
	// 61 - AUTOOFF_IDX
	"Auto apagado",
	// 62 - MODE_IDX
	"Modo trabajo",
	// 63 - IPAP_IDX
	"IPAP",
	// 64 - EPAP_IDX
	"EPAP",
	// 65 - STARTPRESS_IDX
	"Presi\xC3\xB3""n Inicial ",
	// 66 - BELEX_IDX
	"Belex",
	// 67 - RAMP_IDX
	"Rampa",
	// 68 - ISENS_IDX
	"ISens",
	// 69 - ESENS_IDX
	"ESens",
	// 70 - ISLOP_IDX
	"ISlop",
	// 71 - MAXINSPTIME_IDX
	"MaxInspTime",
	// 72 - MININSPTIME_IDX
	"MinInspTime",
	// 73 - IRAMP_IDX
	"iRamp",
	// 74 - MAXP_IDX
	"Presi\xC3\xB3""n m\xC3\xA1""xima",
	// 75 - MINP_IDX
	"Presi\xC3\xB3""n m\xC3\xAD""nima",
	// 76 - SLOP_SENS_IDX
	"Increm. sensib.",
	// 77 - SMART_PRESSURE_IDX
	"Presi\xC3\xB3""n smart",
	// 78 - DIVIDENIGHT_IDX
	"Minute and night",
	// 79 - MAXPS_IDX
	"MaxPS",
	// 80 - MAXIPAP_IDX
	"MaxIPAP",
	// 81 - MINIPAP_IDX
	"MinIPAP",
	// 82 - MINEPAP_IDX
	"MinEPAP",
	// 83 - TARGET_VT_IDX   
	"Vol. corriente obj.",
	// 84 - VAF_SWITCH_IDX
	"AVAPS",
	// 85 - TARGET_FLOW_IDX
	"Flow",
	// 86 - TUBESELECTION_IDX
	"Selecci\xC3\xB3""n tubo",
	// 87 - MASKSETTING_IDX
	"Ajust. masc.",
	// 88 - MASKTEST_IDX
	"Test masc.",
	// 89 - ECOSETTING_IDX
	"Ajust. ECO",
	// 90 - DATE_IDX
	"Fecha",
	// 91 - TIME_IDX
	"Hora",
	// 92 - BACKLIGHT_IDX
	"Backlight",
	// 93 - BRIGHTNESS_IDX
	"Brillo",
	// 94 - LANGUAGE_IDX
	"Idioma",
	// 95 - P_UINT_IDX
	"Ud. presi\xC3\xB3""n",
	// 96 - WIFI_SET_IDX
	"Ajust. WiFi",
	// 97 - WIFI_SELECT_IDX
	"Selecci\xC3\xB3""n de Wifi",
	// 98 - BLUETOOTH_IDX
	"Bluetooth",
	// 99 - RESET_IDX
	"Rest. sistema",
	// 100 - SOFTWAREVERSION_IDX
	"Vers. software",
	// 101 - SN_IDX
	"N\xC3\xBA""mero de serie",
	// 102 - HIGHPRESS_IDX
	"Presi\xC3\xB3""n Alta",
	// 103 - LOWPRESS_IDX
	"Presi\xC3\xB3""n Baja!",
	// 104 - HIGHLEAKAGE
	"Fuga Alta",
	// 105 - LOWMV_IDX
	"LowMV",
	// 106 - APNEA_SET_IDX
	"Apnea",
	// 107 - REPLACEFILTER_IDX
	"Cambiar Filtro",
	// 108 - REPLACEMASK_IDX
	"Cambio masc.",
	// 109 - REPLACETUBE_IDX
	"Cambiar Tubo ",
	// 110 - REPLACEFILTER1_IDX
	"Cambiar Filtro",
	// 111 - REPLACEMASK1_IDX
	"Cambio masc.",
	// 112 - REPLACETUBE1_IDX
	"Cambiar Tubo ",
	// 113 - REPLACEFILTERCONTENT_IDX
	"Por favor, cambie el filtro ",
	// 114 - REPLACEMASKCONTENT_IDX
	"Por favor, cambie la mascarilla ",
	// 115 - REPLACETUBECONTENT_IDX
	"Por favor, cambie el tubo ",
	// 116 - SDCARD_CONNECTIONSTATUS_IDX
	"SD prompt",
	// 117 - SDCARD_UNCONNECTIONCONTENT_IDX
	"No reconoce tarjeta SD, verifique.",
	// 118 - KNOBTONE_IDX
	"Bot\xC3\xB3""n de tono",
	// 119 - QRCODE_IDX
	"C\xC3\xB3""digo qr",
	// 120 - NOTICE_IDX
	"Tips",
	// 121 - REPLACETFCARDCONTENT_IDX
	"Tarjeta SD llena",
	// 122 - USECYCLE_IDX
	"Ciclo de uso",
	// 123 - USETIME_IDX
	"Tiempo de uso ",
	// 124 - AVGPRESS_IDX
	"Presi\xC3\xB3""n promedio",
	// 125 - PRESSURE_95TH_IDX
	"Pres. titulaci\xC3\xB3""n",
	// 126 - AVGLEAKAGE_IDX
	"Fuga prom.",
	// 127 - AHI_IDX
	"AHI",
	// 128 - CSI_IDX
	"CAI",
	// 129 - USEDAYS_IDX
	"D\xC3\xAD""as de uso",
	// 130 - RUNHRS_IDX
	"Horas de uso",
	// 131 - PREHEATING_IDX
	"Precalentamiento",
	// 132 - PREHEATINGTIME_SET_IDX
	"Ajust.tiempo precal.",
	// 133 - HIMIDIFY_IDX
	"Equipo humid.",
	// 134 - TUBEWARMLEVEL_SET_IDX
	"Ajust. temperatura tubo",
	// 135 - PREHEATING_RESULT_IDX
	"Fin precalen.",
	// 136 - PREHEATING_OK_IDX
	"OK",
	// 137 - WLANTITLE_IDX
	"WLAN",
	// 138 - SCANNING_IDX
	"Scanning",
	// 139 - SCAN_FINISH_IDX
	"Scan Completo",
	// 140 - ENCRYPT_IDX
	"Encriptado",
	// 141 - UNENCRYPT_IDX
	"Desencriptado",
	// 142 - PASSWORD_IDX
	"Clave ",
	// 143 - BACK_IDX
	"Salir",
	// 144 - WIFI_CONNECTED_IDX
	"Conectado",
	// 145 - WIFI_DISCONNECTED_IDX
	"Desconectado",
	// 146 - WIFI_CONNECTING_IDX
	"Conectando,",
	// 147 - WIFI_WAIT_IDX
	"Por favor espere",
	// 148 - WIFI_CONNECTION_SUCCEEDED_IDX
	"Conectado",
	// 149 - WIFI_CONNECTION_FAILED_IDX
	"Conexi\xC3\xB3""n fallida,",
	// 150 - WIFI_ENTER_PASSWORD_IDX
	"Por favor ingrese nuevamente su clave",
	// 151 - PERFORMANCE_TEST_IDX
	"\xC2\xBF""Iniciar prueba de funcionamiento?",
	// 152 - TESTING_PROMPT_IDX
	"Probando",
	// 153 - TESTING_LEAK_IDX
	"Fuga de aire detectada, bloquee el extremo del tubo.",
	// 154 - TESTING_SUCCESS_IDX
	"Prueba existosa",
	// 155 - TESTING_IDX
	"Prueba ",
	// 156 - TESTING_FAIL_IDX
	"falla",
	// 157 - TESTING_YLCGQ_FAIL_IDX
	"Sensor de presi\xC3\xB3""n ",
	// 158 - TESTING_YL_PROTECT_MODULE_IDX
	"Prot. presi\xC3\xB3""n ",
	// 159 - TESTING_PROTECT_RESET_MODULE_IDX
	"Prot. reinicio circ. el\xC3\xA9""ctr. ",
	// 160 - RESETCONTENT_IDX
	"Esto restablecer\xC3\xA1"" los valores de f\xC3\xA1""brica.\xC2\xBF""Desea continuar?",
	// 161 - OK_RESET_IDX
	"OK",
	// 162 - CANCEL_RESET_IDX
	"Cancelar",
	// 163 - MASKWEARINGTEST_TITLE_IDX
	"Test masc.",
	// 164 - MASKTEST_START_IDX
	"Iniciar",
	// 165 - MASKTEST_STOP_IDX
	"Detener",
	// 166 - MASKTEST_EXIT_IDX
	"Salir",
	// 167 - MASKTEST_PERFECT_IDX
	"Perfecto",
	// 168 - MASKTEST_ADJUST_IDX
	"Ajustes",
	// 169 - OFF_IDX
	"Apagado",
	// 170 - ON_IDX
	"Encendido",
	// 171 - CPAP_IDX
	"CPAP",
	// 172 - APAP_IDX
	"APAP",
	// 173 - IAPAP_IDX
	"iAPAP",
	// 174 - S_IDX
	"S",
	// 175 - T_IDX
	"T",
	// 176 - ST_IDX
	"ST",
	// 177 - AUTOB_IDX
	"AUTOB",
	// 178 - APCV_IDX
	"APCV",
	// 179 - VAF_ST_IDX
	"VAF-ST",
	// 180 - VAF_APCV_IDX
	"VAF-APCV",
	// 181 - HIGH_FLOW_IDX
	"HF",
	// 182 - APAP_1_IDX
	"APAP",
	// 183 - CMH2O_IDX
	"cmH2O",
	// 184 - HPA_IDX
	"hPa",
	// 185 - L_MIN_IDX
	"L/min",
	// 186 - L_IDX
	"L",
	// 187 - ML_IDX
	"ml",
	// 188 - LV_IDX
	"Lv",
	// 189 - SEC_IDX
	"s",
	// 190 - MIN_IDX
	"min",
	// 191 - DAY_IDX
	"D\xC3\xAD""as",
	// 192 - HOUR_IDX
	"Hora",
	// 193 - MONTH_IDX
	"Mes",
	// 194 - TUBE_15MM_IDX
	"Tubo est\xC3\xA1""ndar 15 mm",
	// 195 - TUBE_19MM_IDX
	"Tubo est\xC3\xA1""ndar 19 mm",
	// 196 - TUBE_HEATING_IDX
	"Tubo calen.",
	// 197 - NASALMASK_IDX
	"Masc nasal",
	// 198 - NOSEANDMOUTHMASK_IDX
	"Masc oronasal",
	// 199 - NASALPILLOW_IDX
	"Olivas nasal",
	// 200 - STANDARD_IDX
	"Est\xC3\xA1""ndar",
	// 201 - SENSITIVE_IDX
	"Sensible",
	// 202 - SOFT_IDX
	"Suave",
	// 203 - AUTO_IDX
	"Auto",
	// 204 - AUTO_CN_IDX
	"Auto",
	// 205 - BPMUNIT_IDX
	"BPM",
	// 206 - SEC_E_IDX
	"s",
	// 207 - PERCENTAGE_IDX
	"%",
	// 208 - I_STATUS_IDX
	"I",
	// 209 - E_STATUS_IDX
	"E",
	// 210 - ADMIN_CALI_MOTORSPEED_IDX
	"Motor Speed",
	// 211 - ADMIN_CALI_PREV_IDX
	"PREV",
	// 212 - ADMIN_CALI_OK_IDX
	"OK",
	// 213 - ADMIN_CALI_EXIT_IDX
	"Exit",
	// 214 - ADMIN_CALI_FLOW_IDX
	"Flow: ",
	// 215 - ADMIN_CALI_TIME_IDX
	"Calibration Time: ",
	// 216 - ADMIN_FLOW_CALI_ERROR_IDX
	"Abnormal Flow\nCalibration",
	// 217 - ADMIN_FLOWCALI_TITLE_IDX
	"FlowCali",
	// 218 - ADMIN_CALI_PRESSURE_IDX
	"Press: ",
	// 219 - ADMIN_PRESSCALI_TITLE_IDX
	"PressureCali",
	// 220 - ADMIN_PRESCALI_IDX
	"PressureCali",
	// 221 - ADMIN_FLOWCALI_IDX
	"FlowCali",
	// 222 - ADMIN_CLEARUSAGEDATA_PROMPT_IDX
	"Is all device data wiped?",
	// 223 - ADMIN_CLEARUSAGEDATA_SUCCESS_IDX
	"Data Wiped Successfully",
	// 224 - HF_MODE_USAGE_PROMPT_IDX
	"Heating Tube Better!",
	// 225 - ESLOP_IDX
	"ESlop",
	// 226 - CLEAR_USAGE_DATA_IDX
	"Wipe Usage Data",
	// 227 - MANAGER_SET_IDX
	"AdminSet",
	// 228 - NOSEANDMOUTHMASK_FM_1_IDX
	"Full Face Mask(FM\xE2\x85\xA0"")",
	// 229 - NOSEANDMOUTHMASK_FM_2_IDX
	"Full Face Mask(FM\xE2\x85\xA1"")",
	// 230 - NOSEANDMOUTHMASK_FM_2P_IDX
	"Full Face Mask(FMIIP)",
	// 231 - NOSEANDMOUTHMASK_FM_3_IDX
	"Full Face Mask(FM\xE2\x85\xA2"")",
	// 232 - ECO_MODE_TIPS_IDX
	"Wifi, Preheat, Humidifier, Backlight and Brightness will be disabled. Turn off to recover",
	// 233 - CHAR_IDX
	"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz,\xEF\xBC\x8C""~!@#$%^&*?-_:;/() \xE2\x84\x83""""'+,.<>[]\\`{|}",
	// 234 - NUMBER_IDX
	"-0.0123456789",
	// 235 - DEBUG_TITLE_IDX
	"Debug \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 236 - DEBUG_FLOWCALI_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE6\x89\x8B\xE5\x8A\xA8\xE5\xAE\x9A\xE6\xA0\x87",
	// 237 - DEBUG_FLOWAUTOCALI_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE8\x87\xAA\xE5\x8A\xA8\xE5\xAE\x9A\xE6\xA0\x87",
	// 238 - DEBUG_PRESCALI_IDX
	"\xE5\x8E\x8B\xE5\x8A\x9B\xE5\xAE\x9A\xE6\xA0\x87",
	// 239 - DEBUG_BUZZER_IDX
	"\xE8\x9C\x82\xE9\xB8\xA3\xE5\x99\xA8\xE6\xB5\x8B\xE8\xAF\x95",
	// 240 - DEBUG_HEATING_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\xB5\x8B\xE8\xAF\x95",
	// 241 - DEBUG_RESET_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE",
	// 242 - DEBUG_DEVICEINFO_IDX
	"\xE8\xAE\xBE\xE5\xA4\x87\xE4\xBF\xA1\xE6\x81\xAF",
	// 243 - DEBUG_SINGLEFAULT_IDX
	"\xE5\x8D\x95\xE4\xB8\x80\xE6\x95\x85\xE9\x9A\x9C\xE5\x8E\x8B\xE5\x8A\x9B\xE6\xB5\x8B\xE8\xAF\x95",
	// 244 - DEBUG_HARD_PROTECT_ON_IDX
	"\xE8\xBF\x87\xE5\x8E\x8B\xE4\xBF\x9D\xE6\x8A\xA4"":\xE5\xBC\x80",
	// 245 - DEBUG_HARD_PROTECT_OFF_IDX
	"\xE8\xBF\x87\xE5\x8E\x8B\xE4\xBF\x9D\xE6\x8A\xA4"":\xE5\x85\xB3",
	// 246 - DEBUG_CE_SWITCH_IDX
	"\xE7\x89\x88\xE6\x9C\xAC"":CE",
	// 247 - DEBUG_CHINA_SWITCH_IDX
	"\xE7\x89\x88\xE6\x9C\xAC"":\xE5\x9B\xBD\xE5\x86\x85",
	// 248 - DEBUG_FDA_SWITCH_IDX
	"\xE7\x89\x88\xE6\x9C\xAC"":FDA",
	// 249 - DEBUG_EXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 250 - FLOWCALI_TITLE_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE5\xAE\x9A\xE6\xA0\x87",
	// 251 - CALI_ADJUSTMOTOR_IDX
	"\xE8\xB0\x83\xE6\x95\xB4\xE9\xA9\xAC\xE8\xBE\xBE",
	// 252 - CALI_PRESSURE_IDX
	"\xE5\x8E\x8B\xE5\x8A\x9B\xEF\xBC\x9A",
	// 253 - CALI_PHYPRESSURE_IDX
	"\xE7\x89\xA9\xE7\x90\x86\xE5\x8E\x8B\xE5\x8A\x9B\xEF\xBC\x9A",
	// 254 - CALI_FLOW_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xEF\xBC\x9A",
	// 255 - CALI_PHYFLOW_IDX
	"\xE7\x89\xA9\xE7\x90\x86\xE6\xB5\x81\xE9\x87\x8F\xEF\xBC\x9A",
	// 256 - CALI_SPEED_IDX
	"\xE9\x80\x9F\xE5\xBA\xA6\xEF\xBC\x9A",
	// 257 - CALI_TIME_IDX
	"\xE5\xAE\x9A\xE6\xA0\x87\xE6\x97\xB6\xE9\x97\xB4\xEF\xBC\x9A",
	// 258 - CALI_SELF_CHECK_IDX
	"\xE8\x87\xAA\xE6\xA3\x80",
	// 259 - CALI_OK_IDX
	"\xE7\xA1\xAE\xE5\xAE\x9A",
	// 260 - CALI_PREV_IDX
	"\xE4\xB8\x8A\xE4\xB8\x80\xE4\xB8\xAA",
	// 261 - CALI_EXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 262 - AUTOCALI_EXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 263 - CALI_START_AUTOCALI_IDX
	"\xE5\xAE\x9A\xE6\xA0\x87\xE5\xBC\x80\xE5\xA7\x8B",
	// 264 - CALI_AUTOCALI_IDX
	"\xE8\x87\xAA\xE5\x8A\xA8\xE5\xAE\x9A\xE6\xA0\x87",
	// 265 - CALI_RETEST_EXPORT_DATA
	"\xE5\xA4\x8D\xE6\xB5\x8B\xE6\x95\xB0\xE6\x8D\xAE",
	// 266 - CALI_MODE_CHECK_IDX
	"\xE6\xA8\xA1\xE5\xBC\x8F\xE5\x88\x87\xE6\x8D\xA2",
	// 267 - AUTOCALI_TOTALTIME_IDX
	"\xE8\x80\x97\xE6\x97\xB6",
	// 268 - AUTOCALI_DATA_ERR_IDX
	"\xE6\xA0\xA1\xE5\x87\x86\xE6\x95\xB0\xE6\x8D\xAE\xE5\xBC\x82\xE5\xB8\xB8""!",
	// 269 - AUTOCALI_TIMEOUT_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE8\xAE\xA1\xE6\x9C\xAA\xE8\xBF\x9E\xE6\x8E\xA5""!",
	// 270 - AUTOCALI_RETEST_OK_IDX
	"\xE5\xA4\x8D\xE6\xB5\x8B\xE6\x88\x90\xE5\x8A\x9F""!",
	// 271 - AUTOCALI_RETEST_ERR_IDX
	"\xE5\xA4\x8D\xE6\xB5\x8B\xE9\x94\x99\xE8\xAF\xAF""!",
	// 272 - AUTOCALI_OK_IDX
	"\xE8\x87\xAA\xE5\x8A\xA8\xE5\xAE\x9A\xE6\xA0\x87\xE5\xAE\x8C\xE6\x88\x90""!",
	// 273 - AUTOCALI_MAX_FLOW_ERR_IDX
	"\xE6\x97\xA0\xE6\xB3\x95\xE8\xBE\xBE\xE5\x88\xB0\xE6\x9C\x80\xE5\xA4\xA7\xE5\xAE\x9A\xE6\xA0\x87\xE6\xB5\x81\xE9\x87\x8F""!",
	// 274 - CALI_MODE_TSI_IDX
	"\xE4\xBD\xBF\xE7\x94\xA8""TSI\xE5\xAE\x9A\xE6\xA0\x87\xE6\xAD\xA4\xE8\xAE\xBE\xE5\xA4\x87\xE3\x80\x82",
	// 275 - CALI_MODE_DEV_IDX
	"\xE4\xBD\xBF\xE7\x94\xA8\xE6\xB2\xBB\xE5\x85\xB7\xE5\xAE\x9A\xE6\xA0\x87\xE6\xAD\xA4\xE8\xAE\xBE\xE5\xA4\x87\xE3\x80\x82",
	// 276 - CALI_MODE_HOST_IDX
	"\xE4\xBD\x9C\xE4\xB8\xBA\xE6\xB2\xBB\xE5\x85\xB7\xE5\xAE\x9A\xE6\xA0\x87\xE5\x85\xB6\xE4\xBB\x96\xE8\xAE\xBE\xE5\xA4\x87\xE3\x80\x82",
	// 277 - DEBUG_FLOW_CALI_ERROR_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE5\xAE\x9A\xE6\xA0\x87\xE5\xBC\x82\xE5\xB8\xB8\xE8\xAF\xB7\xE7\xA1\xAE\xE8\xAE\xA4",
	// 278 - PRESSCALI_TITLE_IDX
	"\xE5\x8E\x8B\xE5\x8A\x9B\xE5\xAE\x9A\xE6\xA0\x87",
	// 279 - DEBUG_AMBIENTTEMP_IDX
	"\xE5\xAE\xA4\xE6\xB8\xA9"":",
	// 280 - DEBUG_HEATINGCHIPTEMP_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\x9D\xBF\xE6\xB8\xA9\xE5\xBA\xA6"":",
	// 281 - DEBUG_TUBETEMP_IDX
	"\xE7\xAE\xA1\xE8\xB7\xAF\xE6\xB8\xA9\xE5\xBA\xA6"":",
	// 282 - DEBUG_LANGUAGE_IDX
	"\xE5\xBD\x93\xE5\x89\x8D\xE8\xAF\xAD\xE8\xA8\x80"":",
	// 283 - DEBUG_TIME_IDX
	"\xE8\xAE\xBE\xE5\xA4\x87\xE6\x97\xB6\xE9\x97\xB4"":",
	// 284 - DEBUG_BRIGHTNESS_IDX
	"\xE7\x8E\xAF\xE5\xA2\x83\xE5\x85\x89\xE7\xBA\xBF"":",
	// 285 - DEBUG_MOTOR_TEMP_IDX
	"\xE7\x94\xB5\xE6\x9C\xBA\xE6\xB8\xA9\xE5\xBA\xA6"":",
	// 286 - DEBUG_POWER_VOL_IDX
	"\xE7\x94\xB5\xE6\xBA\x90\xE7\x94\xB5\xE5\x8E\x8B"":",
	// 287 - DEBUG_TEMPERATUREUNIT_IDX
	"\xE2\x84\x83",
	// 288 - DEBUG_BUZZERTITLE_IDX
	"\xE8\x9C\x82\xE9\xB8\xA3\xE5\x99\xA8\xE6\xB5\x8B\xE8\xAF\x95"" \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 289 - DEBUG_HIGHPRIORITYALARM_IDX
	"\xE9\xAB\x98\xE4\xBC\x98\xE5\x85\x88\xE7\xBA\xA7\xE6\x8A\xA5\xE8\xAD\xA6",
	// 290 - DEBUG_UNLOCKSOUNDS_IDX
	"\xE8\xA7\xA3\xE9\x94\x81\xE5\xA3\xB0\xE9\x9F\xB3",
	// 291 - DEBUG_POWERONSOUNDS_IDX
	"\xE5\xBC\x80\xE6\x9C\xBA\xE5\xA3\xB0\xE9\x9F\xB3",
	// 292 - DEBUG_PREHEATINGBEEP_IDX
	"\xE9\xA2\x84\xE7\x83\xAD\xE6\x8F\x90\xE7\xA4\xBA\xE9\x9F\xB3",
	// 293 - DEBUG_TOUCH_TONE_IDX
	"\xE6\x97\x8B\xE9\x92\xAE\xE9\x9F\xB3",
	// 294 - DEBUG_ABNORMALPOWEROFFSOUNDS_IDX
	"\xE6\x8E\x89\xE7\x94\xB5\xE6\x8A\xA5\xE8\xAD\xA6\xE9\x9F\xB3",
	// 295 - DEBUG_BUZZEREXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 296 - DEBUG_YES_IDX
	"\xE6\x98\xAF",
	// 297 - DEBUG_NO_IDX
	"\xE5\x90\xA6",
	// 298 - DEBUG_HEATINGTITLE_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\xB5\x8B\xE8\xAF\x95"" \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 299 - DEBUG_HEATINGCHIPTITLE_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\x9D\xBF\xE5\x8A\xA0\xE7\x83\xAD\xE6\xB5\x8B\xE8\xAF\x95",
	// 300 - DEBUG_TUBETITLE_IDX
	"\xE7\xAE\xA1\xE8\xB7\xAF\xE5\x8A\xA0\xE7\x83\xAD\xE6\xB5\x8B\xE8\xAF\x95",
	// 301 - DEBUG_HEATINGCHIPRESULT_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\x9D\xBF\xE5\x8A\xA0\xE7\x83\xAD\xE4\xB8\xAD\xEF\xBC\x81",
	// 302 - DEBUG_TUBERESULT_IDX
	"\xE5\x8A\xA0\xE6\xB8\xA9\xE7\xAE\xA1\xE8\xB7\xAF\xE5\x8A\xA0\xE7\x83\xAD\xE4\xB8\xAD\xEF\xBC\x81",
	// 303 - DEBUG_HEATINGCHIPRESULT_STOP_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\x9D\xBF\xE5\x81\x9C\xE6\xAD\xA2\xE5\x8A\xA0\xE7\x83\xAD\xEF\xBC\x81",
	// 304 - DEBUG_TUBERESULT_STOP_IDX
	"\xE5\x8A\xA0\xE6\xB8\xA9\xE7\xAE\xA1\xE8\xB7\xAF\xE5\x81\x9C\xE6\xAD\xA2\xE5\x8A\xA0\xE7\x83\xAD\xEF\xBC\x81",
	// 305 - DEBUG_STARTHEATING_IDX
	"\xE5\xBC\x80\xE5\xA7\x8B\xE5\x8A\xA0\xE7\x83\xAD",
	// 306 - DEBUG_STOPHEATING_IDX
	"\xE5\x81\x9C\xE6\xAD\xA2\xE5\x8A\xA0\xE7\x83\xAD",
	// 307 - DEBUG_HEATINGEXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 308 - DEBUG_SYSRESETTITLE_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE"" \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 309 - DEBUG_SYSRESETTITLE1_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE\xE8\xBF\x87\xE7\xA8\x8B\xE9\x9C\x80\xE8\xA6\x81""3-4\xE5\x88\x86\xE9\x92\x9F"",\xE8\xAF\xB7\xE5\x9C\xA8\xE6\xAD\xA4\xE8\xBF\x87\xE7\xA8\x8B\xE4\xB8\xAD\xE4\xB8\x8D\xE8\xA6\x81\xE5\x85\xB3\xE9\x97\xAD\xE7\x94\xB5\xE6\xBA\x90\xE3\x80\x82",
	// 310 - DEBUG_RESETSYSPARA_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE""--\xE5\x90\x84\xE7\xB3\xBB\xE7\xBB\x9F\xE5\x8F\x82\xE6\x95\xB0",
	// 311 - DEBUG_RESETTIPARA_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE""--\xE5\x90\x84\xE6\xBB\xB4\xE5\xAE\x9A\xE5\x8F\x82\xE6\x95\xB0",
	// 312 - DEBUG_RESETDATASAVE_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE""--\xE6\x95\xB0\xE6\x8D\xAE\xE5\xAD\x98\xE5\x82\xA8",
	// 313 - DEBUG_RESETSTART_IDX
	"\xE5\xBC\x80\xE5\xA7\x8B\xE6\x81\xA2\xE5\xA4\x8D"",\xE8\xAF\xB7\xE8\x80\x90\xE5\xBF\x83\xE7\xAD\x89\xE5\xBE\x85""...",
	// 314 - DEBUG_FINISHSYSPARARESET_IDX
	"\xE7\xB3\xBB\xE7\xBB\x9F\xE5\x8F\x82\xE6\x95\xB0\xE6\x81\xA2\xE5\xA4\x8D\xE5\xAE\x8C\xE6\x88\x90\xEF\xBC\x81",
	// 315 - DEBUG_FINISHTIPARARESET_IDX
	"\xE6\xBB\xB4\xE5\xAE\x9A\xE5\x8F\x82\xE6\x95\xB0\xE6\x81\xA2\xE5\xA4\x8D\xE5\xAE\x8C\xE6\x88\x90\xEF\xBC\x81",
	// 316 - DEBUG_RESETSTART1_IDX
	"\xE5\xBC\x80\xE5\xA7\x8B\xE6\x81\xA2\xE5\xA4\x8D"",\xE8\xAF\xB7\xE8\x80\x90\xE5\xBF\x83\xE7\xAD\x89\xE5\xBE\x85"",\xE5\x85\xB1\xE4\xB8\xA4\xE9\x83\xA8\xE5\x88\x86""...",
	// 317 - DEBUG_FINISHFIRST_IDX
	"\xE5\x88\x9D\xE5\xA7\x8B\xE5\x8C\x96\xE7\xAC\xAC\xE4\xB8\x80\xE9\x83\xA8\xE5\x88\x86\xE5\xAE\x8C\xE6\x88\x90\xEF\xBC\x81",
	// 318 - DEBUG_FINISHDAYASAVERESET_IDX
	"\xE6\x95\xB0\xE6\x8D\xAE\xE5\xAD\x98\xE5\x82\xA8\xE6\x81\xA2\xE5\xA4\x8D\xE5\xAE\x8C\xE6\x88\x90",
	// 319 - DEBUG_DEVICEINFOTITLE_IDX
	"\xE8\xAE\xBE\xE5\xA4\x87\xE4\xBF\xA1\xE6\x81\xAF",
	// 320 - DEBUG_CHIPMODELTEXT_IDX
	"\xE8\x8A\xAF\xE7\x89\x87\xE5\x9E\x8B\xE5\x8F\xB7""  :",
	// 321 - DEBUG_FONTBMPVERSIONTEXT_IDX
	"\xE8\xB5\x84\xE6\xBA\x90\xE7\x89\x88\xE6\x9C\xAC""  :",
	// 322 - DEBUG_SOFTWAREVERTEXT_IDX
	"\xE8\xBD\xAF\xE4\xBB\xB6\xE7\x89\x88\xE6\x9C\xAC""  :",
	// 323 - DEBUG_BOOTLOADERTEXT_IDX
	"Boot\xE7\x89\x88\xE6\x9C\xAC"" :",
	// 324 - DEBUG_SNTEXT_IDX
	"\xE4\xBA\xA7\xE5\x93\x81\xE5\xBA\x8F\xE5\x88\x97\xE5\x8F\xB7""  :",
	// 325 - DEBUG_PRODUCTIONDATE_IDX
	"\xE7\x94\x9F\xE4\xBA\xA7\xE6\x97\xA5\xE6\x9C\x9F""  :",
	// 326 - DEBUG_SINGLEFAULTPTITLE_IDX
	"\xE5\x8D\x95\xE4\xB8\x80\xE6\x95\x85\xE9\x9A\x9C\xE5\x8E\x8B\xE5\x8A\x9B\xE6\xB5\x8B\xE8\xAF\x95"" \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 327 - DEBUG_CPAPPRESSURE_IDX
	"\xE5\x8D\x95\xE6\xB0\xB4\xE5\xB9\xB3\xE5\x8E\x8B\xE5\x8A\x9B\xE6\xB5\x8B\xE8\xAF\x95",
	// 328 - DEBUG_BIPAPPRESSURE_IDX
	"\xE5\x8F\x8C\xE6\xB0\xB4\xE5\xB9\xB3\xE5\x8E\x8B\xE5\x8A\x9B\xE6\xB5\x8B\xE8\xAF\x95",
	// 329 - DEBUG_PWMON_IDX
	"PWM\xE5\xBC\x80",
	// 330 - DEBUG_PEMOFF_IDX
	"PWM\xE5\x85\xB3",
	// 331 - DEBUG_PWMADD_IDX
	"PWM\xE5\x8A\xA0",
	// 332 - DEBUG_PWMSUB_IDX
	"PWM\xE5\x87\x8F",
	// 333 - DEBUG_SINGLEPEXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 334 - DEBUG_OFFSET_IDX
	"\xE5\x81\x8F\xE7\xA7\xBB",
	// 335 - DEBUG_FLOWOFFSET_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE5\x81\x8F\xE7\xA7\xBB",
	// 336 - DEBUG_PRESSOFFSET_IDX
	"\xE5\x8E\x8B\xE5\x8A\x9B\xE5\x81\x8F\xE7\xA7\xBB",
	// 337 - DEBUG_ALLOFFSET_IDX
	"\xE6\x95\xB4\xE4\xBD\x93\xE5\x81\x8F\xE7\xA7\xBB",
	// 338 - DEBUG_F30OFFSET_IDX
	"30L/min\xE5\x81\x8F\xE7\xA7\xBB",
	// 339 - DEBUG_F80OFFSET_IDX
	"80L/min\xE5\x81\x8F\xE7\xA7\xBB",
	// 340 - DEBUG_F120OFFSET_IDX
	"120L/min\xE5\x81\x8F\xE7\xA7\xBB",
	// 341 - DEBUG_F140OFFSET_IDX
	"140L/min\xE5\x81\x8F\xE7\xA7\xBB",
	// 342 - DEBUG_P4OFFSET_IDX
	"4cmH2o\xE5\x81\x8F\xE7\xA7\xBB",
	// 343 - DEBUG_P10OFFSET_IDX
	"10cmH2o\xE5\x81\x8F\xE7\xA7\xBB",
	// 344 - DEBUG_P20OFFSET_IDX
	"20cmH2o\xE5\x81\x8F\xE7\xA7\xBB",
	// 345 - DEBUG_RETEST_IDX
	"\xE5\xA4\x8D\xE6\xB5\x8B",
	// 346 - DEBUG_SHIFT_IDX
	"\xE6\x8D\xA2\xE6\x8C\xA1",
	// 347 - DEBUG_RETESTPRESS_IDX
	"\xE8\xAF\xB7\xE8\xB0\x83\xE6\x95\xB4\xE5\x8E\x8B\xE5\x8A\x9B\xE6\x8E\xA7\xE5\x88\xB6\xE5\x8E\x8B\xE5\x8A\x9B\xE8\xBE\x93\xE5\x87\xBA",
	// 348 - DEBUG_MOREOPTION_IDX
	"\xE6\x9B\xB4\xE5\xA4\x9A""...",
	// 349 - DEBUG_AUTOPRESSCALI_IDX
	"\xE8\x87\xAA\xE5\x8A\xA8\xE5\x8E\x8B\xE5\x8A\x9B\xE5\xAE\x9A\xE6\xA0\x87",
	// 350 - DEBUG_LEAKVIEWSET_ONMASK_IDX
	"\xE6\xBC\x8F\xE6\xB0\x94\xE9\x87\x8F\xE6\x98\xBE\xE7\xA4\xBA"":  \xE5\x90\xAB\xE9\x9D\xA2\xE7\xBD\xA9",
	// 351 - DEBUG_LEAKVIEWSET_OFFMASK_IDX
	"\xE6\xBC\x8F\xE6\xB0\x94\xE9\x87\x8F\xE6\x98\xBE\xE7\xA4\xBA"":  \xE4\xB8\x8D\xE5\x90\xAB\xE9\x9D\xA2\xE7\xBD\xA9",
	// 352 - DEBUG_CHINESESERVER_IDX
	"\xE6\x9C\x8D\xE5\x8A\xA1\xE5\x99\xA8"":  Dev",
	// 353 - DEBUG_FOREIGNSERVER_IDX
	"\xE6\x9C\x8D\xE5\x8A\xA1\xE5\x99\xA8"":  Test",
	// 354 - DEBUG_BECSERVER_IDX
	"\xE6\x9C\x8D\xE5\x8A\xA1\xE5\x99\xA8"":  Stage",
	// 355 - DEBUG_FLOWSENSORTYPE_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE4\xBC\xA0\xE6\x84\x9F\xE5\x99\xA8\xEF\xBC\x9A",
	// 356 - BLANK_IDX
	" ",
};
