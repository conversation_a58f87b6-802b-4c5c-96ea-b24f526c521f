/*********************************************************************
*                                                                    *
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
*                                                                    *
**********************************************************************
*                                                                    *
* C-file generated by:                                               *
*                                                                    *
*        GUI_Builder for emWin version 5.32                          *
*        Compiled Oct  8 2015, 11:59:02                              *
*        (c) 2015 Segger Microcontroller GmbH & Co. KG               *
*                                                                    *
**********************************************************************
*                                                                    *
*        Internet: www.segger.com  Support: <EMAIL>       *
*                                                                    *
**********************************************************************
*/

// USER START (Optionally insert additional includes)
#include "StaterBar.h"
#include "Key.h"
#include "ConfigSave.h"
#include "GlobalVariable.h"
#include "Humidi.h"
#include "SignalCondition.h"
#include "SysSettingScreen.h"
#include "PipeDispose.h"
#include "GuiTask.h"
#include "SignalCondition.h"
#include "PerformanceTestWindow.h"
#include "stack.h"
#include "GlobalVariable.h"
#include "WifiUartModule.h"
// USER END

#include "DIALOG.h"

/*********************************************************************
*
*       Defines
*
**********************************************************************
*/
#define ID_WINDOW_0  (GUI_ID_USER + 0x00)
#define ID_TEXT_0  (GUI_ID_USER + 0x01)
#define ID_BUTTON_EXIT  (GUI_ID_USER + 0x02)

#define ID_BUTTON_1  (GUI_ID_USER + 0x03)
#define ID_BUTTON_2  (GUI_ID_USER + 0x04)
#define ID_BUTTON_3  (GUI_ID_USER + 0x05)
#define ID_BUTTON_4  (GUI_ID_USER + 0x06)
#define ID_BUTTON_5  (GUI_ID_USER + 0x07)
#define ID_BUTTON_6  (GUI_ID_USER + 0x08)

// USER START (Optionally insert additional defines)
static void ShowProcess(void);
WINDOWINFO g_DebugMoreOptionWInfo = {NULL, NULL, NULL, NULL, ShowProcess, NULL, 0, 0, DEBUGMOREOPTION_ID};

WM_HWIN g_DebugMoreOptionHwin = NULL;

// USER END

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/

// USER START (Optionally insert additional static data)
// USER END

/*********************************************************************
*
*       _aDialogCreate
*/
#define USR_BUTTON_NUM  (7)
#if (LCD_TYPE == LCD_5_TFT)
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] =
{
    { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 70, 800, 410, 0, 0x0, 0 },
    { TEXT_CreateIndirect, "Title", ID_TEXT_0, 0, 0, 800, 50, 0, 0x64, 0 },
    { BUTTON_CreateIndirect, "Exit", ID_BUTTON_EXIT, 550, 330, 200, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "OvervoltageProtection", ID_BUTTON_1, 50, 60, 200, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "AutoPressCali", ID_BUTTON_2, 300, 60, 200, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "CESwitch", ID_BUTTON_3, 550, 60, 200, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "LeakageViewSet", ID_BUTTON_4, 50, 130, 325, 40, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "ServerSelection", ID_BUTTON_5, 425, 130, 325, 40, 0, 0x0, 0 },
	{ BUTTON_CreateIndirect, "FlowCalib", ID_BUTTON_6, 50, 200, 200, 40, 0, 0x0, 0 },
    // USER START (Optionally insert additional widgets)
    // USER END
};
#else
#if (LCD_TYPE == LCD_28_TFT)
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] =
{
    { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 46, 240, 320 - 46, 0, 0x0, 0 },
    { TEXT_CreateIndirect, "Title", ID_TEXT_0, 0, 0, 240, 33, 0, 0x64, 0 },
    { BUTTON_CreateIndirect, "OvervoltageProtection", ID_BUTTON_1, 20, 44, 200, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "AutoPressCali", ID_BUTTON_2, 20, 77, 200, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "CESwitch", ID_BUTTON_3, 20, 110, 200, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "LeakageViewSet", ID_BUTTON_4, 20, 143, 200, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "ServerSelection", ID_BUTTON_5, 20, 176, 200, 26, 0, 0x0, 0 },
	{ BUTTON_CreateIndirect, "FlowCalib", ID_BUTTON_6, 20, 209, 200, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Exit", ID_BUTTON_EXIT, 20, 244, 200, 26, 0, 0x0, 0 },
    // USER START (Optionally insert additional widgets)
    // USER END
#else
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] =
{
    { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 46, 480, 320 - 46, 0, 0x0, 0 },
    { TEXT_CreateIndirect, "Title", ID_TEXT_0, 0, 0, 480, 33, 0, 0x64, 0 },
    { BUTTON_CreateIndirect, "Exit", ID_BUTTON_EXIT, 340, 226, 120, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "OvervoltageProtection", ID_BUTTON_1, 20, 47, 140, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "AutoPressCali", ID_BUTTON_2, 190, 47, 120, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "CESwitch", ID_BUTTON_3, 340, 47, 120, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "LeakageViewSet", ID_BUTTON_4, 20, 94, 205, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "ServerSelection", ID_BUTTON_5, 255, 94, 205, 26, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "FlowCalib", ID_BUTTON_6, 20, 141, 205, 26, 0, 0x0, 0 },

    // USER START (Optionally insert additional widgets)
    // USER END
#endif
};
#endif


/*********************************************************************
*
*       Static code
*
**********************************************************************
*/

// USER START (Optionally insert additional static code)
// USER END

/*********************************************************************
*
*       _cbDialog
*/
static void _cbDialog(WM_MESSAGE *pMsg)
{
    WM_HWIN hItem;
    int     NCode;
    int     Id;
    // USER START (Optionally insert additional variables)
    U8 i;
    char str[32];
    char strNumTmp[10];
    // USER END

    switch(pMsg->MsgId)
    {
        case WM_INIT_DIALOG:
            //
            // Initialization of 'Window'
            //
            hItem = pMsg->hWin;
            WINDOW_SetBkColor(hItem, GUI_MAKE_COLOR(0x00000000));
            //
            // Initialization of 'Title'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_0);
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
            TEXT_SetText(hItem, "Debug Mode V1.0");
            TEXT_SetTextAlign(hItem, GUI_TA_HCENTER | GUI_TA_VCENTER);
#if (LCD_TYPE == LCD_5_TFT)
            TEXT_SetFont(hItem, FONT_24);
#else
            TEXT_SetFont(hItem, FONT_18);
#endif

            // USER START (Optionally insert additional code for further widget initialization)
#if (LCD_TYPE == LCD_5_TFT)
            TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_0), FONT_24);
#else
            TEXT_SetFont(WM_GetDialogItem(pMsg->hWin, ID_TEXT_0), FONT_18);
#endif

            TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_0), GetMultiLanguageString(DEBUG_TITLE_IDX));

            //
            if(!g_ConfigSave.GetHardProtect())
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_1),
                                GetMultiLanguageString((ESTRINGIDX)DEBUG_HARD_PROTECT_OFF_IDX));
            }
            else
            {
                BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_1),
                                GetMultiLanguageString((ESTRINGIDX)DEBUG_HARD_PROTECT_ON_IDX));
            }

            switch(g_ConfigSave.GetMarketAreaType())
            {
                case MARKET_AREA_CHINA:
                    BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_3), GetMultiLanguageString((ESTRINGIDX)DEBUG_CHINA_SWITCH_IDX));
//                    UpdateCEString();
                    break;
                case MARKET_AREA_CE:
                    BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_3), GetMultiLanguageString((ESTRINGIDX)DEBUG_CE_SWITCH_IDX));
//                    UpdateCEString();
                    break;
                case MARKET_AREA_FDA:
                    BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_3), GetMultiLanguageString((ESTRINGIDX)DEBUG_FDA_SWITCH_IDX));
//                    UpdateFDAString();
                    g_ConfigSave.LoadLanguage();
                    break;
                default:
                    BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_3), GetMultiLanguageString((ESTRINGIDX)DEBUG_CHINA_SWITCH_IDX));
                    break;
            }

            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_2),
                            GetMultiLanguageString((ESTRINGIDX)(DEBUG_AUTOPRESSCALI_IDX)));

            //
            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_EXIT), GetMultiLanguageString((ESTRINGIDX)(DEBUG_EXIT_IDX)));

            //面罩漏气量
            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_4),
                            GetMultiLanguageString((ESTRINGIDX)(DEBUG_LEAKVIEWSET_ONMASK_IDX + g_ConfigSave.GetParameter(LEAKVIEWTYPE))));
            //服务器选择
            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_5),
                            GetMultiLanguageString((ESTRINGIDX)(DEBUG_CHINESESERVER_IDX + g_ConfigSave.GetParameter(SERVERTYPE))));
			
			BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_6),
                            GetMultiLanguageString((ESTRINGIDX)(DEBUG_FLOWCALI_IDX)));

            for(i = 0; i < USR_BUTTON_NUM; i++)
            {
#if (LCD_TYPE == LCD_5_TFT)
                BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_EXIT + i), FONT_24);
#else
                BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_EXIT + i), FONT_18);
#endif
                _SetButtonSkin(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_EXIT + i));
            }

            // USER END
            break;
        case WM_NOTIFY_PARENT:
            Id    = WM_GetId(pMsg->hWinSrc);
            NCode = pMsg->Data.v;
            switch(Id)
            {
                case ID_BUTTON_EXIT: // Notifications sent by 'Exit'
                    switch(NCode)
                    {
                        case WM_NOTIFICATION_CLICKED:
                            // USER START (Optionally insert code for reacting on notification message)
                            ShowDebugMoreOption(0);
                            // USER END
                            break;
                        case WM_NOTIFICATION_RELEASED:
                            // USER START (Optionally insert code for reacting on notification message)
                            // USER END
                            break;
                            // USER START (Optionally insert additional code for further notification handling)
                            // USER END
                    }
                    break;
                // USER START (Optionally insert additional code for further Ids)
                // USER END
                case ID_BUTTON_1:
                    switch(NCode)
                    {
                        case WM_NOTIFICATION_CLICKED:
                            // USER START (Optionally insert code for reacting on notification message)

                            g_ConfigSave.SetParameter(HARD_PROTECT_SWITCH, !g_ConfigSave.GetHardProtect());
                            if(g_ConfigSave.GetHardProtect())
                            {
                                BUTTON_SetText(WM_GetDialogItem(g_DebugMoreOptionHwin, ID_BUTTON_1),
                                                GetMultiLanguageString((ESTRINGIDX)DEBUG_HARD_PROTECT_ON_IDX));
                            }
                            else
                            {
                                BUTTON_SetText(WM_GetDialogItem(g_DebugMoreOptionHwin, ID_BUTTON_1),
                                                GetMultiLanguageString((ESTRINGIDX)DEBUG_HARD_PROTECT_OFF_IDX));
                            }

                            UpdateModeDutyCycle();
                            // USER END
                            break;
                        case WM_NOTIFICATION_RELEASED:
                            // USER START (Optionally insert code for reacting on notification message)
                            // USER END
                            break;
                            // USER START (Optionally insert additional code for further notification handling)
                            // USER END
                    }
                    break;
                case ID_BUTTON_2:
                    switch(NCode)
                    {
                        case WM_NOTIFICATION_CLICKED:
                            // USER START (Optionally insert code for reacting on notification message)
                            ShowAutoPresCali(1);
                            // USER END
                            break;
                        case WM_NOTIFICATION_RELEASED:
                            // USER START (Optionally insert code for reacting on notification message)
                            // USER END
                            break;
                            // USER START (Optionally insert additional code for further notification handling)
                            // USER END
                    }
                    break;
                case ID_BUTTON_3:
                    switch(NCode)
                    {
                        case WM_NOTIFICATION_CLICKED:
                            // USER START (Optionally insert code for reacting on notification message)
//                            if(g_pMachineConfig->ProNum < 2 && (g_pMachineConfig->DType != RF_20I_S3_X3)
//                                            && (g_pMachineConfig->DType != RF_30A_S7_X7) && (g_pMachineConfig->DType != RF_25T_H1_P1)
//                                            && (g_pMachineConfig->DType != RF_25P_H2_P2) && (g_pMachineConfig->DType != RF_25V_H3_P3)
//                                            && (g_pMachineConfig->DType != RF_30P_H6_P6) && (g_pMachineConfig->DType != RF_30S_H7_P7))
//                            {
//                                g_ConfigSave.SetParameter(MARKET_AREA_TYPE, (g_ConfigSave.GetMarketAreaType() + 1) % 3);
//                            }
//                            else
//                            {
                            g_ConfigSave.SetParameter(MARKET_AREA_TYPE, (g_ConfigSave.GetMarketAreaType() + 1) % 2);
//                            }
                            switch(g_ConfigSave.GetMarketAreaType())
                            {
                                case MARKET_AREA_CHINA:
                                    BUTTON_SetText(WM_GetDialogItem(g_DebugMoreOptionHwin, ID_BUTTON_3),
                                                    GetMultiLanguageString((ESTRINGIDX)DEBUG_CHINA_SWITCH_IDX));
//                                    UpdateCEString();
                                    update_CE_workmode();//更新工作模式
                                    break;
                                case MARKET_AREA_CE:
                                    BUTTON_SetText(WM_GetDialogItem(g_DebugMoreOptionHwin, ID_BUTTON_3),
                                                    GetMultiLanguageString((ESTRINGIDX)DEBUG_CE_SWITCH_IDX));
//                                    UpdateCEString();
                                    update_CE_workmode();//更新工作模式
                                    break;
                                case MARKET_AREA_FDA:
                                    BUTTON_SetText(WM_GetDialogItem(g_DebugMoreOptionHwin, ID_BUTTON_3),
                                                    GetMultiLanguageString((ESTRINGIDX)DEBUG_FDA_SWITCH_IDX));
//                                    UpdateFDAString();
                                    g_ConfigSave.LoadLanguage();
                                    update_FDA_workmode();//更新工作模式
                                    break;
                                default:
                                    BUTTON_SetText(WM_GetDialogItem(g_DebugMoreOptionHwin, ID_BUTTON_3),
                                                    GetMultiLanguageString((ESTRINGIDX)DEBUG_CHINA_SWITCH_IDX));
                                    break;
                            }
                            CalCntForJudgeAirwayObstruction();  //气道阻塞时长计算: FDA 8s; 其他: 5s

                            // USER END
                            break;
                        case WM_NOTIFICATION_RELEASED:
                            // USER START (Optionally insert code for reacting on notification message)
                            // USER END
                            break;
                            // USER START (Optionally insert additional code for further notification handling)
                            // USER END
                    }
                    break;
                case ID_BUTTON_4: //面罩漏气量显示开关
                    switch(NCode)
                    {
                        case WM_NOTIFICATION_CLICKED:
                            // USER START (Optionally insert code for reacting on notification message)
                            g_ConfigSave.SetParameter(LEAKVIEWTYPE, !g_ConfigSave.GetParameter(LEAKVIEWTYPE));
                            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_4),
                                            GetMultiLanguageString((ESTRINGIDX)(DEBUG_LEAKVIEWSET_ONMASK_IDX + g_ConfigSave.GetParameter(LEAKVIEWTYPE))));
                            // USER END
                            break;
                        case WM_NOTIFICATION_RELEASED:
                            // USER START (Optionally insert code for reacting on notification message)
                            // USER END
                            break;
                            // USER START (Optionally insert additional code for further notification handling)
                            // USER END
                    }
                    break;
                case ID_BUTTON_5: //服务器选择
                    switch(NCode)
                    {
                        case WM_NOTIFICATION_CLICKED:
                            // USER START (Optionally insert code for reacting on notification message)
							g_ConfigSave.SetParameter(SERVERTYPE, (g_ConfigSave.GetParameter(SERVERTYPE) + 1) % SERVER_IP_NUM);
                            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_5),
                                            GetMultiLanguageString((ESTRINGIDX)(DEBUG_CHINESESERVER_IDX + g_ConfigSave.GetParameter(SERVERTYPE))));                            // USER END
                            break;
                        case WM_NOTIFICATION_RELEASED:
                            // USER START (Optionally insert code for reacting on notification message)
                            // USER END
                            break;
                            // USER START (Optionally insert additional code for further notification handling)
                            // USER END
                    }
                    break;
                case ID_BUTTON_6:
                    switch(NCode)
                    {
                        case WM_NOTIFICATION_CLICKED:
                            // USER START (Optionally insert code for reacting on notification message)
                            ShowFlowCali(1);
                            // USER END
                            break;
                        case WM_NOTIFICATION_RELEASED:
                            // USER START (Optionally insert code for reacting on notification message)
                            // USER END
                            break;
                            // USER START (Optionally insert additional code for further notification handling)
                            // USER END
                    }
                    break;
            }
            break;
        // USER START (Optionally insert additional message handling)
        // USER END
        default:
            WM_DefaultProc(pMsg);
            break;
    }
}

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/
// USER START (Optionally insert additional public code)
void ShowDebugMoreOption(U8 Flag)
{
    if(Flag)
    {
        if(g_DebugMoreOptionHwin == NULL)
        {
            g_DebugMoreOptionHwin = GUI_CreateDialogBox(_aDialogCreate, GUI_COUNTOF(_aDialogCreate), _cbDialog, WM_HBKWIN, 0, 0);
        }
        ShowProcess();
        g_WindowDrv.PushWindow(&g_DebugMoreOptionWInfo);
    }
    else
    {
        WM_HideWindow(g_DebugMoreOptionHwin);
        g_WindowDrv.PopWindow(&g_DebugMoreOptionWInfo);
    }
}

static void ShowProcess(void)
{
    WM_SetFocus(WM_GetDialogItem(g_DebugMoreOptionHwin, ID_BUTTON_1));
    WM_ShowWindow(g_DebugMoreOptionHwin);
}
// USER END

/*************************** End of file ****************************/

