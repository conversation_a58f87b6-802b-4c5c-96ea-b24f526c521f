#include "crypto.h"
#include "cmox_crypto.h"
#include "cmox_ecc.h"
#include "cmox_hash.h"
#include "update.h"
#include <string.h>
#include <stdio.h>
#include "fatfs.h"

/* === Crypto Assets Stored In Flash === */
#define FW_VER_PUB_KEY_ADDR      0x080FF800
#define EXT_SERVICE_PUB_KEY_ADDR 0x080FF840
#define FW_AES_KEY_ADDR          0x080FF880
#define LOCAL_DATA_AES_KEY_ADDR  0x080FF8A0
#define DEVICE_PRIVATE_KEY_ADDR  0x080FF8C0
#define DEVICE_CERT_LEN_ADDR     0x080FF8E0
#define DEVICE_CERT_ADDR         0x080FF8E2

#define ECDSA_PUB_KEY_SIZE   64
#define ECDSA_PRIVATE_KEY    32
#define AES_KEY_SIZE         16
#define DEVICE_CERT_LEN_SIZE  2
#define HASH_BUFFER_SIZE     512  // 哈希计算的缓冲区大小

const uint8_t AAD[3] = { 0x41, 0x41, 0x44 };

/* Buffers */
static uint8_t Computed_Hash[CMOX_SHA256_SIZE]__attribute__((aligned(4)));
static uint8_t Working_Buffer[MAX_CIPHER_LENGTH]__attribute__((aligned(4)));
static uint8_t tmpSignature[64]; // TODO: Delete this

/* ECC Context */
static cmox_ecc_handle_t Ecc_Ctx __attribute__((aligned(4)));

//cmox_gcm_handle_t Gcm_Ctx;  // Global GCM context
cmox_cipher_handle_t *cipher_ctx;

size_t decrypt_data_chunk( eAESKeyType_t key_type, uint8_t *encrypted_data, uint8_t *decrypted_data, uint16_t chunk_length, uint8_t *iv, uint8_t first_chunk)
{
    static cmox_gcm_handle_t Gcm_Ctx;  // Static to persist state
    cmox_cipher_retval_t ret;
    size_t computed_size;
		const uint8_t * aes_key;
		
		if( AES_FW_KEY == key_type )
		{
			aes_key = ( const uint8_t * )FW_AES_KEY_ADDR;
		}
		else if( AES_LOCAL_DATA_KEY == key_type )
		{
			aes_key = (const uint8_t *)LOCAL_DATA_AES_KEY_ADDR;
		}
		else
		{
			return 0;
		}
		
    /* Initialize GCM context **only on first chunk** */
    if (first_chunk)
    {
        cipher_ctx = cmox_gcm_construct(&Gcm_Ctx, CMOX_AES_GCM_DEC);
        cmox_cipher_init(cipher_ctx);

        // ? Set encryption parameters (only once)
        cmox_cipher_setKey(cipher_ctx, aes_key, AES_KEY_SIZE);
        cmox_cipher_setIV(cipher_ctx, iv, IV_SIZE);  // ? Set IV only for the first chunk
        cmox_cipher_setTagLen(cipher_ctx, TAG_SIZE);
        cmox_cipher_setADLen(cipher_ctx, sizeof(AAD));
        cmox_cipher_appendAD(cipher_ctx, AAD, sizeof(AAD));
    }

    /* Decrypt the chunk */
		ret = cmox_cipher_append(cipher_ctx, encrypted_data, chunk_length, decrypted_data, &computed_size);
		if (ret != CMOX_CIPHER_SUCCESS)
		{
				return 0;  // Decryption failed
		}

    return computed_size;
}

uint8_t validate_tag( eAESKeyType_t key_type, uint8_t * tag)
{
	cmox_cipher_retval_t ret;
	uint32_t fault_check = CMOX_CIPHER_AUTH_FAIL;
	ret = cmox_cipher_verifyTag(cipher_ctx, tag, &fault_check);
	if (ret != CMOX_CIPHER_AUTH_SUCCESS || fault_check != CMOX_CIPHER_AUTH_SUCCESS)
	{
		return 0;  // Authentication failed - TODO: Create good Enum for this
	}
	return FILE_OK;
}

/* === Validate Firmware in Internal Flash (Verify Signature Only) === */
eFileStatus_t validate_flash_file(void)
{
    cmox_hash_retval_t hretval;
    cmox_ecc_retval_t retval;
    size_t computed_size;
    uint32_t fault_check = CMOX_ECC_AUTH_FAIL;

    uint8_t *firmware_ptr = (uint8_t *)FLASH_APP_ADDR;
		uint8_t *signature_ptr = (uint8_t *)FLASH_APP_ADDR + FILE_SIZE;
	
		memcpy(tmpSignature, &signature_ptr[0], SIGNATURE_SIZE);
		cmox_init_arg_t init_target = {CMOX_INIT_TARGET_AUTO, NULL};

		/* Initialize cryptographic library */
		if (cmox_initialize(&init_target) != CMOX_INIT_SUCCESS)
		{
			return FILE_VERIFICATION_ERROR;
		}	

    // Compute Hash
    hretval = cmox_hash_compute(
        CMOX_SHA256_ALGO,
        firmware_ptr, FILE_SIZE,
        Computed_Hash,
        CMOX_SHA256_SIZE,
        &computed_size
    );

    if (hretval != CMOX_HASH_SUCCESS || computed_size != CMOX_SHA256_SIZE) {
        return FILE_VERIFICATION_ERROR;
    }

		/* Construct a ECC context, specifying mathematics implementation and working buffer for later processing */
		cmox_ecc_construct(&Ecc_Ctx, CMOX_ECC256_MATH_FUNCS, Working_Buffer, sizeof(Working_Buffer));				
		
    // Verify Signature
    retval = cmox_ecdsa_verify(
        &Ecc_Ctx,
        CMOX_ECC_CURVE_SECP256R1,
        (const uint8_t *)FW_VER_PUB_KEY_ADDR, ECDSA_PUB_KEY_SIZE,
        Computed_Hash, CMOX_SHA256_SIZE,
        signature_ptr, SIGNATURE_SIZE,
        &fault_check
    );

    return (retval == CMOX_ECC_AUTH_SUCCESS && fault_check == CMOX_ECC_AUTH_SUCCESS) ? FILE_OK : FILE_VERIFICATION_ERROR;
}
    uint8_t encrypted_chunk[2048];  // 2KB加密数据块
    uint8_t decrypted_chunk[2048];  // 2KB解密数据块
/* === 验证文件内容的完整性和签名 (分块处理方式实现) === */
eFileStatus_t validate_file_content(FIL* fil, uint32_t fileSize)
{
    cmox_ecc_retval_t ecc_ret;
    uint32_t fault_check = CMOX_ECC_AUTH_FAIL;

    uint8_t signature[SIGNATURE_SIZE];  // 用于存储解密后的签名
    uint8_t iv[IV_SIZE];            // 初始化向量
    uint8_t tag[TAG_SIZE];          // AES-GCM认证标签
    uint32_t bytesRead, decryptedSize;
    uint32_t totalBytesRead = 0;
    uint8_t first_chunk = 1;
    uint8_t is_last_chunk = 0;
    FRESULT result;
    size_t computed_size;
    cmox_sha256_handle_t sha_handle;
    cmox_hash_handle_t *hash_handle;
    cmox_init_arg_t init_arg = {CMOX_INIT_TARGET_AUTO, NULL};
    
    /* 1. 初始化加密库 */
    if (cmox_initialize(&init_arg) != CMOX_INIT_SUCCESS)
    {
        return FILE_VERIFICATION_ERROR;
    }
    
    /* 2. 读取IV和TAG */
    result = f_lseek(fil, 0);
    if (result != FR_OK)
    {
        return FILE_VERIFICATION_ERROR;
    }
    
    result = f_read(fil, iv, IV_SIZE, &bytesRead);
    if (result != FR_OK || bytesRead != IV_SIZE)
    {
        return FILE_VERIFICATION_ERROR;
    }
    
    result = f_lseek(fil, fileSize - TAG_SIZE);
    if (result != FR_OK)
    {
        return FILE_VERIFICATION_ERROR;
    }
    
    result = f_read(fil, tag, TAG_SIZE, &bytesRead);
    if (result != FR_OK || bytesRead != TAG_SIZE)
    {
        return FILE_VERIFICATION_ERROR;
    }
    
    /* 3. 计算加密数据大小和初始化哈希 */
    uint32_t encryptedDataSize = fileSize - IV_SIZE - TAG_SIZE;  // 加密数据总大小（包括签名）
    
    hash_handle = cmox_sha256_construct(&sha_handle);
    if (hash_handle == NULL)
    {
        return FILE_VERIFICATION_ERROR;
    }
    
    if (cmox_hash_init(hash_handle) != CMOX_HASH_SUCCESS)
    {
        cmox_hash_cleanup(hash_handle);
        return FILE_VERIFICATION_ERROR;
    }
    
    /* 4. 在一次遍历中解密所有数据并计算哈希（固件部分） */
    result = f_lseek(fil, IV_SIZE);
    if (result != FR_OK)
    {
        cmox_hash_cleanup(hash_handle);
        return FILE_VERIFICATION_ERROR;
    }
    
    first_chunk = 1;
    totalBytesRead = 0;
    
    while (totalBytesRead < encryptedDataSize)
    {
        uint32_t bytesToRead = (encryptedDataSize - totalBytesRead > sizeof(encrypted_chunk)) ? 
                               sizeof(encrypted_chunk) : (encryptedDataSize - totalBytesRead);
        
        // 检查是否是最后一块
        is_last_chunk = (totalBytesRead + bytesToRead >= encryptedDataSize);
        
        result = f_read(fil, encrypted_chunk, bytesToRead, &bytesRead);
        if (result != FR_OK || bytesRead == 0)
        {
            cmox_hash_cleanup(hash_handle);
            return FILE_VERIFICATION_ERROR;
        }
        
        decryptedSize = decrypt_data_chunk(AES_FW_KEY, encrypted_chunk, decrypted_chunk, bytesRead, iv, first_chunk);
        if (decryptedSize == 0)
        {
            cmox_hash_cleanup(hash_handle);
            return FILE_VERIFICATION_ERROR;
        }
        
        if (is_last_chunk)
        {
            // 这是最后一块，包含签名
            // 1. 提取签名（最后64字节）
            memcpy(signature, decrypted_chunk + decryptedSize - SIGNATURE_SIZE, SIGNATURE_SIZE);
            
            // 2. 计算哈希时排除签名部分
            if (cmox_hash_append(hash_handle, decrypted_chunk, decryptedSize - SIGNATURE_SIZE) != CMOX_HASH_SUCCESS)
            {
                cmox_hash_cleanup(hash_handle);
                return FILE_VERIFICATION_ERROR;
            }
        }
        else
        {
            // 不是最后一块，计算整块的哈希
            if (cmox_hash_append(hash_handle, decrypted_chunk, decryptedSize) != CMOX_HASH_SUCCESS)
            {
                cmox_hash_cleanup(hash_handle);
                return FILE_VERIFICATION_ERROR;
            }
        }
        
        first_chunk = 0;
        totalBytesRead += bytesRead;
    }
    
    // 完成哈希计算
    if (cmox_hash_generateTag(hash_handle, Computed_Hash, &computed_size) != CMOX_HASH_SUCCESS ||
        computed_size != CMOX_SHA256_SIZE)
    {
        cmox_hash_cleanup(hash_handle);
        return FILE_VERIFICATION_ERROR;
    }
    
    cmox_hash_cleanup(hash_handle);
    
    /* 5. 验证签名 */
    cmox_ecc_construct(&Ecc_Ctx, CMOX_ECC256_MATH_FUNCS, Working_Buffer, sizeof(Working_Buffer));
    
    ecc_ret = cmox_ecdsa_verify(
        &Ecc_Ctx,
        CMOX_ECC_CURVE_SECP256R1,
        (const uint8_t *)FW_VER_PUB_KEY_ADDR,
        ECDSA_PUB_KEY_SIZE,
        Computed_Hash,
        CMOX_SHA256_SIZE,
        signature,
        SIGNATURE_SIZE,
        &fault_check
    );
    
    /* 6. 重置文件指针以便后续操作 */
    f_lseek(fil, IV_SIZE);
    
    return (ecc_ret == CMOX_ECC_AUTH_SUCCESS && fault_check == CMOX_ECC_AUTH_SUCCESS) ? 
             FILE_OK : FILE_VERIFICATION_ERROR;
}
