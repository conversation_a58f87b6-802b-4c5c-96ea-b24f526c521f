/*********************************************************************
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
**********************************************************************
*                                                                    *
*        (c) 1996 - 2017  SEGGER Microcontroller GmbH & Co. KG       *
*                                                                    *
*        Internet: www.segger.com    Support:  <EMAIL>    *
*                                                                    *
**********************************************************************

** emWin V5.44 - Graphical user interface for embedded applications **
All  Intellectual Property rights  in the Software belongs to  SEGGER.
emWin is protected by  international copyright laws.  Knowledge of the
source code may not be used to write a similar product.  This file may
only be used in accordance with the following terms:

The  software has  been licensed  to STMicroelectronics International
N.V. a Dutch company with a Swiss branch and its headquarters in Plan-
les-Ouates, Geneva, 39 Chemin du Champ des Filles, Switzerland for the
purposes of creating libraries for ARM Cortex-M-based 32-bit microcon_
troller products commercialized by Licensee only, sublicensed and dis_
tributed under the terms and conditions of the End User License Agree_
ment supplied by STMicroelectronics International N.V.
Full source code is available at: www.segger.com

We appreciate your understanding and fairness.
----------------------------------------------------------------------
File        : GUIConf.c
Purpose     : Display controller initialization
---------------------------END-OF-HEADER------------------------------
*/

/**
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2018 STMicroelectronics. 
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license SLA0044,
  * the "License"; You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *                      http://www.st.com/SLA0044
  *
  ******************************************************************************
  */

#include "GUI.h"
#include "DataType.h"

/*********************************************************************
*
*       Defines
*
**********************************************************************
*/
/*********************************************************************
*
*       External memory support
*/

//#if (ENABLE_SDRAM)
//#define USE_EXTMEMHEAP            (1)
//#else
#define USE_EXTMEMHEAP            (0)
//#endif

//
// Define the available number of bytes available for the GUI
//
#if USE_EXTMEMHEAP
  #define GUI_NUMBYTES   (1024 * 1024 * 8)    // x Byte
  #define GUI_EXTBUFADD  (0xD1000000 - GUI_NUMBYTES)//16MB SDRAM的最后8MB作为STemWIN动态内存 winbond测试  
  //#define GUI_EXTBUFADD  (0xD2000000 - GUI_NUMBYTES)//16MB SDRAM的最后8MB作为STemWIN动态内存 winbond测试
#else
  #define GUI_NUMBYTES  (1024 * 60)//(1024 * 64)    // x KByte
#endif

#define GUI_BLOCKSIZE 0x80

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/
#if USE_EXTMEMHEAP
  static U32 HeapMem[GUI_NUMBYTES / 4] __attribute__((at(GUI_EXTBUFADD)));  
#else
  //static U32 extMem[GUI_NUMBYTES / 4] __attribute__((section(".ARM.__at_0x10000000")));
  static U32 extMem[GUI_NUMBYTES / 4] __attribute__((section(".ccm_area")));
#endif

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/

/*********************************************************************
*
*       Get_ExtMemHeap
*
* Purpose:
*   Allocate heap from external memory
*/
#if USE_EXTMEMHEAP
U32* Get_ExtMemHeap (void)
{
  return HeapMem;
}
#endif

/*********************************************************************
*
*       GUI_X_Config
*
* Purpose:
*   Called during the initialization process in order to set up the
*   available memory for the GUI.
*/
void GUI_X_Config(void)
{    
#if USE_EXTMEMHEAP    
  GUI_ALLOC_AssignMemory(HeapMem, GUI_NUMBYTES);
	GUI_ALLOC_SetAvBlockSize(GUI_BLOCKSIZE);
#else	
  GUI_ALLOC_AssignMemory(extMem, GUI_NUMBYTES);	
	GUI_ALLOC_SetAvBlockSize(GUI_BLOCKSIZE);
#endif
}


/*************************** End of file ****************************/
