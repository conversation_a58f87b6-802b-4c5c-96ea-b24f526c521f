#ifndef __KEY_H__
#define __KEY_H__

#include "DataType.h"
#include "main.h"
#include "QueueBuffer.h"

#define KEY_NUMBER  3


#define SHORT_PRESS_TIME   20    //20                    // short press the keys time 20ms
#define LONG_PRESS_TIME    1000   //1000: 1s    //2200                  2.2s
//#define LONG_SHUTTLE_PRESS_TIME    2200//2.2s--长按飞梭解锁

#define LEFT_KEY_PIN 	HAL_GPIO_ReadPin(LEFT_KEY_GPIO_Port, LEFT_KEY_Pin)
#define RIGHT_KEY_PIN 	HAL_GPIO_ReadPin(RIGHT_KEY_GPIO_Port, RIGHT_KEY_Pin)

#define KEY_POWER_VALUE		0x01
#define KEY_ALARM_VALUE		0x02
#define KEY_ENTER_VALUE		0x03
#define KEY_LEFT_VALUE		0x04
#define KEY_RIGHT_VALUE		0x05

extern U8 g_sU8CanEnterDebugFlag;    //是否可进入Debug界面标志，仅开机第一次操作可进入Debug界面
U8 CheckDebugFileToDebug(void);

void AppTaskScanKey(void *p_arg);

U8 CheckDebugKey(void);

extern OS_TCB   AppTaskKeyTCB;
extern CPU_STK  AppTaskKeyStk[APP_CFG_TASK_KEY_STK_SIZE];
extern U8 g_KeyValue;
extern U16 g_KeyLongPressed;

extern U8* PopKeyFromQueue(void);
extern void PushKeyToQueue(U8 key);

extern void Set_KeyLongPressed(U16 Value);
extern U16 Get_KeyLongPressed(void);
extern void clear_key_long_pressed(void);

void ResetCanEnterDebug(void);
U8 GetCanPowerOnStatus(U8 IsKeyFlag);

extern CQueueBuffer<U8, 10> g_KeyQueueBuf;

#endif

