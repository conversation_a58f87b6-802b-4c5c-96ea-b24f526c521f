#include "StartupInterface.h"
#include "Led.h"
#include <stdio.h>
#include "ConfigSave.h"
#include "GlobalVariable.h"
#include "StaterBar.h"
#include "SelfTest.h"
#include "GuiTask.h"
#include "SelfTest.h"
#include "AppBMP.h"
#include "SignalCondition.h"
#include "sdp800.h"

extern volatile U32 g_StartUpStatus;
extern BmpSourceInfo gBmpSourceInfo[BMP_SOURCE_MAX];

void DisplayLogoVersion()
{
    U16 sU16BmpIdx = BMP_RESFREE;
    uint32_t u32CheckErrorType = NONE_ERROR;//,u32CheckErrorTypeTmp,u32BuffIndex = 0;
	//uint8_t u8ErrNumber = 0;
    GUI_RECT rect, checkrect;	
    char str[256] = {0};
    U16 sU16ProcessBarWidth;
    U16 sU16StartupTicket = 0;
    WM_SelectWindow(WM_HBKWIN);    
    
    g_StartUpStatus = 16;
	#if (LCD_TYPE == LCD_28_TFT)
    	GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
	#else
		GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
	#endif
    GUI_FillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT); 
#if (LCD_TYPE == LCD_5_TFT)
    rect.x0 = 150;
    rect.y0 = 330;
    rect.x1 = 650;
    rect.y1 = 346;

    checkrect.x0 = 0;
    checkrect.y0 = 370;
    checkrect.x1 = 799;
    checkrect.y1 = 400;
    GUI_SetFont(FONT_24);

#elif (LCD_TYPE == LCD_35_TFT)
    rect.x0 = 90;
    rect.y0 = 220;
    rect.x1 = 408;
    rect.y1 = 230;

    checkrect.x0 = 0;
    checkrect.y0 = 246;
    checkrect.x1 = 480 - 1;
    checkrect.y1 = 266;
    GUI_SetFont(FONT_18);
#elif (LCD_TYPE == LCD_28_TFT)
    rect.x0 = 30;
    rect.y0 = 220;
    rect.x1 = 210;
    rect.y1 = 230;

    checkrect.x0 = 0;
    checkrect.y0 = 266;
    checkrect.x1 = 240 - 1;
    checkrect.y1 = 306;
    GUI_SetFont(FONT_18);
#endif    
//绘制LOGO
    
#if (LCD_TYPE == LCD_5_TFT)   
    DrawBMP(sU16BmpIdx, (SCREEN_WIDTH - gBmpSourceInfo[sU16BmpIdx].xSize) / 2, rect.y0 - gBmpSourceInfo[sU16BmpIdx].ySize - 10);//logo底部到进度条的距离=10
#else
	#if (LCD_TYPE == LCD_28_TFT)
        DrawBMP(sU16BmpIdx, 45, 100);  
	#else
        const MACHINECONFIG* pMachineConfig;
        pMachineConfig = g_ConfigSave.GetMachineConfig();
        if (pMachineConfig->ProNum == 1)
        {
            sU16BmpIdx = BMP_RESFREE;
        }
        else
        {
            sU16BmpIdx = BMP_RESHOPE;
        }

        DrawBMP(sU16BmpIdx, (SCREEN_WIDTH - gBmpSourceInfo[sU16BmpIdx].xSize) / 2, rect.y0 - gBmpSourceInfo[sU16BmpIdx].ySize - 10);//logo底部到进度条的距离=10
	#endif 
#endif    
	GUI_SetColor(GUI_WHITE);
    
//设置亮度        
    GetAutoBrightNess();
    ResetAutoBalckScreenTime();
    g_StartUpStatus = 17;

    sU16ProcessBarWidth = rect.x1 - rect.x0;

    GUI_SetColor(DEFAULT_MENU_BACKCOLOR);
#if (LCD_TYPE == LCD_28_TFT)
    GUI_AA_FillRoundedRect(rect.x0, rect.y0, rect.x1, rect.y1,5);
       
    GUI_SetTextMode(GUI_TM_TRANS);
#else
	GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
#endif
	GUI_SetColor(GUI_WHITE);  
    GUI_DispStringInRect(GetMultiLanguageString(CHECKING_IDX), &checkrect, GUI_TA_HCENTER | GUI_TA_VCENTER);    
    
	//u8ErrNumber = CalErrNumber(u32CheckErrorType);
	GUI_SetTextMode(GUI_TM_TRANS);

	GUI_SetColor(DEFAULT_MENU_BACKCOLOR);
#if (LCD_TYPE == LCD_28_TFT)
	GUI_AA_FillRoundedRect(rect.x0, rect.y0, rect.x1, rect.y1,5);
	GUI_SetTextMode(GUI_TM_TRANS);

	GUI_SetColor(GREEN);
#else
	GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
	GUI_SetTextMode(GUI_TM_TRANS);

	GUI_SetColor(GUI_MAKE_COLOR(0xFF2080C0));
#endif
	ReadPowerTime();
    while (sU16StartupTicket < 3000)       //3s启动
    {
#if (ADC3_SAMPLE_STYLE == 0)
//        ADC3CodeUpdate();
#endif
        sU16StartupTicket += 100;
        rect.x1 = rect.x0 + sU16ProcessBarWidth * sU16StartupTicket / 3000;
		#if (LCD_TYPE == LCD_28_TFT)
	        GUI_SetColor(GREEN);
	        GUI_AA_FillRoundedRect(rect.x0, rect.y0, rect.x1, rect.y1,5);
		#else
			GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
		#endif
        HAL_Delay(100);
//#if (ENABLE_PERFORMANCE_TEST == 1)
//        if (sU16StartupTicket == 1000)
//        {
//            HAL_GPIO_WritePin(YLCGQ_CLE_GPIO_Port, YLCGQ_CLE_Pin, GPIO_PIN_RESET);
//            HAL_GPIO_WritePin(POWER_24_GPIO_Port, POWER_24_Pin, GPIO_PIN_SET);
//        }
//        else 
//#endif
        if (sU16StartupTicket ==  2500)
        {
            u32CheckErrorType = CheckSelfTest();
            if (u32CheckErrorType)
            {
                memset(str,0,sizeof(str));
				#if (LCD_TYPE == LCD_28_TFT)					
                	GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
				#else
					GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
				#endif
                GUI_FillRect(checkrect.x0, checkrect.y0, checkrect.x1, checkrect.y1);                
                GUI_SetColor(GUI_WHITE);
                if ((u32CheckErrorType & SDRAM_ERROR) == SDRAM_ERROR)
                {
                    sprintf(str, "%s ", GetMultiLanguageString(SDRAM_ERROR_IDX));						
                }
                if ((u32CheckErrorType & MOTOR_ERROR) == MOTOR_ERROR)
                {
                    sprintf(str, "%s %s", str, GetMultiLanguageString(MOTOR_ERROR_IDX));						 
                }
                if ((u32CheckErrorType & SPI_FLASH_ERROR) == SPI_FLASH_ERROR)
                {
                    sprintf(str, "%s %s", str, GetMultiLanguageString(SPI_FLASH_ERROR_IDX));						 
                }
                if ((u32CheckErrorType & HUMI_TEMP_SENSOR_ERROR) == HUMI_TEMP_SENSOR_ERROR)
                {
                    sprintf(str, "%s %s", str, GetMultiLanguageString(HUMI_TEMP_SENSOR_ERROR_IDX));						 
                }
                if ((u32CheckErrorType & FLOW_SENSOR_ERROR) == FLOW_SENSOR_ERROR)
                {
                    sprintf(str, "%s %s", str, GetMultiLanguageString(FLOW_SENSOR_ERROR_IDX));
                }
                if ((u32CheckErrorType & EEPROM_ERROR) == EEPROM_ERROR)
                {
                    sprintf(str, "%s %s", str, GetMultiLanguageString(EEPROM_ERROR_IDX));						 
                }
                if ((u32CheckErrorType & RTC_ERROR) == RTC_ERROR)
                {
                    sprintf(str, "%s %s", str, GetMultiLanguageString(RTC_ERROR_IDX));						  
                }
                if (u32CheckErrorType & YLCGQ_REF_ERROR)    
                {
                    sprintf(str, "%s %s", str, GetMultiLanguageString(YLCGQ_REF_ERROR_IDX));						  
                }
                if (u32CheckErrorType & MOTOR_POWER_24V_ERROR)    
                {
                    sprintf(str, "%s %s", str, GetMultiLanguageString(MOTOR_POWER_24V_ERROR_IDX));						  
                }

                sprintf(str, "%s %s", str, GetMultiLanguageString(CHECKERROR_IDX));
				
				#if (LCD_TYPE == LCD_28_TFT)	       
	                GUI_DispStringInRectWrap(str, &checkrect, GUI_TA_HCENTER | GUI_TA_VCENTER,GUI_WRAPMODE_WORD);   
	                GUI_SetColor(FONTCOLOR_DEFAULT);
				#else
					GUI_DispStringInRect(str, &checkrect, GUI_TA_HCENTER | GUI_TA_VCENTER);   
                	GUI_SetColor(GUI_MAKE_COLOR(0xFF2080C0));
				#endif
            }
        }
    }
    if (u32CheckErrorType == 0)
    {
		#if (LCD_TYPE == LCD_28_TFT)
        	GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
		#else
			GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
		#endif
        GUI_FillRect(checkrect.x0, checkrect.y0, checkrect.x1, checkrect.y1);    
        GUI_SetColor(GUI_WHITE);
        sprintf(str, "%s", GetMultiLanguageString(CHECKOK_IDX));
        GUI_DispStringInRect(str, &checkrect, GUI_TA_HCENTER | GUI_TA_VCENTER);
	}
    else if (u32CheckErrorType & (FLOW_SENSOR_ERROR | HUMI_TEMP_SENSOR_ERROR | SPI_FLASH_ERROR | EEPROM_ERROR | YLCGQ_REF_ERROR | MOTOR_POWER_24V_ERROR))       //自检失败时，不能进入主界面
    {
        while (1)
        {
            ;
        }
    }
	if(GetSensorType() == ADP800 || GetSensorType() == SDP810)
	{
		g_I2cRWByteInterval = 20;
	}
    HAL_Delay(1000);
}


