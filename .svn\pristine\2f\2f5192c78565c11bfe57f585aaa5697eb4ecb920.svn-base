#include "SysSettingScreen.h"
#include "Led.h"
#include <stdio.h>
#include "Key.h"
#include "ConfigSave.h"
#include "MultiLanguage.h"
#include "GlobalVariable.h"
#include "bsp_rtc_ds1302.h"
#include "StaterBar.h"
#include "TaskManager.h"
#include "CalcDateTime.h"
#include "PipeDispose.h"
#include "AppBMP.h"
#include "WifiUartModule.h"
#include "EdfDataSave.h"
#include "delay/delay.h"
#include "ManagerSetScreen.h"
#include "ValueToStr.h"

void RepaintSysSettingScreen(void);
static void SysSettingScreenProcess(U8 Key);

WINDOWINFO g_SysSettingScreenWInfo = {NULL, SysSettingScreenProcess, NULL, RepaintSysSettingScreen, NULL, NULL, 0, 0, SYSSETTING_ID};

RTC_TimeTypeDef1 g_SysSettingRTCTime;
RTC_TimeTypeDef1 g_OldSysSettingRTCTime;

char g_sI8SysMenuDisplayDateStr[16] = {0};
char g_sI8SysMenuDisplayTimeStr[10] = {0};

U8 g_U8WifiConnectStatus = 0;
#define ADMIN_OPERATE_TIMES 5000
#define ADMIN_OPERATE_OVER_TIMES 6000
static uint16_t s_hEnterAdminCnt = 0;
static uint32_t s_wEnterAdminTick = 0;

#if (LCD_TYPE == LCD_28_TFT)
static ESTRINGIDX IdType_Sys[][3] = {{TUBESELECTION_IDX, MASKSETTING_IDX, MASKTEST_IDX}
                                    ,{ECOSETTING_IDX,    DATE_IDX,        TIME_IDX}
                                    ,{BACKLIGHT_IDX,     BRIGHTNESS_IDX,  KNOBTONE_IDX}
                                    ,{LANGUAGE_IDX,      P_UINT_IDX,      WIFI_SET_IDX}
                                    ,{WIFI_SELECT_IDX,   RESET_IDX,       SOFTWAREVERSION_IDX}
                                    ,{SN_IDX,            BLANK_IDX,       BLANK_IDX}};

static ESTRINGIDX IdType_NoWifi[][3] = {{LANGUAGE_IDX,        P_UINT_IDX, RESET_IDX}
                                      , {SOFTWAREVERSION_IDX, SN_IDX,     BLANK_IDX}};
static ESTRINGIDX IdType_IsWifi[][3] = {{RESET_IDX,       SOFTWAREVERSION_IDX, SN_IDX}
                                       ,{BLANK_IDX,       BLANK_IDX,           BLANK_IDX}
                                       ,{WIFI_SELECT_IDX, RESET_IDX,           SOFTWAREVERSION_IDX}
                                       ,{SN_IDX,          BLANK_IDX,           BLANK_IDX}};
#else
static ESTRINGIDX IdType_Sys[][5] = {{TUBESELECTION_IDX, MASKSETTING_IDX, MASKTEST_IDX,    ECOSETTING_IDX, DATE_IDX}
                                    ,{TIME_IDX,          BACKLIGHT_IDX,   BRIGHTNESS_IDX,  KNOBTONE_IDX,   LANGUAGE_IDX}
                                    ,{P_UINT_IDX,        WIFI_SET_IDX,    WIFI_SELECT_IDX, RESET_IDX,      SOFTWAREVERSION_IDX}
                                    ,{SN_IDX,            BLANK_IDX,       BLANK_IDX,       BLANK_IDX,      BLANK_IDX}};

static ESTRINGIDX IdType_NoWifi[5] = {P_UINT_IDX, RESET_IDX, SOFTWAREVERSION_IDX, SN_IDX, BLANK_IDX};
static ESTRINGIDX IdType_IsWifi[][5] = {{P_UINT_IDX, WIFI_SET_IDX, RESET_IDX,       SOFTWAREVERSION_IDX, SN_IDX}
                                       ,{P_UINT_IDX, WIFI_SET_IDX, WIFI_SELECT_IDX, RESET_IDX,           SOFTWAREVERSION_IDX}
                                       ,{SN_IDX,     BLANK_IDX,    BLANK_IDX,       BLANK_IDX,           BLANK_IDX}};
static ESTRINGIDX IdType_SysTubeType[] = {TUBE_15MM_IDX, TUBE_19MM_IDX, TUBE_HEATING_IDX};                                       
#endif

//出厂默认：wifi:关0；加湿器档位：0；屏幕亮度：3；自动息屏：600s; 预热开关：关
uint16_t g_sU16ECOStandardModeValue[] = {0, 0, 3, 600, 0};

static uint8_t g_sU8DateSettingFocus = 0;
static uint8_t g_sU8OldDateSettingFocus = 0;

static uint8_t g_sU8TimeSettingFocus = 0;
static uint8_t g_sU8OldTimeSettingFocus = 0;
                                          
static uint8_t g_sU8CurEditState_SysSetting = FALSE;
static uint8_t g_sU8EnterDateEditState = FALSE;
static uint8_t g_sU8EnterTimeEditState = FALSE;
                                          
static uint8_t g_sU8CurEditState_DateSetting = FALSE;
static uint8_t g_sU8CurEditState_TimeSetting = FALSE;

//系统参数设置-系统还原、面罩佩戴测试、日期、时间、wifi设置、进行wifi密码设置时的标志位
//g_U8CurEditState_ForPowerON = True时，不能开机
uint8_t g_U8CurEditState_ForPowerON = FALSE;

//系统参数设置-在调节亮度时，不进行根据环境光自动调节亮度，除非当前值为AUTO
uint8_t g_U8CurEditState_ForAutoBlightNess = FALSE;
uint8_t g_U8TmpBrightNessValue = 0;

uint8_t g_U8LastWifiSwitch = 0; //记录系统还原之前wifi设置的开关状态
static uint8_t g_U8LastPipeType = 0;//记录上一次的管路类型

static void ChangeFocus_Date(void);
static void ChangeFocus_Time(void);
static void EditDateTimeValue(void);
static void DrawingOptionsDateTime(uint8_t IsLeft);

static void ChangeFocusSysSetting_Right(void);
static void ChangeFocusSysSetting_Left(void);
static void DrawSysSettingScreen(uint8_t u8Page, uint8_t u8IsFirst);

static void RefreshSysSettingScreen_Left(void);
static void RefreshSysSettingScreen_Right(void);
static void OnEnterClick_SysSetting(void);

static void DrawOptionSysSettingKeyLeft(void);
static void DrawOptionSysSettingKeyRight(void);

#if (LCD_TYPE == LCD_28_TFT)
static void DrawDateTime(int x0, int y0, ESTRINGIDX Idx, uint32_t U32FontColor, const GUI_FONT * pNewFont1, const GUI_FONT * pNewFont2, uint8_t u8EditState ,uint8_t u8DISP);
#else
static void DrawDateTime(int x0, int y0, ESTRINGIDX Idx, uint32_t U32FontColor, const GUI_FONT * pNewFont1, const GUI_FONT * pNewFont2, uint8_t u8EditState);
#endif
static uint8_t CalEachMonthDays(void);
static void UpdateConsumTime(RTC_TimeTypeDef1 Rtc_Time,bool bChangeFlag);
void EditDateTimeEdf(void);


static void SysSettingScreenProcess(U8 Key)
{   
	//5秒内按5次过程中，触发其他条目则清零按下次数
	if(Key != GUI_KEY_ENTER )
	{
		s_wEnterAdminTick = tick_get() - ADMIN_OPERATE_OVER_TIMES;
	}
	switch (Key)
	{
        case GUI_KEY_BACKTAB:            
            if (g_sU8CurEditState_SysSetting == FALSE 
                && g_sU8EnterDateEditState == FALSE
                && g_sU8EnterTimeEditState == FALSE)
            {
                g_SysSettingScreenWInfo.U8CurFocusID = g_SysSettingScreenWInfo.U8CurFocusID <= 0 
                                             ? (g_U8SysSettingMaxFocusNum - 1) 
                                             : (g_SysSettingScreenWInfo.U8CurFocusID - 1);                
                ChangeFocusSysSetting_Left(); 
            }
            else if (g_sU8EnterDateEditState == TRUE)
            {
                if (g_sU8CurEditState_DateSetting == FALSE)
                {
                    g_sU8DateSettingFocus = g_sU8DateSettingFocus <= 0 
                                            ? (MAX_FOCUS_SYSDATE - 1) 
                                            : (g_sU8DateSettingFocus - 1);
                    ChangeFocus_Date();
                }
                else
                    DrawingOptionsDateTime(TRUE);
            }
            else if (g_sU8EnterTimeEditState == TRUE)
            {
                if (g_sU8CurEditState_TimeSetting == FALSE)
                {
                    g_sU8TimeSettingFocus = g_sU8TimeSettingFocus <= 0 
                                            ? (MAX_FOCUS_SYSTIME - 1) 
                                            : (g_sU8TimeSettingFocus - 1);
                    ChangeFocus_Time();
                }
                else
                   DrawingOptionsDateTime(TRUE); 
            }
            else if (g_sU8EnterDateEditState == TRUE && g_sU8CurEditState_DateSetting == TRUE)
            {
                DrawingOptionsDateTime(TRUE);
            }
            else
                DrawOptionSysSettingKeyLeft();   
            break;
        case GUI_KEY_TAB:
            if (g_sU8CurEditState_SysSetting == FALSE 
                && g_sU8EnterDateEditState == FALSE
                && g_sU8EnterTimeEditState == FALSE)
            {
                g_SysSettingScreenWInfo.U8CurFocusID = g_SysSettingScreenWInfo.U8CurFocusID >= (g_U8SysSettingMaxFocusNum - 1) 
                                             ? 0
                                             : (g_SysSettingScreenWInfo.U8CurFocusID + 1);
                ChangeFocusSysSetting_Right();
            }
            else if (g_sU8EnterDateEditState == TRUE)
            {
                if (g_sU8CurEditState_DateSetting == FALSE)
                {
                    g_sU8DateSettingFocus = g_sU8DateSettingFocus >= (MAX_FOCUS_SYSDATE - 1) 
                                            ? 0
                                            : (g_sU8DateSettingFocus + 1);
                    ChangeFocus_Date();
                }
                else
                    DrawingOptionsDateTime(FALSE);
            }
            else if (g_sU8EnterTimeEditState == TRUE)
            {
                if (g_sU8CurEditState_TimeSetting == FALSE)
                {
                    g_sU8TimeSettingFocus = g_sU8TimeSettingFocus >= (MAX_FOCUS_SYSTIME - 1) 
                                            ? 0
                                            : (g_sU8TimeSettingFocus + 1);
                    ChangeFocus_Time();
                }
                else
                    DrawingOptionsDateTime(FALSE);
            }
            else
                DrawOptionSysSettingKeyRight();
            break;
        case GUI_KEY_ENTER:              
        case GUI_KEY_F1:
            OnEnterClick_SysSetting();            
            break;  
        default:
            break;
    }
}

/*********************************************************************
*   ScanningWifiCalSysSettingMaxFocus
*   输入参数：无
*   返回参数：无
*   函数描述：检测是否有Wifi模块，并计算"系统设置"界面的最大焦点号
*********************************************************************/
void ScanningWifiCalSysSettingMaxFocus(void)
{
    uint8_t i = 0, j = 0;

    if(GetWifiFlag())
    {
        if(g_ConfigSave.GetParameter(ECOTYPE) == SYS_PARA_ECOMODE_ECO_1)
        {
            g_ConfigSave.SetParameter(WIFI_SWITCH, 0, 0);  //关闭wifi
        }
        g_U8SysSettingMaxFocusNum = g_ConfigSave.GetParameter(WIFI_SWITCH)  
                    ? MAX_FOCUS_NUM_SYSSETTING_WIFI 
                    : MAX_FOCUS_NUM_SYSSETTING_OFFWIFI; 
        #if (LCD_TYPE == LCD_28_TFT)
	        for(i = 0; i < 3; i++)
	        {
	            IdType_Sys[4][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH)*2][i];        
	            IdType_Sys[5][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH)*2 + 1][i];
	        }
		#else
        if (g_ConfigSave.GetParameter(WIFI_SWITCH))
        {
			for(i = 0; i < 5; i++)
            {
            	IdType_Sys[2][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH)][i];
                IdType_Sys[3][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH) + 1][i];
            }
        }
        else
        {
			for(i = 0; i < 5; i++)
            	IdType_Sys[2][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH)][i];
        }
		#endif
    }
    else
    {
        g_U8SysSettingMaxFocusNum = MAX_FOCUS_NUM_SYSSETTING_NOWIFI;
		#if (LCD_TYPE == LCD_28_TFT)
	        for (j = 0; j < 2; j++)
            {
                for(i = 0; i < 3; i++)
                    IdType_Sys[3 + j][i] = IdType_NoWifi[j][i];
            }
		#else
			for(i = 0; i < 5; i++)
	            IdType_Sys[2][i] = IdType_NoWifi[i];
		#endif
    }
}

void ScanningWifiConnectedStatus(void)
{    
    if (GetWifiFlag())
    {
        //从wifi选择界面输入密码后返回系统设置界面，飞梭旋转至第三页，刷新"wifi设置选项的wifi名称和连接状态"
        strItem[1] = g_ConfigSave.GetWifiConfig()->SSID;
        if (strlen(g_ConfigSave.GetWifiConfig()->SSID) > 0)
        {
//            strItem[2] = g_WifiUartModule.GetConnectStatus() ? GetMultiLanguageString(WIFI_CONNECTED_IDX) : GetMultiLanguageString(WIFI_DISCONNECTED_IDX);
            #if (LCD_TYPE == LCD_5_TFT)
                if (g_SysSettingScreenWInfo.U8CurFocusID == g_SysSettingScreenWInfo.U8CurFocusID / 5 * 5 + 2 % 5)
                    SetUiItemText(74
                                  , 3 + (2 % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Sys[2][2 % 5]
                                  , g_sU8CurEditState_SysSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                  , FONT_32
                                  , g_sU8CurEditState_SysSetting ? FONT_38 : FONT_32
                                  , g_sU8CurEditState_SysSetting);
                else
                    SetUiItemText(74
                                  , 3 + (2 % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                  , IdType_Sys[2][2 % 5]
                                  , GUI_WHITE
                                  , FONT_32
                                  , FONT_32
                                  , FALSE);
            #else
                    #if (LCD_TYPE == LCD_28_TFT)
                    if (g_SysSettingScreenWInfo.U8CurFocusID == g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE * MAX_PAGE_LINE + 1 % MAX_PAGE_LINE)
                        SetUiItemText(15
                                      , 28 + (1 % MAX_PAGE_LINE) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[3][1 % MAX_PAGE_LINE]
                                      , g_sU8CurEditState_SysSetting ? DEFAULT_MENU_FOCUSCOLOR : GUI_WHITE
                                      , FONT_18
                                      , g_sU8CurEditState_SysSetting ? FONT_24 : FONT_18
                                      , g_sU8CurEditState_SysSetting,g_sU8CurEditState_SysSetting);
                    else
                        SetUiItemText(15
                                      , 28 + (1 % MAX_PAGE_LINE) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[3][1 % MAX_PAGE_LINE]
                                      , GUI_WHITE
                                      , FONT_18
                                      , FONT_18
                                      , FALSE,FALSE);
                    #else
                    if (g_SysSettingScreenWInfo.U8CurFocusID == g_SysSettingScreenWInfo.U8CurFocusID / 5 * 5 + 2 % 5)
                        SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                      , 2 + (2 % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[2][2 % 5]
                                      , g_sU8CurEditState_SysSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                      , FONT_24
                                      , g_sU8CurEditState_SysSetting ? FONT_32 : FONT_24
                                      , g_sU8CurEditState_SysSetting);
                    else
                        SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                      , 2 + (2 % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[2][2 % 5]
                                      , GUI_WHITE
                                      , FONT_24
                                      , FONT_24
                                      , FALSE);
                    #endif
            #endif
//            if (g_U8ChangeParaOnEditState == FALSE)
                SetUiItemValueAndUnitsText(IdType_Sys[2][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE], g_U8ChangeParaOnEditState);
        }
    }
}

static RTC_TimeTypeDef1 RTCTime = {0};
static RTC_TimeTypeDef1 RTCOldTime = {0};
static uint8_t bEditDateTimeFlag = FALSE;

/**
  * @brief  未更新EDF则更新EDF数据
  * @param  无
  * @retval 无
  */
void UpdateEdfData(void)
{
    if(bEditDateTimeFlag == TRUE)
    {
        bEditDateTimeFlag = FALSE;
        g_EdfDataSave.AcrossDayEdfDataDispose(RTCOldTime, RTCTime, 1);
    }
}

/**
  * @brief  修改时间10秒后还未EDF更新则更新
  * @param  无
  * @retval 无
  */
void UpdateEdfDataAfterTenSecond(void)
{
    static uint8_t bEdfUpdateTimeUp = 0;
    
    if(bEditDateTimeFlag == FALSE)
    {
        bEdfUpdateTimeUp = 0;
        return ;
    }
    if(++ bEdfUpdateTimeUp >= 10)
    {
        bEdfUpdateTimeUp = 0;
        bEditDateTimeFlag = FALSE;
        g_EdfDataSave.AcrossDayEdfDataDispose(RTCOldTime, RTCTime, 1);
    }
}

/**
  * @brief  ECO模式处理
  */
void eco_process(void)
{
    uint8_t i;
    //节能模式1: 加湿器设置为0；Wifi设置：关；屏幕亮度：1；自动息屏：30s
    if(GetWifiFlag())
    {
        g_ConfigSave.SetParameter(WIFI_SWITCH, 0, 0);  //关闭wifi
        g_U8SysSettingMaxFocusNum = g_ConfigSave.GetParameter(WIFI_SWITCH)  
                                    ? MAX_FOCUS_NUM_SYSSETTING_WIFI 
                                    : MAX_FOCUS_NUM_SYSSETTING_OFFWIFI;
        #if (LCD_TYPE == LCD_28_TFT)
        for(i = 0; i < 3; i++)
        {
            IdType_Sys[4][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH)*2][i];        
            IdType_Sys[5][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH)*2 + 1][i];
        }
        #else
        for(i = 0; i < 5; i++)
            IdType_Sys[2][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH)][i];
        #endif
    }
    g_ConfigSave.SetParameter(WARMLEVEL, 0, 0);        //加湿器档位设置为0
    g_ConfigSave.SetParameter(BACKLIGHTBRIGHTNESS, SYS_PARA_BRIGHTNESS_MIN, 0);    //屏幕亮度设置为1
    g_ConfigSave.SetParameter(BACKLIGHTTIME, SYS_PARA_BACKLIGHT_MIN, 0);           //自动息屏设置为30s
    g_ConfigSave.SetParameter(PREHEATSWITCH, 0, 1);    //预热设置界面-预热开关为0
    g_ConfigSave.SetParameter(ECOTYPE,true);
    UpdateIconStatus(ECO_INDICATION_BIT, true);
}

static void OnEnterClick_SysSetting(void)
{
    uint8_t i = 0;
    uint16_t u16OldValue_WifiSetting = 0;


    WM_SelectWindow(WM_HBKWIN);

    //系统参数设置-系统还原、面罩佩戴测试、日期、时间、wifi设置、进行wifi密码设置时的标志位
    //g_U8CurEditState_ForPowerON = True时，不能开机
    g_U8CurEditState_ForPowerON = FALSE;
    g_U8CurEditState_ForAutoBlightNess = FALSE;
    
    if(g_SysSettingScreenWInfo.U8CurFocusID == g_U8SysSettingMaxFocusNum - 1)
    {
        ShowSysSettingScreen(0);
        UpdateEdfData();
    }
    else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == TUBESELECTION_IDX)
        return;
    else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == WIFI_SELECT_IDX)
    {
        ShowWifiSelectedScreen(1);
    }
    else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == RESET_IDX)
    {
        if (GetBldcmStatus() != MOTOR_STOP_STATE)
            return;

        if (GetWifiFlag())
        {
            //系统还原前，记录下Wifi设置的开关状态
            g_U8LastWifiSwitch = g_ConfigSave.GetParameter(WIFI_SWITCH);
        }
        g_U8CurEditState_ForPowerON = TRUE;
        UpdateTipMarket(SYSTEMRESET, 1);
    }
    else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == MASKTEST_IDX)
    {
        if (GetBldcmStatus() != MOTOR_STOP_STATE)
            return;

        ShowMaskWearingTestScreen(1);
        g_U8CurEditState_ForPowerON = TRUE;
    }
	else if(IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == SN_IDX)
	{
		//管理员界面在电机停止时进入
		if(GetBldcmStatus() != MOTOR_STOP_STATE)
		{
			return ;
		}
		
		if(time_count(tick_get(),s_wEnterAdminTick) > ADMIN_OPERATE_TIMES)
		{
			s_wEnterAdminTick = tick_get();
			s_hEnterAdminCnt = 0;
		}
		if(++s_hEnterAdminCnt >= 5)
		{
			s_hEnterAdminCnt = 0;
            if (GetBldcmStatus() != MOTOR_NORMAL_RUN_STATE && GetBldcmStatus() != MOTOR_START_STATE)
            {
			//进入管理员设置界面
			ShowManagerSetScreen(1);
			g_U8CurEditState_ForPowerON = TRUE; //为TRUE时，开机功能失效
            }
        }
	}
    else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == SOFTWAREVERSION_IDX)
    {
		
        return;
    }
    else
    {
        if (g_ConfigSave.GetParameter(ECOTYPE) == SYS_PARA_ECOMODE_ECO_1
            && (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == BACKLIGHT_IDX
            || IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == BRIGHTNESS_IDX
            || IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == WIFI_SET_IDX))                       
            return;

        if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == DATE_IDX
            || IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == TIME_IDX
            || IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == WIFI_SET_IDX)
        {
            if (GetBldcmStatus() != MOTOR_STOP_STATE)
                return;
        }
        
		#if (LCD_TYPE == LCD_28_TFT)
	        GUI_SetColor(GREEN);
	        if(IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] != DATE_IDX
	            &&IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] != TIME_IDX)
	        {
	            DrawRoundedRect(5,10+58*(g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) + STATEBAR_TITLE_HEIGHT,235,60+58*(g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN); 
	        }
		#endif

        if (g_sU8CurEditState_SysSetting == FALSE)
        {
            if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == DATE_IDX)
            {
                g_U8CurEditState_ForPowerON = TRUE;

                if (g_sU8EnterDateEditState == FALSE)
                {
                    #if (LCD_TYPE == LCD_28_TFT)
						GUI_SetColor(GREEN);
	                    DrawRoundedRect(5,10+58*1 + STATEBAR_TITLE_HEIGHT,235,60+58*1 + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                    
	                        DrawDateTime(15
	                                     , 23 + EACHLIST_HEIGHT
	                                     , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
	                                     , DEFAULT_MENU_FOCUSCOLOR
	                                     , FONT_18
	                                     , FONT_24
	                                     , g_sU8CurEditState_SysSetting,0);
					#else
						GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
	                    GUI_FillRect(LIST_X_POS
	                                , LIST_BORDER_HEIGHT + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                                , EACHLIST_WIDTH - 1
	                                , LIST_BORDER_HEIGHT + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
                    
	                    #if (LCD_TYPE == LCD_5_TFT)
	                        DrawDateTime(74
	                                  , 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                                  , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5]
	                                  , FONTCOLOR_SELECTED
	                                  , FONT_32
	                                  , FONT_38
	                                  , g_sU8CurEditState_SysSetting);
	                    #else
	                        DrawDateTime(PARALIST_TITLE_LEFT_MARGIN
	                                     , 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                                     , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5]
	                                     , FONTCOLOR_SELECTED
	                                     , FONT_24
	                                     , FONT_32
	                                     , g_sU8CurEditState_SysSetting);
	                    #endif
					#endif
                                       
                    g_sU8EnterDateEditState = TRUE;
                    bEditDateTimeFlag = FALSE;
                }
                else
                {
                    //开始设置日期
                    EditDateTimeValue();
                    g_sU8CurEditState_DateSetting = TRUE;
                    g_sU8CurEditState_SysSetting = TRUE;
                }
            }
            else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == TIME_IDX)
            {
                g_U8CurEditState_ForPowerON = TRUE;

                if (g_sU8EnterTimeEditState == FALSE)
                {
					#if (LCD_TYPE == LCD_28_TFT)
	                    GUI_SetColor(GREEN);
	                    DrawRoundedRect(5,10+58*2 + STATEBAR_TITLE_HEIGHT,235,60+58*2 + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                    
	                        DrawDateTime(15
	                                     , 23+EACHLIST_HEIGHT*2
	                                     , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
	                                     , DEFAULT_MENU_FOCUSCOLOR
	                                     , FONT_18
	                                     , FONT_24
	                                     , g_sU8CurEditState_SysSetting,0);
					#else
						GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
	                    GUI_FillRect(LIST_X_POS
	                                , LIST_BORDER_HEIGHT + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                                , EACHLIST_WIDTH - 1
	                                , LIST_BORDER_HEIGHT + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
	                    #if (LCD_TYPE == LCD_5_TFT)
	                        DrawDateTime(74
	                                  , 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                                  , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5]
	                                  , FONTCOLOR_SELECTED
	                                  , FONT_32
	                                  , FONT_38
	                                  , g_sU8CurEditState_SysSetting);
	                    #else
	                        DrawDateTime(PARALIST_TITLE_LEFT_MARGIN
	                                     , 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                                     , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5]
	                                     , FONTCOLOR_SELECTED
	                                     , FONT_24
	                                     , FONT_32
	                                     , g_sU8CurEditState_SysSetting);
	                    #endif
					#endif
                    g_sU8EnterTimeEditState = TRUE;
                    bEditDateTimeFlag = FALSE;
                }
                else
                {
                    //开始设置时间                    
                    EditDateTimeValue();                    
                    
                    g_sU8CurEditState_TimeSetting = TRUE;
                    g_sU8CurEditState_SysSetting = TRUE;
                }
            }            
            else
            {
                if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == WIFI_SET_IDX)
                    g_U8CurEditState_ForPowerON = TRUE;
                
                if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == BRIGHTNESS_IDX)
                {
                    g_U8CurEditState_ForAutoBlightNess = TRUE;
                }

                if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == ECOSETTING_IDX)
                {
                    //标准模式0：获取数据，更新数组
                    if (g_ConfigSave.GetParameter(ECOTYPE) == SYS_PARA_ECOMODE_STANDARD_0)
                    {
                        g_sU16ECOStandardModeValue[0] = g_ConfigSave.GetParameter(WIFI_SWITCH);  //Wifi设置
                        g_sU16ECOStandardModeValue[1] = g_ConfigSave.GetParameter(WARMLEVEL);    //加湿器档位
                        g_sU16ECOStandardModeValue[2] = g_ConfigSave.GetParameter(BACKLIGHTBRIGHTNESS);  //屏幕亮度
                        g_sU16ECOStandardModeValue[3] = g_ConfigSave.GetParameter(BACKLIGHTTIME);    //自动息屏
                        g_sU16ECOStandardModeValue[4] = g_ConfigSave.GetParameter(PREHEATSWITCH);    //预热设置界面-预热开关
                    }
                }

                g_sU8DateSettingFocus = g_sU8OldDateSettingFocus = 0;
                g_sU8TimeSettingFocus = g_sU8OldTimeSettingFocus = 0;          
      
                
                #if (LCD_TYPE == LCD_28_TFT)
					SetUiItemText(15
	                              , 28 + (g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                              , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
	                              , DEFAULT_MENU_FOCUSCOLOR
	                              , FONT_18
	                              , FONT_24
	                              , g_sU8CurEditState_SysSetting,TRUE);
				#else
					GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
	                GUI_FillRect(LIST_X_POS
	                            , LIST_BORDER_HEIGHT + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                            , EACHLIST_WIDTH - 1
	                            , LIST_BORDER_HEIGHT + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);                
                
	                #if (LCD_TYPE == LCD_5_TFT)
	                    SetUiItemText(74
	                                  , 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                                  , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5]
	                                  , FONTCOLOR_SELECTED
	                                  , FONT_32
	                                  , FONT_38
	                                  , g_sU8CurEditState_SysSetting);
	                #else
	                    SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
	                                  , 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                                  , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5]
	                                  , FONTCOLOR_SELECTED
	                                  , FONT_24
	                                  , FONT_32
	                                  , g_sU8CurEditState_SysSetting);
	                #endif
				#endif
                g_sU8CurEditState_SysSetting = TRUE;
            }
        }
        else
        {
            #if (LCD_TYPE == LCD_28_TFT)
				if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == WIFI_SET_IDX)
	                u16OldValue_WifiSetting = g_ConfigSave.GetParameter(WIFI_SWITCH);
            
	            //退出编辑状态，将参数写入系统
	             SetUiItemText(15
	                              , 28 + (g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                              , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
	                              , GUI_WHITE
	                              , FONT_18
	                              , FONT_18
	                              , g_sU8CurEditState_SysSetting,FALSE);
			#else
				GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
	            GUI_FillRect(LIST_X_POS
	                        , LIST_BORDER_HEIGHT + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                        , EACHLIST_WIDTH - 1
	                        , LIST_BORDER_HEIGHT + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);

            if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] == WIFI_SET_IDX)
                u16OldValue_WifiSetting = g_ConfigSave.GetParameter(WIFI_SWITCH);
            
	            //退出编辑状态，将参数写入系统
	            #if (LCD_TYPE == LCD_5_TFT)
	                SetUiItemText(74
	                              , 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                              , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5]
	                              , FONTCOLOR_DEFAULT
	                              , FONT_32
	                              , FONT_32
	                              , g_sU8CurEditState_SysSetting);
	            #else
	                SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
	                              , 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
	                              , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5]
	                              , FONTCOLOR_DEFAULT
	                              , FONT_24
	                              , FONT_24
	                              , g_sU8CurEditState_SysSetting);
	            #endif
			#endif
            
            g_sU8CurEditState_SysSetting = FALSE;
            
            g_sU8EnterDateEditState = FALSE;
            g_sU8EnterTimeEditState = FALSE;
                                          
            g_sU8CurEditState_DateSetting = FALSE;
            g_sU8CurEditState_TimeSetting = FALSE;

            g_sU8DateSettingFocus = 0;
            g_sU8OldDateSettingFocus = 0;

            g_sU8TimeSettingFocus = 0;
            g_sU8OldTimeSettingFocus = 0;

            if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == DATE_IDX)
            {
                    if ((g_OldSysSettingRTCTime.year != g_SysSettingRTCTime.year) 
                    || (g_OldSysSettingRTCTime.mon != g_SysSettingRTCTime.mon) 
                    || (g_OldSysSettingRTCTime.day != g_SysSettingRTCTime.day))
                    {
                        if (g_SysSettingRTCTime.day > CalEachMonthDays())
                            g_SysSettingRTCTime.day = CalEachMonthDays();

                        RTC_ReadTime(&RTCTime);                    
                        memcpy(&RTCOldTime, &RTCTime, sizeof(RTCTime));                    
                        RTCTime.year = g_SysSettingRTCTime.year;
                        RTCTime.mon = g_SysSettingRTCTime.mon;
                        RTCTime.day = g_SysSettingRTCTime.day;

                        RTC_SetTime(&RTCTime);
                        g_OldSysSettingRTCTime = g_SysSettingRTCTime;
                        UpdateConsumTime(RTCTime, FALSE);
                        bEditDateTimeFlag = TRUE;
                    }
                    g_U8CurEditState_ForPowerON = FALSE;
                    #if (LCD_TYPE == LCD_28_TFT)
                        GUI_SetColor(GREEN);//刷新                          
                        DrawRoundedRect(5,10+58*1 + STATEBAR_TITLE_HEIGHT,235,60+58*1 + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                        DrawDateTime(15
                                     , 23 + EACHLIST_HEIGHT
                                     , DATE_IDX
                                     , GUI_WHITE
                                     , FONT_18
                                     , FONT_24
                                     , g_sU8CurEditState_SysSetting,1);
                    #endif
            }
            else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == TIME_IDX)
            {
                    if ((g_OldSysSettingRTCTime.hour != g_SysSettingRTCTime.hour) 
                    || (g_OldSysSettingRTCTime.minu != g_SysSettingRTCTime.minu) 
                    || (g_OldSysSettingRTCTime.sec != g_SysSettingRTCTime.sec))
                    {                
                        RTC_ReadTime(&RTCTime);
                        memcpy(&RTCOldTime, &RTCTime, sizeof(RTCTime));
                        RTCTime.hour = g_SysSettingRTCTime.hour;
                        RTCTime.minu = g_SysSettingRTCTime.minu;
                        RTCTime.sec = g_SysSettingRTCTime.sec;
                        RTC_SetTime(&RTCTime);
                        g_OldSysSettingRTCTime = g_SysSettingRTCTime;
                        bEditDateTimeFlag = TRUE;
                    }
                    g_U8CurEditState_ForPowerON = FALSE;
                    #if (LCD_TYPE == LCD_28_TFT)
                    GUI_SetColor(GREEN);//刷新                          
                    DrawRoundedRect(5,10+58*2 + STATEBAR_TITLE_HEIGHT,235,60+58*2 + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                    DrawDateTime(15
                                 , 23 + EACHLIST_HEIGHT*2
                                 , TIME_IDX
                                 , GUI_WHITE
                                 , FONT_18
                                 , FONT_24
                                 , g_sU8CurEditState_SysSetting,2);
                    #endif
            }
            else
            {
                //将参数写入系统
                U8 sU8Changed = SaveTheValueChanged(IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]);
                mask_type_leak_parm_calc();
                //ECO：舒适(0)、节能(1)、飞行(2) 以下存储参数时，前几个不进行实时EEPROM，FLASH存储，只有最后一个才进行存储
                if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == ECOSETTING_IDX && sU8Changed)
                {
                    //标准模式0：还原参数
                    if (g_ConfigSave.GetParameter(ECOTYPE) == SYS_PARA_ECOMODE_STANDARD_0)
                    {
                        //标准
                        if(GetWifiFlag())
                            g_ConfigSave.SetParameter(WIFI_SWITCH, g_sU16ECOStandardModeValue[0], 0);       //关闭wifi
                        g_ConfigSave.SetParameter(WARMLEVEL, g_sU16ECOStandardModeValue[1], 0);             //加湿器档位设置为0
                        g_ConfigSave.SetParameter(BACKLIGHTBRIGHTNESS, g_sU16ECOStandardModeValue[2], 0);   //屏幕亮度设置为1
                        g_ConfigSave.SetParameter(BACKLIGHTTIME, g_sU16ECOStandardModeValue[3], 0);         //自动息屏设置为30s
                        g_ConfigSave.SetParameter(PREHEATSWITCH, g_sU16ECOStandardModeValue[4], 1);         //预热设置界面--预热开关
                        #if (LCD_TYPE == LCD_28_TFT)            
                                    ScanningWifiCalSysSettingMaxFocus();
                        #endif
                        UpdateIconStatus(ECO_INDICATION_BIT, false);
                    }
                    else if (g_ConfigSave.GetParameter(ECOTYPE) == SYS_PARA_ECOMODE_ECO_1)
                    {
                        if(g_ConfigSave.GetParameter(LANGUAGE) == LANG_ENGLISH)
                        {
                            UpdateTipMarket(ECOTIPS, 1);
                        }
                        else
                        {
                            eco_process();
                        }
                    }
                }
                else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == WIFI_SET_IDX)
                {
                    g_U8CurEditState_ForPowerON = FALSE;
                    
                    if (g_sU16CurEditValue != u16OldValue_WifiSetting)
                    {
                        ScanningWifiCalSysSettingMaxFocus();
                        DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, TRUE);
                        ScanningWifiConnectedStatus();
                    }
                }
            }
        }
    }
}

static void ChangeFocusSysSetting_Right(void)
{
//    WM_SelectWindow(WM_HBKWIN);
    
    GUI_SetTextMode(GUI_TM_TRANS);  //设置背景透明
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
    #else
		#if (LCD_TYPE == LCD_28_TFT)
    		GUI_SetFont(FONT_18);
		#else
        	GUI_SetFont(FONT_24);
		#endif
    #endif
    RefreshSysSettingScreen_Right();
    g_SysSettingScreenWInfo.U8OldFocusID = g_SysSettingScreenWInfo.U8CurFocusID;
}

static void ChangeFocusSysSetting_Left(void)
{   
//    WM_SelectWindow(WM_HBKWIN);
    
    GUI_SetTextMode(GUI_TM_TRANS);  //设置背景透明
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
    #else
		#if (LCD_TYPE == LCD_28_TFT)
    		GUI_SetFont(FONT_18);
		#else
        	GUI_SetFont(FONT_24);
		#endif
    #endif

    RefreshSysSettingScreen_Left();

    g_SysSettingScreenWInfo.U8OldFocusID = g_SysSettingScreenWInfo.U8CurFocusID;
}

#if (LCD_TYPE == LCD_28_TFT)
static void RefreshSysSettingScreen_Left(void)
{    
    if (g_SysSettingScreenWInfo.U8CurFocusID == g_U8SysSettingMaxFocusNum - 1  && (g_U8SysSettingMaxFocusNum - 1) % MAX_PAGE_LINE != 0)
        DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, 2);
    else
        DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, FALSE);
}
#else
static void RefreshSysSettingScreen_Left(void)
{    
    if (g_SysSettingScreenWInfo.U8CurFocusID == g_U8SysSettingMaxFocusNum - 1)
        DrawSysSettingScreen((g_SysSettingScreenWInfo.U8CurFocusID - 1) / 5, FALSE);
    else if (g_SysSettingScreenWInfo.U8CurFocusID % 5 == 4 
           && (g_SysSettingScreenWInfo.U8CurFocusID / 5 < (g_U8SysSettingMaxFocusNum - 2) / 5))
        DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / 5, FALSE);   //翻到第1页,并选择该页的最后一项
    else
        ChangeFocusNoPageTurning_ChildScreen(g_SysSettingScreenWInfo.U8CurFocusID
                                            , g_SysSettingScreenWInfo.U8OldFocusID
                                            , g_U8SysSettingMaxFocusNum
                                            , IdType_Sys
                                            , g_sU8CurEditState_SysSetting);
}
#endif

#if (LCD_TYPE == LCD_28_TFT)
static void RefreshSysSettingScreen_Right(void)
{
    if (g_SysSettingScreenWInfo.U8CurFocusID == (g_U8SysSettingMaxFocusNum - 2)/MAX_PAGE_LINE * MAX_PAGE_LINE && (g_U8SysSettingMaxFocusNum - 1)%MAX_PAGE_LINE != 0)
        DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, 2); //翻页，刷新
    else
        DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, FALSE);
}
#else
static void RefreshSysSettingScreen_Right(void)
{
    if (g_SysSettingScreenWInfo.U8CurFocusID % 5 == 0 && g_SysSettingScreenWInfo.U8CurFocusID < (g_U8SysSettingMaxFocusNum - 1))
        DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / 5, TRUE); //翻到第1页，并选中该页的第1行
    else
        ChangeFocusNoPageTurning_ChildScreen(g_SysSettingScreenWInfo.U8CurFocusID
                                            , g_SysSettingScreenWInfo.U8OldFocusID
                                            , g_U8SysSettingMaxFocusNum
                                            , IdType_Sys
                                            , g_sU8CurEditState_SysSetting);
}
#endif

#if (LCD_TYPE == LCD_28_TFT)
void RepaintSysSettingScreen(void)
{   
    g_U8LastPipeType = g_ConfigSave.GetParameter(TUBETYPE);
    
    DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE, TRUE);
}
#else
void RepaintSysSettingScreen(void)
{   
    g_U8LastPipeType = g_ConfigSave.GetParameter(TUBETYPE);

    if (g_U8SysSettingMaxFocusNum % 5 == 1)
    {
        if (g_SysSettingScreenWInfo.U8CurFocusID == (g_U8SysSettingMaxFocusNum - 1) && g_SysSettingScreenWInfo.U8CurFocusID / 5 > 0)
        {
            //最大焦点个数为16个(有wifi模块，且wifi设置为开)，当前焦点号为15(即，焦点在"菜单")时
            //触发报警，按静音键后，重绘当前"系统设置"第三页界面，当前焦点号为15(即，焦点在"菜单")，非该页第一行
            DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / 5 - 1, 0);
        }
        else
        {
            DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / 5, (g_SysSettingScreenWInfo.U8CurFocusID % 5) == 0);
        }
    }
    else
    {
        DrawSysSettingScreen(g_SysSettingScreenWInfo.U8CurFocusID / 5, (g_SysSettingScreenWInfo.U8CurFocusID % 5) == 0);
    }    
}
#endif

#if (LCD_TYPE == LCD_28_TFT)
static void DrawSysSettingScreen(uint8_t u8Page, uint8_t u8IsFirst)
{
	uint8_t i;
    static uint8_t Disp[4]={0,0,0,0};
    static uint8_t LastPage = 0;
    RTC_TimeTypeDef1 RTC_Time;    
    RTC_ReadTime(&RTC_Time);    
    
    g_OldSysSettingRTCTime = g_SysSettingRTCTime = RTC_Time;
    
    if ((g_U8SysSettingMaxFocusNum - 1) % MAX_PAGE_LINE == 0 && u8Page > ((g_U8SysSettingMaxFocusNum - 1) / MAX_PAGE_LINE) - 1)
    {
        u8Page = ((g_U8SysSettingMaxFocusNum - 1) / MAX_PAGE_LINE) - 1;
    }
    else if (u8Page > ((g_U8SysSettingMaxFocusNum - 1) / MAX_PAGE_LINE))
    {
        u8Page = ((g_U8SysSettingMaxFocusNum - 1) / MAX_PAGE_LINE);
    }

	if(u8Page == 4) // 当WiFi关闭时，WiFi选择列表需隐藏
	{
		if (GetWifiFlag())
		{
			g_U8SysSettingMaxFocusNum = g_ConfigSave.GetParameter(WIFI_SWITCH)
										? MAX_FOCUS_NUM_SYSSETTING_WIFI 
										: MAX_FOCUS_NUM_SYSSETTING_OFFWIFI;
			if(g_ConfigSave.GetParameter(WIFI_SWITCH))
			{
				for(i = 0; i < 3; i++)
					IdType_Sys[4][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH) + 1][i];
			}
			else
			{
				for(i = 0; i < 3; i++)
					IdType_Sys[4][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH)][i];
			}
		}
	}
	
    //绘制背景条纹框
    if(u8IsFirst)
    {
        for(i = 0;i<4;i++)
        {
            Disp[i] = 0;
        }
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);//设置界面背景色-黑色(0, 0, 0)
        if(u8IsFirst == 1)
        {
            LastPage = u8Page;
            GUI_FillRect(0, STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, MAINSCREEN_HEIGHT + STATEBAR_TITLE_HEIGHT);
        }
        else
            GUI_FillRect(0, STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, Return_Y-25 + STATEBAR_TITLE_HEIGHT);
    }
    if(LastPage != u8Page)
    {
        u8IsFirst = 2;//翻页不刷新返回
        LastPage = u8Page;
    }
    GUI_SetTextMode(GUI_TM_TRANS);  //设置背景透明
    GUI_SetFont(FONT_18);

    if ((g_U8SysSettingMaxFocusNum -1) % MAX_PAGE_LINE == 0
        || ((g_U8SysSettingMaxFocusNum -1) % MAX_PAGE_LINE != 0 
        && u8Page < ((g_U8SysSettingMaxFocusNum - 1) / MAX_PAGE_LINE)))
    {
        for(i = 0; i < 3; i++)
        {
            if(u8IsFirst 
                || (i == g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) 
                || Disp[i] == 1)
            {
                if(i == g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE
                  && g_SysSettingScreenWInfo.U8CurFocusID < g_U8SysSettingMaxFocusNum - 1)
                {
                    if(u8IsFirst == 1)
                         GUI_SetColor(GREEN);
                    else
                        DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                    Disp[i] = 1;
                }
                else
                {
                    if(u8IsFirst == 1)
                        GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                    else
                        DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);
                    Disp[i] = 0;
                }
                if(u8IsFirst == 1)
                    GUI_AA_FillRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,234,59+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED);
     
                if(IdType_Sys[u8Page][i] == DATE_IDX)
                {
                    DrawDateTime(15
                                 , 23 + EACHLIST_HEIGHT
                                 , DATE_IDX
                                 , GUI_WHITE
                                 , FONT_18
                                 , FONT_24
                                 , g_sU8CurEditState_SysSetting,1);
                }
                else if (IdType_Sys[u8Page][i] == TIME_IDX)
                {
                    DrawDateTime(15
                                 , 23+EACHLIST_HEIGHT*2
                                 , TIME_IDX
                                 , GUI_WHITE
                                 , FONT_18
                                 , FONT_24
                                 , g_sU8CurEditState_SysSetting,2);
                }
                else
                {
                    if(i == g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE)
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[u8Page][i]
                                      , g_sU8CurEditState_SysSetting ? DEFAULT_MENU_FOCUSCOLOR : GUI_WHITE
                                      , FONT_18
                                      , g_sU8CurEditState_SysSetting ? FONT_24 : FONT_18
                                      , g_sU8CurEditState_SysSetting,g_sU8CurEditState_SysSetting);
                    }
                    else
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[u8Page][i]
                                      , GUI_WHITE
                                      , FONT_18
                                      , FONT_18
                                      , FALSE,FALSE);
                    }
//                    if (g_U8ChangeParaOnEditState == FALSE)
                        SetUiItemValueAndUnitsText(IdType_Sys[u8Page][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE], g_U8ChangeParaOnEditState);
                }
            }
        }
    }
    else
    {
        if (u8Page >= (g_U8SysSettingMaxFocusNum - 1) / MAX_PAGE_LINE)
        {
            for(i = 0; i < (g_U8SysSettingMaxFocusNum - 1) % MAX_PAGE_LINE; i++)
            {
                if(u8IsFirst 
                 || (i == g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE) 
                 || Disp[i] == 1)
                {
                    if(i == g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE 
                        && g_SysSettingScreenWInfo.U8CurFocusID < g_U8SysSettingMaxFocusNum - 1)
                    {
                        if(u8IsFirst == 1)
                            GUI_SetColor(GREEN);
                        else
                            DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                        Disp[i] = 1;
                    }
                    else
                    {
                        if(u8IsFirst == 1)
                            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                        else
                            DrawRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,235,60+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);
                        Disp[i] = 0;
                    }
                    if(u8IsFirst == 1)
                        GUI_AA_FillRoundedRect(5,10+58*i + STATEBAR_TITLE_HEIGHT,234,59+58*i + STATEBAR_TITLE_HEIGHT,ROUNDED);

                    if(i == g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE)
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[u8Page][i]
                                      , g_sU8CurEditState_SysSetting ? DEFAULT_MENU_FOCUSCOLOR : GUI_WHITE
                                      , FONT_18
                                      , g_sU8CurEditState_SysSetting ? FONT_24 : FONT_18
                                      , g_sU8CurEditState_SysSetting,g_sU8CurEditState_SysSetting);
                    }
                    else
                    {
                        SetUiItemText(15
                                      , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[u8Page][i]
                                      , GUI_WHITE
                                      , FONT_18
                                      , FONT_18
                                      , FALSE,FALSE);
                    }
                }
            }
        }
    if (g_SysSettingScreenWInfo.U8CurFocusID != g_U8SysSettingMaxFocusNum - 1)
            SetUiItemValueAndUnitsText(IdType_Sys[u8Page][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE], g_U8ChangeParaOnEditState);
    }
    if(u8IsFirst == 1 || (g_SysSettingScreenWInfo.U8CurFocusID == g_U8SysSettingMaxFocusNum -1) || Disp[3] == 1)
    {
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
        GUI_FillRect(0, Return_Y + STATEBAR_TITLE_HEIGHT - 20, SCREEN_WIDTH, SCREEN_HEIGHT);

        if(g_SysSettingScreenWInfo.U8CurFocusID == g_U8SysSettingMaxFocusNum - 1)
        {
            GUI_SetColor(GREEN);
            Disp[3] = 1;
            GUI_AA_FillCircle(Return_X,Return_Y + STATEBAR_TITLE_HEIGHT,20);
            DrawBMP(BMP_RETURNFOCU, Return_X-12, Return_Y-10 + STATEBAR_TITLE_HEIGHT);
        }
        else
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            Disp[3] = 0;
            GUI_AA_FillCircle(Return_X,Return_Y + STATEBAR_TITLE_HEIGHT,20);
            DrawBMP(BMP_RETURN, Return_X-12, Return_Y-10 + STATEBAR_TITLE_HEIGHT);
        }

        DrawPageCircle(u8Page, (g_U8SysSettingMaxFocusNum - 2) / MAX_PAGE_LINE + 1);
    }
    if(u8IsFirst == 2)
        DrawPageCircle(u8Page, (g_U8SysSettingMaxFocusNum - 2) / MAX_PAGE_LINE + 1);
}
#else
static void DrawSysSettingScreen(uint8_t u8Page, uint8_t u8IsFirst)
{
	uint8_t i;
    uint8_t j;
    RTC_TimeTypeDef1 RTC_Time;    
    RTC_ReadTime(&RTC_Time);    
   
    g_OldSysSettingRTCTime = g_SysSettingRTCTime = RTC_Time;

    //绘制背景条纹框
    DrawChildSettingScreenBackground();    

    GUI_SetTextMode(GUI_TM_TRANS);  //设置背景透明
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
    #else
        GUI_SetFont(FONT_24);
    #endif

    if (g_SysSettingScreenWInfo.U8CurFocusID != g_U8SysSettingMaxFocusNum - 1)
    {        
        GUI_SetColor(CHILD_INTERFACE_LIST_BORDERCOLOR_SELECTED);
        for (j = 0; j < 2; j++)
        {
            GUI_FillRect(LIST_X_POS
                        , (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + EACHLIST_HEIGHT * j + STATEBAR_TITLE_HEIGHT
                        , SCREEN_WIDTH - LIST_X_POS
                        , (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + EACHLIST_HEIGHT * j + LIST_BORDER_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
        }
    }

    switch (u8Page)
    {
        case 0:
        case 1:
        {            
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);                
            //GUI_FillRect(LIST_X_POS, 4 * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT, 800 - LIST_X_POS, 4 * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + LIST_HEIGHT - 1);                 
            GUI_FillRect(LIST_X_POS
                        , (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + STATEBAR_TITLE_HEIGHT
                        , SCREEN_WIDTH - LIST_X_POS
                        , (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
            
            for(i = 0; i < 5; i++)
            {
                if (g_ConfigSave.GetParameter(ECOTYPE) == SYS_PARA_ECOMODE_ECO_1
                    && (IdType_Sys[u8Page][i] == BACKLIGHT_IDX
                    || IdType_Sys[u8Page][i] == BRIGHTNESS_IDX))
                {
                    #if (LCD_TYPE == LCD_5_TFT)
                        SetUiItemText(74
                                      , 3 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[u8Page][i]
                                      , PAGE_CIRCLE_COLOR_DEFAULT
                                      , FONT_32
                                      , FONT_32
                                      , g_sU8CurEditState_SysSetting);
                    #else
                        SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                      , 2 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[u8Page][i]
                                      , PAGE_CIRCLE_COLOR_DEFAULT
                                      , FONT_24
                                      , FONT_24
                                      , g_sU8CurEditState_SysSetting);
                    #endif
                }
                else
                {
                    #if (LCD_TYPE == LCD_5_TFT)
                        if (g_SysSettingScreenWInfo.U8CurFocusID == g_SysSettingScreenWInfo.U8CurFocusID / 5 * 5 + i % 5)
                            SetUiItemText(74
                                          , 3 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                          , IdType_Sys[u8Page][i % 5]
                                          , g_sU8CurEditState_SysSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                          , FONT_32
                                          , g_sU8CurEditState_SysSetting ? FONT_38 : FONT_32
                                          , g_sU8CurEditState_SysSetting);
                        else
                            SetUiItemText(74
                                          , 3 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                          , IdType_Sys[u8Page][i % 5]
                                          , GUI_WHITE
                                          , FONT_32
                                          , FONT_32
                                          , FALSE);
                    #else
                        if (g_SysSettingScreenWInfo.U8CurFocusID == g_SysSettingScreenWInfo.U8CurFocusID / 5 * 5 + i % 5)
                            SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                          , 2 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                          , IdType_Sys[u8Page][i % 5]
                                          , g_sU8CurEditState_SysSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                          , FONT_24
                                          , g_sU8CurEditState_SysSetting ? FONT_32 : FONT_24
                                          , g_sU8CurEditState_SysSetting);
                        else
                            SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                          , 2 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                          , IdType_Sys[u8Page][i % 5]
                                          , GUI_WHITE
                                          , FONT_24
                                          , FONT_24
                                          , FALSE);
                    #endif
//                    if (g_U8ChangeParaOnEditState == FALSE)
                        SetUiItemValueAndUnitsText(IdType_Sys[u8Page][g_SysSettingScreenWInfo.U8CurFocusID % 5], g_U8ChangeParaOnEditState);
                }
            }

            //菜单图标
            GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
            #if (LCD_TYPE == LCD_5_TFT)
                GUI_AA_FillCircle(655, 408 + 33 - 70 + STATEBAR_TITLE_HEIGHT, 33);
                GUI_FillRect(655, 408 - 70 + STATEBAR_TITLE_HEIGHT, 655 + 133, 408 - 70 + 33 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
            #else
                GUI_AA_FillCircle(393, 272 + 22 - 46 + STATEBAR_TITLE_HEIGHT, 22);
                GUI_FillRect(393, 272 - 46 + STATEBAR_TITLE_HEIGHT, 393 + 80, 272 - 46 + 22 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
            #endif

            break;
        }
        case 2:
        case 3:
        {
            if (GetWifiFlag())
            {
                g_U8SysSettingMaxFocusNum = g_ConfigSave.GetParameter(WIFI_SWITCH)
                                            ? MAX_FOCUS_NUM_SYSSETTING_WIFI 
                                            : MAX_FOCUS_NUM_SYSSETTING_OFFWIFI;
                for(i = 0; i < 5; i++)
                    IdType_Sys[2][i] = IdType_IsWifi[g_ConfigSave.GetParameter(WIFI_SWITCH)][i];
            }

            if (g_SysSettingScreenWInfo.U8CurFocusID != g_U8SysSettingMaxFocusNum - 1)
            {
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
                GUI_FillRect(LIST_X_POS
                            , (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + STATEBAR_TITLE_HEIGHT
                            , SCREEN_WIDTH - LIST_X_POS
                            , (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + LIST_BORDER_HEIGHT + LIST_HEIGHT - 1 + STATEBAR_TITLE_HEIGHT);
            }
            if (u8IsFirst || g_SysSettingScreenWInfo.U8CurFocusID != g_U8SysSettingMaxFocusNum - 1)
            {
                //画菜单背景
                GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_AA_FillCircle(655, 408 + 33 - 70 + STATEBAR_TITLE_HEIGHT, 33);
                    GUI_FillRect(655, 408 - 70 + STATEBAR_TITLE_HEIGHT, 655 + 133, 408 - 70 + 33 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
                #else
                    GUI_AA_FillCircle(393, 272 + 22 - 46 + STATEBAR_TITLE_HEIGHT, 22);
                    GUI_FillRect(393, 272 - 46 + STATEBAR_TITLE_HEIGHT, 393 + 80, 272 - 46 + 22 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
                #endif
            }
            else
            {   
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_AA_FillCircle(655, 408 + 33 - 70 + STATEBAR_TITLE_HEIGHT, 33);
                    GUI_FillRect(655, 408 - 70 + STATEBAR_TITLE_HEIGHT, 655 + 133, 408 - 70 + 33 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
                #else
                    GUI_AA_FillCircle(393, 272 + 22 - 46 + STATEBAR_TITLE_HEIGHT, 22);
                    GUI_FillRect(393, 272 - 46 + STATEBAR_TITLE_HEIGHT, 393 + 80, 272 - 46 + 22 * 2 - 1 + STATEBAR_TITLE_HEIGHT);
                #endif
            }

            for(i = 0; i < 5; i++)
            {
                if (g_ConfigSave.GetParameter(ECOTYPE) == SYS_PARA_ECOMODE_ECO_1
                    && IdType_Sys[u8Page][i] == WIFI_SET_IDX)
                {
                    #if (LCD_TYPE == LCD_5_TFT)
                        SetUiItemText(74
                                      , 3 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[u8Page][i]
                                      , PAGE_CIRCLE_COLOR_DEFAULT
                                      , FONT_32
                                      , FONT_32
                                      , g_sU8CurEditState_SysSetting);
                    #else
                        SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                      , 2 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                      , IdType_Sys[u8Page][i]
                                      , PAGE_CIRCLE_COLOR_DEFAULT
                                      , FONT_24
                                      , FONT_24
                                      , g_sU8CurEditState_SysSetting);
                    #endif
                }
                else
                {
                    #if (LCD_TYPE == LCD_5_TFT)
                        if (g_SysSettingScreenWInfo.U8CurFocusID == g_SysSettingScreenWInfo.U8CurFocusID / 5 * 5 + i % 5)
                            SetUiItemText(74
                                          , 3 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                          , IdType_Sys[u8Page][i % 5]
                                          , g_sU8CurEditState_SysSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                          , FONT_32
                                          , g_sU8CurEditState_SysSetting ? FONT_38 : FONT_32
                                          , g_sU8CurEditState_SysSetting);
                        else
                            SetUiItemText(74
                                          , 3 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                          , IdType_Sys[u8Page][i % 5]
                                          , GUI_WHITE
                                          , FONT_32
                                          , FONT_32
                                          , FALSE);
                    #else
                        if (g_SysSettingScreenWInfo.U8CurFocusID == g_SysSettingScreenWInfo.U8CurFocusID / 5 * 5 + i % 5)
                            SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                          , 2 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                          , IdType_Sys[u8Page][i % 5]
                                          , g_sU8CurEditState_SysSetting ? FONTCOLOR_SELECTED : GUI_WHITE
                                          , FONT_24
                                          , g_sU8CurEditState_SysSetting ? FONT_32 : FONT_24
                                          , g_sU8CurEditState_SysSetting);
                        else
                            SetUiItemText(PARALIST_TITLE_LEFT_MARGIN
                                          , 2 + (i % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                          , IdType_Sys[u8Page][i % 5]
                                          , GUI_WHITE
                                          , FONT_24
                                          , FONT_24
                                          , FALSE);
                    #endif
//                    if (g_U8ChangeParaOnEditState == FALSE)
                        SetUiItemValueAndUnitsText(IdType_Sys[u8Page][g_SysSettingScreenWInfo.U8CurFocusID % 5], g_U8ChangeParaOnEditState);
                }
            }
            break;
        }
        default:
            break;
    }
    if (GetWifiFlag() && g_ConfigSave.GetParameter(WIFI_SWITCH))
    {
        //底下的页面小圆点
        DrawPageCircle(u8Page, 4);    
    }
    else
    {
        //底下的页面小圆点
        DrawPageCircle(u8Page, 3);
    }

    GUI_SetColor(GUI_WHITE);
    GUI_DispStringInRect(GetMultiLanguageString(MENU_IDX), &g_MenuIconRect, GUI_TA_HCENTER | GUI_TA_VCENTER);
    #if (LCD_TYPE == LCD_5_TFT)
        DrawMenuIcon(656, 429 - 70 + STATEBAR_TITLE_HEIGHT);
    #else
        DrawMenuIcon(384, 286 - 46 + STATEBAR_TITLE_HEIGHT);
    #endif
}
#endif

#if (LCD_TYPE == LCD_28_TFT)
static void DrawDateTime(int x0, int y0, ESTRINGIDX Idx, uint32_t U32FontColor, const GUI_FONT * pNewFont1, const GUI_FONT * pNewFont2, uint8_t u8EditState ,uint8_t u8DISP)
{
    char str[32];    
    GUI_RECT rect;

    SetUiItemValueAndUnitsText(Idx, u8EditState);

    rect.x0 = x0;
    rect.y0 = y0 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = rect.x0 + PARALIST_TITLE_LEN;
    rect.y1 = rect.y0 + LIST_HEIGHT - 1;        
    
    GUI_SetColor(U32FontColor);
    GUI_SetFont(pNewFont1);
    GUI_DispStringInRect(strItem[0], &rect, GUI_TA_LEFT | GUI_TA_VCENTER);

    rect.x0 = x0 + PARALIST_VALUE_LEN_X0; //X:376
    rect.x1 = rect.x0 + PARALIST_VALUE_LEN_X1 - 1;
    
    GUI_DispStringInRect(strItem[2], &rect, GUI_TA_LEFT | GUI_TA_VCENTER);

    if ((IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == DATE_IDX && u8DISP == 0)|| u8DISP == 1)
    {
        GUI_SetFont(pNewFont2); 
        rect.x0 = x0 + PARALIST_UINT_TIME_X0; //X:376
        rect.x1 = rect.x0 + 80 - 1;

        sprintf(str, "%04d", g_SysSettingRTCTime.year);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);

        //5寸屏：4 * NUMFONT_WIDTH_38
        //3.5寸屏：4 * NUMFONT_WIDTH_24
       
        rect.x0 = x0 + PARALIST_UINT_TIME_X0 + 4 * NUMFONT_WIDTH_24; //X:376
        rect.x1 = rect.x0 + 80 - 1;
        
        sprintf(str, "-%02d-%02d", g_SysSettingRTCTime.mon, g_SysSettingRTCTime.day);
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    }
    else if ((IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == TIME_IDX && u8DISP == 0) || u8DISP == 2)
    {
        GUI_SetFont(pNewFont2); 
        rect.x0 = x0 + PARALIST_UINT_TIME_X0; //X:376
        rect.x1 = rect.x0 + 80 - 1;

        sprintf(str, "%02d", g_SysSettingRTCTime.hour);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);

        //5寸屏：2 * NUMFONT_WIDTH_38
        //3.5寸屏：2 * NUMFONT_WIDTH_24
       
        rect.x0 = x0 + PARALIST_UINT_TIME_X0 + 2 * NUMFONT_WIDTH_24; //X:376
        
        rect.x1 = rect.x0 + 80 - 1;
        
        sprintf(str, ":%02d:%02d", g_SysSettingRTCTime.minu, g_SysSettingRTCTime.sec);
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    }
}
#else
static void DrawDateTime(int x0, int y0, ESTRINGIDX Idx, uint32_t U32FontColor, const GUI_FONT * pNewFont1, const GUI_FONT * pNewFont2, uint8_t u8EditState)
{
    char str[32];    
    GUI_RECT rect;

    SetUiItemValueAndUnitsText(Idx, u8EditState);

    rect.x0 = x0;
    rect.y0 = y0;// + STATEBAR_TITLE_HEIGHT;
    rect.x1 = rect.x0 + PARALIST_TITLE_LEN;
    rect.y1 = rect.y0 + LIST_HEIGHT - 1;        
    
    GUI_SetColor(U32FontColor);
    GUI_SetFont(pNewFont1);
    GUI_DispStringInRect(strItem[0], &rect, GUI_TA_LEFT | GUI_TA_VCENTER);

    rect.x0 = x0 + PARALIST_VALUE_LEN_X0; //X:376
    rect.x1 = rect.x0 + PARALIST_VALUE_LEN_X1 - 1;
    
    GUI_DispStringInRect(strItem[2], &rect, GUI_TA_LEFT | GUI_TA_VCENTER);

    if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] == DATE_IDX)
    {
        GUI_SetFont(pNewFont2); 
        rect.x0 = x0 + PARALIST_UINT_LEN_X0; //X:376
        rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 - 1;

        sprintf(str, "%04d", g_SysSettingRTCTime.year);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);

        //5寸屏：4 * NUMFONT_WIDTH_38
        //3.5寸屏：4 * NUMFONT_WIDTH_32
        #if (LCD_TYPE == LCD_5_TFT)
            rect.x0 = x0 + PARALIST_UINT_LEN_X0 + 4 * NUMFONT_WIDTH_38; //X:376
        #else
            rect.x0 = x0 + PARALIST_UINT_LEN_X0 + 4 * NUMFONT_WIDTH_32; //X:376
        #endif
        rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 - 1;
        
        sprintf(str, "-%02d-%02d", g_SysSettingRTCTime.mon, g_SysSettingRTCTime.day);
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    }
    else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] == TIME_IDX)
    {
        GUI_SetFont(pNewFont2); 
        rect.x0 = x0 + PARALIST_UINT_LEN_X0; //X:376
        rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 - 1;

        sprintf(str, "%02d", g_SysSettingRTCTime.hour);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);

        //5寸屏：2 * NUMFONT_WIDTH_38
        //3.5寸屏：2 * NUMFONT_WIDTH_32
        #if (LCD_TYPE == LCD_5_TFT)
            rect.x0 = x0 + PARALIST_UINT_LEN_X0 + 2 * NUMFONT_WIDTH_38; //X:376
        #else
            rect.x0 = x0 + PARALIST_UINT_LEN_X0 + 2 * NUMFONT_WIDTH_32; //X:376
        #endif
        rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 - 1;
        
        sprintf(str, ":%02d:%02d", g_SysSettingRTCTime.minu, g_SysSettingRTCTime.sec);
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    }
}
#endif

#if (LCD_TYPE == LCD_28_TFT)
static void ChangeFocus_Date(void)
{
    uint8_t i;
    GUI_RECT rect;
    char str[32];
    uint8_t sU8Focus[2] = {0};
    
    sU8Focus[0] = g_sU8DateSettingFocus;
    sU8Focus[1] = g_sU8OldDateSettingFocus;
    
    GUI_SetFont(FONT_24);
    for (i = 0; i < 2; i++)
    {
        if (i == 0)
        {
            GUI_SetColor(FONTCOLOR_SELECTED);
            if (sU8Focus[i] == 0)
            {
                rect.x0 = 15 + PARALIST_UINT_TIME_X0; //X:376
                rect.y0 = 23 + 1 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + 4 * NUMFONT_WIDTH_24 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                
                GUI_SetColor(GREEN);
                GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
                
                GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);                
                sprintf(str, "%04d", g_SysSettingRTCTime.year);
                GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
            }
            else
            {
                
                rect.x0 = 15 + PARALIST_UINT_TIME_X0 + 4 * NUMFONT_WIDTH_24 + HLINE_FONT_WIDTH_24 + (NUMFONT_WIDTH_24 * 2 + HLINE_FONT_WIDTH_24) * (sU8Focus[i] - 1); //X:376
                rect.y0 = 23 + 1 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 2 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                
                GUI_SetColor(GREEN);
                GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
                
                GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);                
                if (g_sU8DateSettingFocus == 1)
                    sprintf(str, "%02d", g_SysSettingRTCTime.mon);
                else
                    sprintf(str, "%02d", g_SysSettingRTCTime.day);
                GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);                
            }
        }
        else
        {
            GUI_SetColor(GUI_WHITE);
            if (sU8Focus[i] == 0)
            {
                rect.x0 = 15 + PARALIST_UINT_TIME_X0; //X:376
                rect.y0 = 23 + 1 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + 4 * NUMFONT_WIDTH_24 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                
                GUI_SetColor(GREEN);
                GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
                
                GUI_SetColor(GUI_WHITE);
                sprintf(str, "%04d", g_SysSettingRTCTime.year);
                GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
            }
            else
            {
                rect.x0 = 15 + PARALIST_UINT_TIME_X0 + NUMFONT_WIDTH_24 * 4 + HLINE_FONT_WIDTH_24 + (NUMFONT_WIDTH_24 * 2 + HLINE_FONT_WIDTH_24) * (sU8Focus[i] - 1); //X:376
                rect.y0 = 23 + 1 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 2 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                
                GUI_SetColor(GREEN);
                GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
                
                GUI_SetColor(GUI_WHITE);
                
                if (g_sU8OldDateSettingFocus == 1)
                    sprintf(str, "%02d", g_SysSettingRTCTime.mon);
                else
                    sprintf(str, "%02d", g_SysSettingRTCTime.day);
                GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
            }
        }
    }
    
    g_sU8OldDateSettingFocus = g_sU8DateSettingFocus;
}
#else
static void ChangeFocus_Date(void)
{
    uint8_t i;
    GUI_RECT rect;
    char str[32];
    uint8_t sU8Focus[2] = {0};
    
    sU8Focus[0] = g_sU8DateSettingFocus;
    sU8Focus[1] = g_sU8OldDateSettingFocus;
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_38);
    #else
        GUI_SetFont(FONT_32);
    #endif

    for (i = 0; i < 2; i++)
    {
        if (i == 0)
        {
            GUI_SetColor(FONTCOLOR_SELECTED);
            if (sU8Focus[i] == 0)
            {
                //5寸屏：4 * NUMFONT_WIDTH_38
                //3.5寸屏：4 * NUMFONT_WIDTH_32
                #if (LCD_TYPE == LCD_5_TFT)
                    rect.x0 = 74 + PARALIST_UINT_LEN_X0; //X:376
                    rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = rect.x0 + 4 * NUMFONT_WIDTH_38 - 1;
                    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                #else
                    rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0; //X:376
                    rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = rect.x0 + 4 * NUMFONT_WIDTH_32 - 1;
                    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                #endif
                
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
                GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
                
                GUI_SetColor(FONTCOLOR_SELECTED);                
                sprintf(str, "%04d", g_SysSettingRTCTime.year);
                GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
            }
            else
            {
                #if (LCD_TYPE == LCD_5_TFT)
                    rect.x0 = 74 + PARALIST_UINT_LEN_X0 + 4 * NUMFONT_WIDTH_38 + HLINE_FONT_WIDTH_38 + (NUMFONT_WIDTH_38 * 2 + HLINE_FONT_WIDTH_38) * (sU8Focus[i] - 1); //X:376
                    rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = rect.x0 + NUMFONT_WIDTH_38 * 2 - 1;
                    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                #else
                    rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0 + 4 * NUMFONT_WIDTH_32 + HLINE_FONT_WIDTH_32 + (NUMFONT_WIDTH_32 * 2 + HLINE_FONT_WIDTH_32) * (sU8Focus[i] - 1); //X:376
                    rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 2 + 1;
                    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                #endif
                
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
                GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
                
                GUI_SetColor(FONTCOLOR_SELECTED);                
                if (g_sU8DateSettingFocus == 1)
                    sprintf(str, "%02d", g_SysSettingRTCTime.mon);
                else
                    sprintf(str, "%02d", g_SysSettingRTCTime.day);
                GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);                
            }
        }
        else
        {
            GUI_SetColor(GUI_WHITE);
            if (sU8Focus[i] == 0)
            {
                #if (LCD_TYPE == LCD_5_TFT)
                    rect.x0 = 74 + PARALIST_UINT_LEN_X0; //X:376
                    rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = rect.x0 + 4 * NUMFONT_WIDTH_38 - 1;
                    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                #else
                    rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0; //X:376
                    rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = rect.x0 + 4 * NUMFONT_WIDTH_32 - 1;
                    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                #endif
                
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
                GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
                
                GUI_SetColor(GUI_WHITE);
                sprintf(str, "%04d", g_SysSettingRTCTime.year);
                GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
            }
            else
            {
                #if (LCD_TYPE == LCD_5_TFT)
                    rect.x0 = 74 + PARALIST_UINT_LEN_X0 + NUMFONT_WIDTH_38 * 4 + HLINE_FONT_WIDTH_38 + (NUMFONT_WIDTH_38 * 2 + HLINE_FONT_WIDTH_38) * (sU8Focus[i] - 1); //X:376
                    rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = rect.x0 + 20 * 2 - 1;
                    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                #else
                    rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0 + NUMFONT_WIDTH_32 * 4 + HLINE_FONT_WIDTH_32 + (NUMFONT_WIDTH_32 * 2 + HLINE_FONT_WIDTH_32) * (sU8Focus[i] - 1); //X:376
                    rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 2 - 1;
                    rect.y1 = rect.y0 + LIST_HEIGHT - 1;
                #endif
                
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
                GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
                
                GUI_SetColor(GUI_WHITE);
                
                if (g_sU8OldDateSettingFocus == 1)
                    sprintf(str, "%02d", g_SysSettingRTCTime.mon);
                else
                    sprintf(str, "%02d", g_SysSettingRTCTime.day);
                GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
            }
        }
    }
    
    g_sU8OldDateSettingFocus = g_sU8DateSettingFocus;
}
#endif

#if (LCD_TYPE == LCD_28_TFT)
static void ChangeFocus_Time(void)
{
    uint8_t i;
    GUI_RECT rect;
    char str[32];
    uint8_t sU8Focus[2] = {0};
    
    sU8Focus[0] = g_sU8TimeSettingFocus;
    sU8Focus[1] = g_sU8OldTimeSettingFocus;
   
    GUI_SetFont(FONT_24);

    for (i = 0; i < 2; i++)
    {
        if (i == 0)
        {
            GUI_SetColor(FONTCOLOR_SELECTED);
            rect.x0 = 15 + PARALIST_UINT_TIME_X0 + (NUMFONT_WIDTH_24 * 2 + COLON_FONT_WIDTH_24) * sU8Focus[i]; //X:376
            rect.y0 = 23 + 2* EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
            rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 2 - 1;
            rect.y1 = rect.y0 + LIST_HEIGHT - 1;

            GUI_SetColor(GREEN);
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);

            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            if (g_sU8TimeSettingFocus == 0)
                sprintf(str, "%02d", g_SysSettingRTCTime.hour);
            else if (g_sU8TimeSettingFocus == 1)
                sprintf(str, "%02d", g_SysSettingRTCTime.minu);
            else
                sprintf(str, "%02d", g_SysSettingRTCTime.sec);
            GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
        }
        else
        {
            GUI_SetColor(GUI_WHITE);

            rect.x0 = 15 + PARALIST_UINT_TIME_X0 + (NUMFONT_WIDTH_24 * 2 + COLON_FONT_WIDTH_24) * sU8Focus[i]; //X:376
            rect.y0 = 23 + 2 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
            rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 2 - 1;
            rect.y1 = rect.y0 + LIST_HEIGHT - 1;

            GUI_SetColor(GREEN);
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);

            GUI_SetColor(GUI_WHITE);
            if (g_sU8OldTimeSettingFocus == 0)
                sprintf(str, "%02d", g_SysSettingRTCTime.hour);
            else if (g_sU8OldTimeSettingFocus == 1)
                sprintf(str, "%02d", g_SysSettingRTCTime.minu);
            else
                sprintf(str, "%02d", g_SysSettingRTCTime.sec);
            GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
        }
    }

    g_sU8OldTimeSettingFocus = g_sU8TimeSettingFocus;
}
#else
static void ChangeFocus_Time(void)
{
    uint8_t i;
    GUI_RECT rect;
    char str[32];
    uint8_t sU8Focus[2] = {0};

    sU8Focus[0] = g_sU8TimeSettingFocus;
    sU8Focus[1] = g_sU8OldTimeSettingFocus;
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_38);
    #else
        GUI_SetFont(FONT_32);
    #endif

    for (i = 0; i < 2; i++)
    {
        if (i == 0)
        {
            GUI_SetColor(FONTCOLOR_SELECTED);

            #if (LCD_TYPE == LCD_5_TFT)
                rect.x0 = 74 + PARALIST_UINT_LEN_X0 + (NUMFONT_WIDTH_38 * 2 + COLON_FONT_WIDTH_38) * sU8Focus[i]; //X:376
                rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_38 * 2 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #else
                rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0 + (NUMFONT_WIDTH_32 * 2 + COLON_FONT_WIDTH_32) * sU8Focus[i]; //X:376
                rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 2 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #endif

            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);

            GUI_SetColor(FONTCOLOR_SELECTED);
            if (g_sU8TimeSettingFocus == 0)
                sprintf(str, "%02d", g_SysSettingRTCTime.hour);
            else if (g_sU8TimeSettingFocus == 1)
                sprintf(str, "%02d", g_SysSettingRTCTime.minu);
            else
                sprintf(str, "%02d", g_SysSettingRTCTime.sec);
            GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
        }
        else
        {
            GUI_SetColor(GUI_WHITE);

            #if (LCD_TYPE == LCD_5_TFT)
                rect.x0 = 74 + PARALIST_UINT_LEN_X0 + (NUMFONT_WIDTH_38 * 2 + COLON_FONT_WIDTH_38) * sU8Focus[i]; //X:376
                rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_38 * 2 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #else
                rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0 + (NUMFONT_WIDTH_32 * 2 + COLON_FONT_WIDTH_32) * sU8Focus[i]; //X:376
                rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 2 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #endif

            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);

            GUI_SetColor(GUI_WHITE);
            if (g_sU8OldTimeSettingFocus == 0)
                sprintf(str, "%02d", g_SysSettingRTCTime.hour);
            else if (g_sU8OldTimeSettingFocus == 1)
                sprintf(str, "%02d", g_SysSettingRTCTime.minu);
            else
                sprintf(str, "%02d", g_SysSettingRTCTime.sec);
            GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
        }
    }
    
    g_sU8OldTimeSettingFocus = g_sU8TimeSettingFocus;
}
#endif

//设置选项中--中间数值
#if (LCD_TYPE == LCD_28_TFT)
static void EditDateTimeValue(void)
{
    GUI_RECT rect;
    char str[32];

    GUI_SetFont(FONT_24);

    if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == DATE_IDX)
    {
        if(g_sU8DateSettingFocus == 0)
        {
            rect.x0 = 15 + PARALIST_UINT_TIME_X0;
            rect.y0 = 23 + EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
            rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 4 - 1;
            rect.y1 = rect.y0 + LIST_HEIGHT - 1;

            GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);

            GUI_SetColor(FONTCOLOR_SELECTED);
            sprintf(str, "%04d", g_SysSettingRTCTime.year);
            GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
        }
        else
        {
            rect.x0 = 15 + PARALIST_UINT_TIME_X0 + NUMFONT_WIDTH_24 * 4 + HLINE_FONT_WIDTH_24 + (NUMFONT_WIDTH_24 * 2 + HLINE_FONT_WIDTH_24) * (g_sU8DateSettingFocus - 1); //X:376
            rect.y0 = 23 + EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
            rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 2 - 1;
            rect.y1 = rect.y0 + LIST_HEIGHT - 1;

            GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);

            GUI_SetColor(FONTCOLOR_SELECTED);
            if (g_sU8DateSettingFocus == 1)
                sprintf(str, "%02d", g_SysSettingRTCTime.mon);
            else
                sprintf(str, "%02d", g_SysSettingRTCTime.day);
            GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
        }
    }
    else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == TIME_IDX)
    {
        rect.x0 = 15 + PARALIST_UINT_TIME_X0 + (NUMFONT_WIDTH_24 * 2 + COLON_FONT_WIDTH_24) * g_sU8TimeSettingFocus; //X:376
        rect.y0 = 23 + EACHLIST_HEIGHT*2 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 2 - 1;
        rect.y1 = rect.y0 + LIST_HEIGHT - 1;

        GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
        GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
        
        GUI_SetColor(FONTCOLOR_SELECTED);
        if (g_sU8TimeSettingFocus == 0)
            sprintf(str, "%02d", g_SysSettingRTCTime.hour);
        else if (g_sU8TimeSettingFocus == 1)
            sprintf(str, "%02d", g_SysSettingRTCTime.minu);
        else
            sprintf(str, "%02d", g_SysSettingRTCTime.sec);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    }
}
#else
static void EditDateTimeValue(void)
{
    GUI_RECT rect;
    char str[32];

    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_38);
    #else
        GUI_SetFont(FONT_32);
    #endif

    if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] == DATE_IDX)
    {
        if(g_sU8DateSettingFocus == 0)
        {
            #if (LCD_TYPE == LCD_5_TFT)
                rect.x0 = 74 + PARALIST_UINT_LEN_X0; //X:376
                rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_38 * 4 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #else
                rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0; //X:376
                rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 4 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #endif

            GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);

            GUI_SetColor(FONTCOLOR_SELECTED);
            sprintf(str, "%04d", g_SysSettingRTCTime.year);
            GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
        }
        else
        {
            #if (LCD_TYPE == LCD_5_TFT)
                rect.x0 = 74 + PARALIST_UINT_LEN_X0 + NUMFONT_WIDTH_38 * 4 + HLINE_FONT_WIDTH_38 + (NUMFONT_WIDTH_38 * 2 + HLINE_FONT_WIDTH_38) * (g_sU8DateSettingFocus - 1); //X:376
                rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_38 * 2 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #else
                rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0 + NUMFONT_WIDTH_32 * 4 + HLINE_FONT_WIDTH_32 + (NUMFONT_WIDTH_32 * 2 + HLINE_FONT_WIDTH_32) * (g_sU8DateSettingFocus - 1); //X:376
                rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 2 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #endif

            GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);

            GUI_SetColor(FONTCOLOR_SELECTED);
            if (g_sU8DateSettingFocus == 1)
                sprintf(str, "%02d", g_SysSettingRTCTime.mon);
            else
                sprintf(str, "%02d", g_SysSettingRTCTime.day);
            GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
        }
    }
    else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] == TIME_IDX)
    {
        #if (LCD_TYPE == LCD_5_TFT)
            rect.x0 = 74 + PARALIST_UINT_LEN_X0 + (NUMFONT_WIDTH_38 * 2 + COLON_FONT_WIDTH_38) * g_sU8TimeSettingFocus; //X:376
            rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
            rect.x1 = rect.x0 + NUMFONT_WIDTH_38 * 2 - 1;
            rect.y1 = rect.y0 + LIST_HEIGHT - 1;
        #else
            rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0 + (NUMFONT_WIDTH_32 * 2 + COLON_FONT_WIDTH_32) * g_sU8TimeSettingFocus; //X:376
            rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
            rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 2 - 1;
            rect.y1 = rect.y0 + LIST_HEIGHT - 1;
        #endif

        GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
        GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
        
        GUI_SetColor(FONTCOLOR_SELECTED);
        if (g_sU8TimeSettingFocus == 0)
            sprintf(str, "%02d", g_SysSettingRTCTime.hour);
        else if (g_sU8TimeSettingFocus == 1)
            sprintf(str, "%02d", g_SysSettingRTCTime.minu);
        else
            sprintf(str, "%02d", g_SysSettingRTCTime.sec);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    }
}
#endif

static uint8_t CalEachMonthDays(void)
{
    uint8_t u8MaxDays = 0;
    
    switch (g_SysSettingRTCTime.mon)
    {
        case 2:
            u8MaxDays = LEAP_YEAR(g_SysSettingRTCTime.year) ? SYS_PARA_DATE_DAY_MAX_29 : SYS_PARA_DATE_DAY_MAX_28;
            break;
        case 4:
        case 6:
        case 9:
        case 11:
            u8MaxDays = SYS_PARA_DATE_DAY_MAX_30;
            break;
        case 1:
        case 3:
        case 5:
        case 7:
        case 8:
        case 10:
        case 12:
            u8MaxDays = SYS_PARA_DATE_DAY_MAX_31;
            break;
        default:
            u8MaxDays = SYS_PARA_DATE_DAY_MAX_31;
        break;
    }
    return u8MaxDays;
}

static void DrawingOptionsDateTime(uint8_t IsLeft)
{
    GUI_RECT rect;
    char str[32];

    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_38);
    #else
		#if (LCD_TYPE == LCD_28_TFT)
        	GUI_SetFont(FONT_24);
		#else
	        GUI_SetFont(FONT_32);
		#endif
    #endif
    if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == DATE_IDX)    //"����"
    {
        switch (g_sU8DateSettingFocus)
        {
            case 0: //年
            {
                g_SysSettingRTCTime.year = __ShiftValue(IsLeft
                                                , g_SysSettingRTCTime.year
                                                , SYS_PARA_DATE_YEAR_MIN
                                                , SYS_PARA_DATE_YEAR_MAX
                                                , SYS_PARA_STEP_1);
                sprintf(str, "%04d", g_SysSettingRTCTime.year);

                break;
            }
            case 1: //月
            {
                g_SysSettingRTCTime.mon = __ShiftValue(IsLeft
                                                , g_SysSettingRTCTime.mon
                                                , SYS_PARA_DATE_MONTH_MIN
                                                , SYS_PARA_DATE_MONTH_MAX
                                                , SYS_PARA_STEP_1);
                sprintf(str, "%02d", g_SysSettingRTCTime.mon);

                break;
            }
            case 2: //日
            {
                g_SysSettingRTCTime.day = __ShiftValue(IsLeft
                                                , g_SysSettingRTCTime.day
                                                , SYS_PARA_DATE_DAY_MIN
                                                , CalEachMonthDays()
                                                , SYS_PARA_STEP_1);
                sprintf(str, "%02d", g_SysSettingRTCTime.day);

                break;
            }
        }
        GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
        if (g_sU8DateSettingFocus == 0)
        {
            #if (LCD_TYPE == LCD_5_TFT)
                rect.x0 = 74 + PARALIST_UINT_LEN_X0; //X:376
                rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_38 * 4 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #else
				#if (LCD_TYPE == LCD_28_TFT)
	                rect.x0 = 15 + PARALIST_UINT_TIME_X0; //X:376
	                rect.y0 = 23+EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
	                rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 4 - 1;
	                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
				#else
	                rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0; //X:376
	                rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
	                rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 4 - 1;
	                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
				#endif
            #endif

            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
        }
        else
        {
            #if (LCD_TYPE == LCD_5_TFT)
                rect.x0 = 74 + PARALIST_UINT_LEN_X0 + NUMFONT_WIDTH_38 * 4 + HLINE_FONT_WIDTH_38 + (NUMFONT_WIDTH_38 * 2 + HLINE_FONT_WIDTH_38) * (g_sU8DateSettingFocus - 1); //X:376
                rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + NUMFONT_WIDTH_38 * 2 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
            #else
				#if (LCD_TYPE == LCD_28_TFT)
					rect.x0 = 15 + PARALIST_UINT_TIME_X0 + NUMFONT_WIDTH_24 * 4 + HLINE_FONT_WIDTH_24 + (NUMFONT_WIDTH_24 * 2 + HLINE_FONT_WIDTH_24) * (g_sU8DateSettingFocus - 1); //X:376
	                rect.y0 = 23+EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
	                rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 2 - 1;
	                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
				#else
	                rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0 + NUMFONT_WIDTH_32 * 4 + HLINE_FONT_WIDTH_32 + (NUMFONT_WIDTH_32 * 2 + HLINE_FONT_WIDTH_32) * (g_sU8DateSettingFocus - 1); //X:376
	                rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
	                rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 2 - 1;
	                rect.y1 = rect.y0 + LIST_HEIGHT - 1;
				#endif
            #endif

            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);   
        }
        GUI_SetColor(FONTCOLOR_SELECTED);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    }
    else if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == TIME_IDX)   //"ʱ��"
    {
        switch (g_sU8TimeSettingFocus)
        {
            case 0: //时
            {
                g_SysSettingRTCTime.hour = __ShiftValue(IsLeft
                                                , g_SysSettingRTCTime.hour
                                                , SYS_PARA_TIME_HOUR_MIN
                                                , SYS_PARA_TIME_HOUR_MAX
                                                , SYS_PARA_STEP_1);
                sprintf(str, "%02d", g_SysSettingRTCTime.hour);
                break;
            }
            case 1: //分
            {
                g_SysSettingRTCTime.minu = __ShiftValue(IsLeft
                                                , g_SysSettingRTCTime.minu
                                                , SYS_PARA_TIME_MINUTE_MIN
                                                , SYS_PARA_TIME_MINUTE_MAX
                                                , SYS_PARA_STEP_1);
                sprintf(str, "%02d", g_SysSettingRTCTime.minu);
                break;
            }
            case 2: //秒
            {
                g_SysSettingRTCTime.sec = __ShiftValue(IsLeft
                                                , g_SysSettingRTCTime.sec
                                                , SYS_PARA_TIME_SEC_MIN
                                                , SYS_PARA_TIME_SEC_MAX
                                                , SYS_PARA_STEP_1);
                sprintf(str, "%02d", g_SysSettingRTCTime.sec);
                break;
            }
        }

        #if (LCD_TYPE == LCD_5_TFT)
            rect.x0 = 74 + PARALIST_UINT_LEN_X0 + (NUMFONT_WIDTH_38 * 2 + COLON_FONT_WIDTH_38) * g_sU8TimeSettingFocus; //X:376
            rect.y0 = 3 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
            rect.x1 = rect.x0 + NUMFONT_WIDTH_38 * 2 - 1;
            rect.y1 = rect.y0 + LIST_HEIGHT - 1;
        #else
			#if (LCD_TYPE == LCD_28_TFT)
				rect.x0 = 15 + PARALIST_UINT_TIME_X0 + (NUMFONT_WIDTH_24 * 2 + COLON_FONT_WIDTH_24) * g_sU8TimeSettingFocus;; //X:376
	            rect.y0 = 23+2*EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
	            rect.x1 = rect.x0 + NUMFONT_WIDTH_24 * 2 - 1;
	            rect.y1 = rect.y0 + LIST_HEIGHT - 1;
			#else
	            rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0 + (NUMFONT_WIDTH_32 * 2 + COLON_FONT_WIDTH_32) * g_sU8TimeSettingFocus; //X:376
	            rect.y0 = 2 + (g_SysSettingScreenWInfo.U8CurFocusID % 5) * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
	            rect.x1 = rect.x0 + NUMFONT_WIDTH_32 * 2 - 1;
	            rect.y1 = rect.y0 + LIST_HEIGHT - 1;
			#endif
        #endif

        GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
        GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
        
        GUI_SetColor(FONTCOLOR_SELECTED);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_VCENTER);
    }
}

static void DrawOptionSysSettingKeyLeft(void)
{
    DrawingOptions(TRUE
                 , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                 , g_SysSettingScreenWInfo.U8CurFocusID);
}

static void DrawOptionSysSettingKeyRight(void)
{
    DrawingOptions(FALSE
                 , IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE]
                 , g_SysSettingScreenWInfo.U8CurFocusID);
}

#if (LCD_TYPE == LCD_28_TFT)
void RefreshSystemMenuDateTime(void)
{
    U8 sU8Page = 0;
    RTC_TimeTypeDef1 RTC_Time;    
    //if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] != DATE_IDX && IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] != TIME_IDX)    
    {
        UpdateEdfDataAfterTenSecond();
        sU8Page = (g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE);
        if(sU8Page == 0)
        {
        //Start刷新：当连接加温管路的时候，管路类型显示"加热管路"，否则显示"19mm管路"
//            WM_SelectWindow(WM_HBKWIN);
            if (GetPipeConnectFlag())
               g_ConfigSave.SetParameter(TUBETYPE, 2);
            else
               g_ConfigSave.SetParameter(TUBETYPE, 1);
            
            if (g_U8LastPipeType != g_ConfigSave.GetParameter(TUBETYPE))
            {
                g_U8LastPipeType = g_ConfigSave.GetParameter(TUBETYPE);
                if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == TUBESELECTION_IDX)
                {
                    GUI_SetColor(GREEN);
                    DrawRoundedRect(5,10 + STATEBAR_TITLE_HEIGHT,235,60 + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);
                    if(g_U8LastPipeType == 2)
                    SetUiItemText(15
                                  , 28 + STATEBAR_TITLE_HEIGHT
                                  , TUBESELECTION_IDX
                                  , GUI_WHITE
                                  , FONT_18
                                  , FONT_18
                                  , g_sU8CurEditState_SysSetting,FALSE);
                    else
                     SetUiItemText(15
                                  , 28 + STATEBAR_TITLE_HEIGHT
                                  , TUBESELECTION_IDX
                                  , GUI_WHITE
                                  , FONT_18
                                  , FONT_18
                                  , g_sU8CurEditState_SysSetting,FALSE);
                }
                else
                {
                    GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                    DrawRoundedRect(5,10 + STATEBAR_TITLE_HEIGHT,235,60 + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);
                    if(g_U8LastPipeType == 2)
                    SetUiItemText(15
                                  , 28 + STATEBAR_TITLE_HEIGHT
                                  , TUBESELECTION_IDX
                                  , GUI_WHITE
                                  , FONT_18
                                  , FONT_18
                                  , g_sU8CurEditState_SysSetting,FALSE);
                    else
                     SetUiItemText(15
                                  , 28 + STATEBAR_TITLE_HEIGHT
                                  , TUBESELECTION_IDX
                                  , GUI_WHITE
                                  , FONT_18
                                  , FONT_18
                                  , g_sU8CurEditState_SysSetting,FALSE);
                }
            }
            //===================结束刷新管路类型====================//
        }
        else if(sU8Page == 1)
        {
            if (g_sU8EnterTimeEditState == FALSE)
            {
//                WM_SelectWindow(WM_HBKWIN);	
                RTC_ReadTime(&RTC_Time);    
                g_OldSysSettingRTCTime.hour = RTC_Time.hour;
                g_OldSysSettingRTCTime.minu = RTC_Time.minu;
                g_OldSysSettingRTCTime.sec = RTC_Time.sec;
                
                g_SysSettingRTCTime.hour = RTC_Time.hour;
                g_SysSettingRTCTime.minu = RTC_Time.minu;
                g_SysSettingRTCTime.sec = RTC_Time.sec;
                
                if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == TIME_IDX)
                {
                    DrawRoundedRect(5,10+58*2 + STATEBAR_TITLE_HEIGHT,235,60+58*2 + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);                            
                }
                else
                {
                    DrawRoundedRect(5,10+58*2 + STATEBAR_TITLE_HEIGHT,235,60+58*2 + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);                            
                } 
                
                   DrawDateTime(15
                                     , 23+EACHLIST_HEIGHT*2
                                     , TIME_IDX
                                     , GUI_WHITE
                                     , FONT_18
                                     , FONT_24
                                     , g_sU8CurEditState_SysSetting,2);
            }
        }
        else if(sU8Page == 4)
        {
            if (GetWifiFlag() == 0 || g_ConfigSave.GetWifiConfig()->Switch == 0)
            {
                return;
            }
            if (g_U8WifiConnectStatus == g_WifiUartModule.GetConnectStatus())
            {
                return;
            }
            g_U8WifiConnectStatus = g_WifiUartModule.GetConnectStatus();
        
            if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / MAX_PAGE_LINE][g_SysSettingScreenWInfo.U8CurFocusID % MAX_PAGE_LINE] == WIFI_SELECT_IDX)
            {
                DrawRoundedRect(5,10 + STATEBAR_TITLE_HEIGHT,235,60 + STATEBAR_TITLE_HEIGHT,ROUNDED,GREEN);                            
            }
            else
            {
                DrawRoundedRect(5,10 + STATEBAR_TITLE_HEIGHT,235,60 + STATEBAR_TITLE_HEIGHT,ROUNDED,DEFAULT_MENU_FOCUSCOLOR);                            
            }  
            SetUiItemText(15
                          , 28 + STATEBAR_TITLE_HEIGHT
                          , WIFI_SELECT_IDX
                          , GUI_WHITE
                          , FONT_18
                          , FONT_18
                          , FALSE,FALSE);
        }
        else
        {
            return;
        }    
    }
}
#else
void RefreshSystemMenuDateTime(void)
{
	char str[16];
    GUI_RECT rect;
    const GUI_FONT* pFont;
    GUI_COLOR Color;
    U8 sU8Page = 0;
    //if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] != DATE_IDX && IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] != TIME_IDX)    
    {
        UpdateEdfDataAfterTenSecond();
        sU8Page = (g_SysSettingScreenWInfo.U8CurFocusID / 5);
        switch (sU8Page)
        {
        case 0:         //自动刷新日期   
            //Start刷新：当连接加温管路的时候，管路类型显示"加热管路"，否则显示"19mm管路"
//			WM_SelectWindow(WM_HBKWIN);
            if (GetPipeConnectFlag())
               g_ConfigSave.SetParameter(TUBETYPE, 2);
            else
               g_ConfigSave.SetParameter(TUBETYPE, 1);
            
            if (g_U8LastPipeType != g_ConfigSave.GetParameter(TUBETYPE))
            {
                g_U8LastPipeType = g_ConfigSave.GetParameter(TUBETYPE);
                if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] == TUBESELECTION_IDX)
                {
                    #if (LCD_TYPE == LCD_5_TFT)    
                        EditUiItemValueMaskSet(74
                                                , 3 + STATEBAR_TITLE_HEIGHT
                                                , GetMultiLanguageString(IdType_SysTubeType[g_ConfigSave.GetParameter(TUBETYPE)])
                                                , GUI_WHITE
                                                , MAINSCREEN_MENU_BACKCOLOR_SELECTED
                                                , FONT_32);
                    #else
                        EditUiItemValueMaskSet(PARALIST_TITLE_LEFT_MARGIN
                                                , 2 + STATEBAR_TITLE_HEIGHT
                                                , GetMultiLanguageString(IdType_SysTubeType[g_ConfigSave.GetParameter(TUBETYPE)])
                                                , GUI_WHITE
                                                , MAINSCREEN_MENU_BACKCOLOR_SELECTED
                                                , FONT_24);
                    #endif
                }
                else
                {
                    #if (LCD_TYPE == LCD_5_TFT)
                        EditUiItemValueMaskSet(74
                                                , 3 + STATEBAR_TITLE_HEIGHT
                                                , GetMultiLanguageString(IdType_SysTubeType[g_ConfigSave.GetParameter(TUBETYPE)])
                                                , GUI_WHITE
                                                , BACKCOLOR_DEFAULT
                                                , FONT_32);
                    #else
                        EditUiItemValueMaskSet(PARALIST_TITLE_LEFT_MARGIN
                                                , 2 + STATEBAR_TITLE_HEIGHT
                                                , GetMultiLanguageString(IdType_SysTubeType[g_ConfigSave.GetParameter(TUBETYPE)])
                                                , GUI_WHITE
                                                , BACKCOLOR_DEFAULT
                                                , FONT_24);
                    #endif
                }
            }
            //===================结束刷新管路类型====================//
            
            if (g_sU8EnterDateEditState)
            {
                return;
            }
//            WM_SelectWindow(WM_HBKWIN);
            RTC_ReadTime(&g_OldSysSettingRTCTime);    
            g_SysSettingRTCTime = g_OldSysSettingRTCTime;
            if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] == DATE_IDX)
            {
                Color = MAINSCREEN_MENU_BACKCOLOR_SELECTED;
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
            }
            else
            {
                Color = BACKCOLOR_DEFAULT;
                GUI_SetColor(BACKCOLOR_DEFAULT);
            }
            #if (LCD_TYPE == LCD_5_TFT)
                pFont = FONT_32;
                rect.x0 = 74 + PARALIST_UINT_LEN_X0;
                rect.y0 = 3 + 4 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1 - 1;

            #else
                pFont = FONT_24;
                rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0;
                rect.y0 = 3 + 4 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1 - 1;
            #endif
            
            sprintf(str, "%04d-%02d-%02d", g_SysSettingRTCTime.year, g_SysSettingRTCTime.mon, g_SysSettingRTCTime.day);     
            DisplayNeedUpdateStr(g_sI8SysMenuDisplayDateStr, str, pFont, rect, Color, GUI_WHITE, GUI_TA_LEFT | GUI_TA_VCENTER);
            break;
        case 1:         //自动刷新时间
            if (g_sU8EnterTimeEditState)
            {
                return;
            }
//            WM_SelectWindow(WM_HBKWIN);            
            RTC_ReadTime(&g_OldSysSettingRTCTime);
            g_SysSettingRTCTime = g_OldSysSettingRTCTime;
            if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] == TIME_IDX)
            {
                Color = MAINSCREEN_MENU_BACKCOLOR_SELECTED;
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
            }
            else
            {
                Color = BACKCOLOR_DEFAULT;
                GUI_SetColor(BACKCOLOR_DEFAULT);
            }           
            #if (LCD_TYPE == LCD_5_TFT)
                pFont = FONT_32;
                rect.x0 = 74 + PARALIST_UINT_LEN_X0;
                rect.y0 = 3 + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1 - 1;
            #else
                pFont = FONT_24;
                rect.x0 = PARALIST_TITLE_LEFT_MARGIN + PARALIST_UINT_LEN_X0;
                rect.y0 = 3 + STATEBAR_TITLE_HEIGHT;
                rect.x1 = rect.x0 + PARALIST_UINT_LEN_X1 - 1;
                rect.y1 = rect.y0 + LIST_HEIGHT - 1 - 1;
            #endif
            
            sprintf(str, "%02d:%02d:%02d", g_SysSettingRTCTime.hour, g_SysSettingRTCTime.minu, g_SysSettingRTCTime.sec);
            DisplayNeedUpdateStr(g_sI8SysMenuDisplayTimeStr, str, pFont, rect, Color, GUI_WHITE, GUI_TA_LEFT | GUI_TA_VCENTER);//系统设置界面的时间设置刷新
            break;
        case 2:
//5'',3.5''需要考虑在系统设置菜单界面，可以更新WIFI连接状态            
            if (GetWifiFlag() == 0 || g_ConfigSave.GetWifiConfig()->Switch == 0)
            {
                return;
            }
            if (g_U8WifiConnectStatus == g_WifiUartModule.GetConnectStatus())
            {
                return;
            }
            g_U8WifiConnectStatus = g_WifiUartModule.GetConnectStatus();
//            WM_SelectWindow(WM_HBKWIN);
        
            if (IdType_Sys[g_SysSettingScreenWInfo.U8CurFocusID / 5][g_SysSettingScreenWInfo.U8CurFocusID % 5] == WIFI_SELECT_IDX)
            {
                Color = MAINSCREEN_MENU_BACKCOLOR_SELECTED;
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
            }
            else
            {
                Color = BACKCOLOR_DEFAULT;
                GUI_SetColor(BACKCOLOR_DEFAULT);
            }          
            #if (LCD_TYPE == LCD_5_TFT)
            GUI_FillRect(74, 3 + 2 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT, 788, 3 * EACHLIST_HEIGHT - 2 + STATEBAR_TITLE_HEIGHT);
            #else
            GUI_FillRect(PARALIST_TITLE_LEFT_MARGIN, 2 + 2 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT, 472, 3 * EACHLIST_HEIGHT - 2 + STATEBAR_TITLE_HEIGHT);
            #endif
            
#if (LCD_TYPE == LCD_5_TFT)
           SetUiItemText(74, 3 + 2 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                           , IdType_Sys[2][2]
                           , GUI_WHITE
                           , FONT_32
                           , FONT_32
                           , FALSE);
#endif
#if (LCD_TYPE == LCD_35_TFT)
           SetUiItemText(PARALIST_TITLE_LEFT_MARGIN , 2 + 2 * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                          , IdType_Sys[2][2]
                                          , GUI_WHITE
                                          , FONT_24
                                          , FONT_24
                                          , FALSE);
#endif
        }
    }
}
#endif

void ShowSysSettingScreen(uint8_t Flag)
{
    if (Flag == 1)
    {
        g_SysSettingScreenWInfo.U8CurFocusID = 0;
        g_SysSettingScreenWInfo.U8OldFocusID = 0;
        g_sU8CurEditState_SysSetting = FALSE;
        g_sU8CurEditState_DateSetting = FALSE;
        g_sU8CurEditState_TimeSetting = FALSE;

        g_sU8DateSettingFocus = 0;
        g_sU8OldDateSettingFocus = 0;

        g_sU8TimeSettingFocus = 0;
        g_sU8OldTimeSettingFocus = 0;

        g_sU8EnterDateEditState = FALSE;
        g_sU8EnterTimeEditState = FALSE;

        ChangeIdx = CHECKING_IDX;

        g_U8WifiConnectStatus = g_WifiUartModule.GetConnectStatus();
        if (GetPipeConnectFlag())
            g_ConfigSave.SetParameter(TUBETYPE, 2);
        else
            g_ConfigSave.SetParameter(TUBETYPE, 1);
        //检测是否有Wifi模块，并计算"系统设置"界面的最大焦点号
        ScanningWifiCalSysSettingMaxFocus();        
        //g_SysSettingScreenWInfo.U8CurFocusID = 0;
        g_sU8CurEditState_SysSetting = FALSE;
        RTC_ReadTime(&g_SysSettingRTCTime);
        g_OldSysSettingRTCTime = g_SysSettingRTCTime;        
        ScanningWifiConnectedStatus();
        UpdateTopStateBarMenuString(GetMultiLanguageString(SYSSETTING_IDX));
        g_U8CurEditState_ForPowerON = FALSE;
//        RepaintSysSettingScreen();
        g_WindowDrv.PushWindow(&g_SysSettingScreenWInfo);
    }
    else if (Flag == 2)
    {
    }
    else
    {
        UpdateTopStateBarMenuString(GetMultiLanguageString(MENU_IDX));
        g_WindowDrv.PopWindow(&g_SysSettingScreenWInfo);
    }
    UpdateIconStatus(DISPLAY_LOCKED_ICON_INDICATION_BIT, !Flag);
}

/*****************************************************************************
 函 数 名  : UpdateConsumTime
 功能描述  : 更新管路等耗材时间 
 输入参数  : Rtc_Time系统当前时间 
 输出参数  : 无
 调用函数  : 
 修改历史      :
  1.日    期   : 
    作    者   :CCK
    修改内容   :
*****************************************************************************/
static void UpdateConsumTime(RTC_TimeTypeDef1 Rtc_Time,bool bChangeFlag)
{
	uint32_t sU32Days = 0;
//	if(!bChangeFlag)
//	{
//		g_ConfigSave.SetParameter(FILCHANGETIME, GetCurrentDateTime() / (60 * 60 * 24), 0);
//		g_ConfigSave.SetParameter(MASKCHANGETIME, GetCurrentDateTime() / (60 * 60 * 24), 0);
//		g_ConfigSave.SetParameter(TUBECHANGETIME, GetCurrentDateTime() / (60 * 60 * 24));
//	}
	 sU32Days = GetDateTimeValue(&Rtc_Time) / (60 * 60 * 24);
	//sU32Days += g_ConfigSave.GetParameter(FILCHANGETIME);
	g_ConfigSave.SetParameter(FILCHANGETIME,sU32Days, 0);
    g_ConfigSave.SetParameter(MASKCHANGETIME,sU32Days, 0);
    g_ConfigSave.SetParameter(TUBECHANGETIME,sU32Days);
}
