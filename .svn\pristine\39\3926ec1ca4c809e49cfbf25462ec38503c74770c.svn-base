const char* const lang_english[] =
{
	// 0 - STR_BASE
	".",
	// 1 - SIMPLIFIEDCHINESE_IDX
	"\xE7\xAE\x80\xE4\xBD\x93\xE4\xB8\xAD\xE6\x96\x87",
	// 2 - ENGLISH_IDX
	"English",
	// 3 - ESPANOL_IDX
	"Espa\xC3\xB1""ol",
	// 4 - ARABIC_IDX
	"\xD8\xA7\xD9\x84\xD8\xB9\xD8\xB1\xD8\xA8\xD9\x8A\xD8\xA9",
	// 5 - TURKISH_IDX
	"T\xC3\xBC""rk\xC3\xA7""e",
	// 6 - PORTUGUESE_IDX
	"Portugu\xC3\xAA""s",
	// 7 - THAI_IDX
	"\xE0\xB8\xA0\xE0\xB8\xB2\xE0\xB8\xA9\xE0\xB8\xB2\xE0\xB9\x84\xE0\xB8\x97\xE0\xB8\xA2",
	// 8 - BULGARRIAN_IDX
	"\xD0\x91\xD1\x8A\xD0\xBB\xD0\xB3\xD0\xB0\xD1\x80\xD1\x81\xD0\xBA\xD0\xB8",
	// 9 - UKRAINIAN_IDX
	"\xD0\xA3\xD0\xBA\xD1\x80\xD0\xB0\xD1\x97\xD0\xBD\xD1\x81\xD1\x8C\xD0\xBA\xD0\xB0",
	// 10 - RUSSIAN_IDX
	"\xD0\xA0\xD1\x83\xD1\x81\xD1\x81\xD0\xBA\xD0\xB8\xD0\xB9",
	// 11 - CHECKING_IDX
	"Checking......",
	// 12 - CHECKERROR_IDX
	"Check Error.",
	// 13 - CHECKOK_IDX
	"Check OK.",
	// 14 - SDRAM_ERROR_IDX
	"SDRAM,",
	// 15 - MOTOR_ERROR_IDX
	"Motor,",
	// 16 - HUMI_TEMP_SENSOR_ERROR_IDX
	"Temp Sensor,",
	// 17 - FLOW_SENSOR_ERROR_IDX
	"Flow Sensor,",
	// 18 - SPI_FLASH_ERROR_IDX
	"SPI FLASH,",
	// 19 - EEPROM_ERROR_IDX
	"EEPROM,",
	// 20 - RTC_ERROR_IDX  
	"RTC,",
	// 21 - YLCGQ_REF_ERROR_IDX
	"Pressure module,",
	// 22 - MOTOR_POWER_24V_ERROR_IDX
	"Motor 24V Power,",
	// 23 - ALM_POWERFAULT_IDX
	"Motor Failure!!!",
	// 24 - ALM_HIPRRESS_IDX
	"High Pressure!!!",
	// 25 - ALM_LOWPRESS_IDX
	"Low Pressure!!!",
	// 26 - ALM_LOWLEAK_IDX
	"Airway Block!!!",
	// 27 - ALM_HIGHLEAK_IDX
	"High Leak!!!",
	// 28 - ALM_LOWMV_IDX
	"Low MV!!!",
	// 29 - ALM_LOWVT_IDX
	"Low VT!!!",
	// 30 - ALM_APNEA_IDX
	"Apnea!!!",
	// 31 - ALM_HUMFAILURE_IDX
	"Humidifier Failure!!!",
	// 32 - ALM_HUMFAULT_IDX
	"Humidifier Failure.!!!",
	// 33 - ALM_HEATPIPEFAILURE_IDX
	"Heating Tube Failure!!!",
	// 34 - ALM_FANOVERHEADT_IDX
	"Motor Failure.!!!",
	// 35 - ALM_LOWVOLTAGE_IDX
	"Low Voltage!!!",
	// 36 - ALM_PRESSURE_ABNORMAL_IDX
	"Pressure sensor Abnormal!!!",
	// 37 - ALM_SDFULL_IDX
	"SD Card Full",
	// 38 - MONITORVIEW_IDX
	"Monitor View",
	// 39 - PFCURVE_IDX
	"Wave View",
	// 40 - PARASETTING_IDX
	"Parameter",
	// 41 - SYSSETTING_IDX
	"System",
	// 42 - ALARMSETTING_IDX
	"Alarm",
	// 43 - INFOSETTING_IDX
	"Info",
	// 44 - PREHEATSETTING_IDX
	"Preheat",
	// 45 - QUICKSET_IDX
	"QuickSet",
	// 46 - PRESSUER_IDX
	"Press",
	// 47 - FLOW_IDX
	"Flow",
	// 48 - VT_IDX
	"VT",
	// 49 - MV_IDX
	"MV",
	// 50 - MV_2_IDX
	"MV",
	// 51 - BPM_IDX
	"BPM",
	// 52 - INSPTIME_IDX
	"InspTime",
	// 53 - IE_IDX
	"I:E",
	// 54 - LEAKAGE_IDX
	"Leakage",
	// 55 - SPO2_IDX
	"SpO2",
	// 56 - PR_IDX
	"PR",
	// 57 - MENU_IDX
	"Menu",
	// 58 - MONITOR_TEMP_IDX
	"TEMP",
	// 59 - MONITOR_MODE_IDX
	"Mode",
	// 60 - AUTOON_IDX
	"AutoON",
	// 61 - AUTOOFF_IDX
	"AutoOFF",
	// 62 - MODE_IDX
	"Mode",
	// 63 - IPAP_IDX
	"IPAP",
	// 64 - EPAP_IDX
	"EPAP",
	// 65 - STARTPRESS_IDX
	"StartP",
	// 66 - BELEX_IDX
	"Belex",
	// 67 - RAMP_IDX
	"Ramp",
	// 68 - ISENS_IDX
	"ISens",
	// 69 - ESENS_IDX
	"ESens",
	// 70 - ISLOP_IDX
	"ISlop",
	// 71 - MAXINSPTIME_IDX
	"MaxInspTime",
	// 72 - MININSPTIME_IDX
	"MinInspTime",
	// 73 - IRAMP_IDX
	"iRamp",
	// 74 - MAXP_IDX
	"MaxPress",
	// 75 - MINP_IDX
	"MinPress",
	// 76 - SLOP_SENS_IDX
	"Boost Sensitivity",
	// 77 - SMART_PRESSURE_IDX
	"SmartPressure",
	// 78 - DIVIDENIGHT_IDX
	"Split night",
	// 79 - MAXPS_IDX
	"MaxPS",
	// 80 - MAXIPAP_IDX
	"MaxIPAP",
	// 81 - MINIPAP_IDX
	"MinIPAP",
	// 82 - MINEPAP_IDX
	"MinEPAP",
	// 83 - TARGET_VT_IDX   
	"VT",
	// 84 - VAF_SWITCH_IDX
	"AVAPS",
	// 85 - TARGET_FLOW_IDX
	"Flow",
	// 86 - TUBESELECTION_IDX
	"Tube",
	// 87 - MASKSETTING_IDX
	"Mask Set",
	// 88 - MASKTEST_IDX
	"Mask Fit",
	// 89 - ECOSETTING_IDX
	"ECO Set",
	// 90 - DATE_IDX
	"Date",
	// 91 - TIME_IDX
	"Time",
	// 92 - BACKLIGHT_IDX
	"Backlight",
	// 93 - BRIGHTNESS_IDX
	"Brightness",
	// 94 - LANGUAGE_IDX
	"Language",
	// 95 - P_UINT_IDX
	"Pressure Unit",
	// 96 - WIFI_SET_IDX
	"WIFI Settings",
	// 97 - WIFI_SELECT_IDX
	"WIFI Selection",
	// 98 - BLUETOOTH_IDX
	"Bluetooth",
	// 99 - RESET_IDX
	"Reset",
	// 100 - SOFTWAREVERSION_IDX
	"Version",
	// 101 - SN_IDX
	"SN",
	// 102 - HIGHPRESS_IDX
	"High Pressure",
	// 103 - LOWPRESS_IDX
	"Low Pressure",
	// 104 - HIGHLEAKAGE
	"High Leak",
	// 105 - LOWMV_IDX
	"Low MV",
	// 106 - APNEA_SET_IDX
	"Apnea",
	// 107 - REPLACEFILTER_IDX
	"Replace Filter",
	// 108 - REPLACEMASK_IDX
	"Replace Mask",
	// 109 - REPLACETUBE_IDX
	"Replace Tube",
	// 110 - REPLACEFILTER1_IDX
	"Replace Filter",
	// 111 - REPLACEMASK1_IDX
	"Replace Mask",
	// 112 - REPLACETUBE1_IDX
	"Replace Tube",
	// 113 - REPLACEFILTERCONTENT_IDX
	"Please replace the filter!",
	// 114 - REPLACEMASKCONTENT_IDX
	"Please replace the mask!",
	// 115 - REPLACETUBECONTENT_IDX
	"Please replace the tube!",
	// 116 - SDCARD_CONNECTIONSTATUS_IDX
	"SD Card Prompt",
	// 117 - SDCARD_UNCONNECTIONCONTENT_IDX
	"Can't find SD Card! Please check it!",
	// 118 - KNOBTONE_IDX
	"Knob Tone",
	// 119 - QRCODE_IDX
	"QR Code",
	// 120 - NOTICE_IDX
	"Tips",
	// 121 - REPLACETFCARDCONTENT_IDX
	"Insufficient SD card capacity!",
	// 122 - USECYCLE_IDX
	"Use Cycle",
	// 123 - USETIME_IDX
	"Use Time",
	// 124 - AVGPRESS_IDX
	"AvgPress",
	// 125 - PRESSURE_95TH_IDX
	"Titration Pressure",
	// 126 - AVGLEAKAGE_IDX
	"AvgLeakage",
	// 127 - AHI_IDX
	"AHI",
	// 128 - CSI_IDX
	"CAI",
	// 129 - USEDAYS_IDX
	"UseDays",
	// 130 - RUNHRS_IDX
	"RunHrs    ",
	// 131 - PREHEATING_IDX
	"Preheat",
	// 132 - PREHEATINGTIME_SET_IDX
	"Preheat Time",
	// 133 - HIMIDIFY_IDX
	"Humidifier",
	// 134 - TUBEWARMLEVEL_SET_IDX
	"Tube Temp",
	// 135 - PREHEATING_RESULT_IDX
	"Preheat Finish",
	// 136 - PREHEATING_OK_IDX
	"OK",
	// 137 - WLANTITLE_IDX
	"WLAN",
	// 138 - SCANNING_IDX
	"Scanning",
	// 139 - SCAN_FINISH_IDX
	"Scan complete",
	// 140 - ENCRYPT_IDX
	"Encrypt",
	// 141 - UNENCRYPT_IDX
	"Unencrypted",
	// 142 - PASSWORD_IDX
	"PassWord",
	// 143 - BACK_IDX
	"Back",
	// 144 - WIFI_CONNECTED_IDX
	"Connected",
	// 145 - WIFI_DISCONNECTED_IDX
	"Unconnected",
	// 146 - WIFI_CONNECTING_IDX
	"Connecting,",
	// 147 - WIFI_WAIT_IDX
	"Please wait...",
	// 148 - WIFI_CONNECTION_SUCCEEDED_IDX
	"Connected...",
	// 149 - WIFI_CONNECTION_FAILED_IDX
	"Connection failed,",
	// 150 - WIFI_ENTER_PASSWORD_IDX
	"Please re-connect ",
	// 151 - PERFORMANCE_TEST_IDX
	"Do you need function testing?",
	// 152 - TESTING_PROMPT_IDX
	"Testing, keep airway closed.",
	// 153 - TESTING_LEAK_IDX
	"Airway leakage occurred,please block the end of the air outlet.",
	// 154 - TESTING_SUCCESS_IDX
	"Test succeeded",
	// 155 - TESTING_IDX
	"Test ",
	// 156 - TESTING_FAIL_IDX
	"failed!",
	// 157 - TESTING_YLCGQ_FAIL_IDX
	"pressure Sensor ",
	// 158 - TESTING_YL_PROTECT_MODULE_IDX
	"pressure protection module ",
	// 159 - TESTING_PROTECT_RESET_MODULE_IDX
	"electric circuit reset protection module ",
	// 160 - RESETCONTENT_IDX
	"System reset will restore the machine to factory settings, please confirm.",
	// 161 - OK_RESET_IDX
	"OK",
	// 162 - CANCEL_RESET_IDX
	"Cancel",
	// 163 - MASKWEARINGTEST_TITLE_IDX
	"Mask Fit",
	// 164 - MASKTEST_START_IDX
	"Start",
	// 165 - MASKTEST_STOP_IDX
	"Stop",
	// 166 - MASKTEST_EXIT_IDX
	"Exit",
	// 167 - MASKTEST_PERFECT_IDX
	"Perfect!",
	// 168 - MASKTEST_ADJUST_IDX
	"Adjust!",
	// 169 - OFF_IDX
	"OFF",
	// 170 - ON_IDX
	"ON",
	// 171 - CPAP_IDX
	"CPAP",
	// 172 - APAP_IDX
	"APAP",
	// 173 - IAPAP_IDX
	"iAPAP",
	// 174 - S_IDX
	"S",
	// 175 - T_IDX
	"T",
	// 176 - ST_IDX
	"ST",
	// 177 - AUTOB_IDX
	"AUTOB",
	// 178 - APCV_IDX
	"APCV",
	// 179 - VAF_ST_IDX
	"VAF-ST",
	// 180 - VAF_APCV_IDX
	"VAF-APCV",
	// 181 - HIGH_FLOW_IDX
	"HF",
	// 182 - APAP_1_IDX
	"APAP",
	// 183 - CMH2O_IDX
	"cmH2O",
	// 184 - HPA_IDX
	"hPa",
	// 185 - L_MIN_IDX
	"L/min",
	// 186 - L_IDX
	"L",
	// 187 - ML_IDX
	"ml",
	// 188 - LV_IDX
	"Lv",
	// 189 - SEC_IDX
	"s",
	// 190 - MIN_IDX
	"min",
	// 191 - DAY_IDX
	"Day",
	// 192 - HOUR_IDX
	"Hour",
	// 193 - MONTH_IDX
	"Month",
	// 194 - TUBE_15MM_IDX
	"15mm Tube",
	// 195 - TUBE_19MM_IDX
	"19mm Tube",
	// 196 - TUBE_HEATING_IDX
	"Heating Tube",
	// 197 - NASALMASK_IDX
	"Nasal mask",
	// 198 - NOSEANDMOUTHMASK_IDX
	"Full Face Mask",
	// 199 - NASALPILLOW_IDX
	"Pillow",
	// 200 - STANDARD_IDX
	"Standard",
	// 201 - SENSITIVE_IDX
	"Sensitive",
	// 202 - SOFT_IDX
	"Soft",
	// 203 - AUTO_IDX
	"Auto",
	// 204 - AUTO_CN_IDX
	"Auto",
	// 205 - BPMUNIT_IDX
	"BPM",
	// 206 - SEC_E_IDX
	"s",
	// 207 - PERCENTAGE_IDX
	"%",
	// 208 - I_STATUS_IDX
	"I",
	// 209 - E_STATUS_IDX
	"E",
	// 210 - ADMIN_CALI_MOTORSPEED_IDX
	"Motor Speed",
	// 211 - ADMIN_CALI_PREV_IDX
	"PREV",
	// 212 - ADMIN_CALI_OK_IDX
	"OK",
	// 213 - ADMIN_CALI_EXIT_IDX
	"Exit",
	// 214 - ADMIN_CALI_FLOW_IDX
	"Flow: ",
	// 215 - ADMIN_CALI_TIME_IDX
	"Calibration Time: ",
	// 216 - ADMIN_FLOW_CALI_ERROR_IDX
	"Abnormal Flow\nCalibration",
	// 217 - ADMIN_FLOWCALI_TITLE_IDX
	"FlowCali",
	// 218 - ADMIN_CALI_PRESSURE_IDX
	"Press: ",
	// 219 - ADMIN_PRESSCALI_TITLE_IDX
	"PressureCali",
	// 220 - ADMIN_PRESCALI_IDX
	"PressureCali",
	// 221 - ADMIN_FLOWCALI_IDX
	"FlowCali",
	// 222 - ADMIN_CLEARUSAGEDATA_PROMPT_IDX
	"Is all device data wiped?",
	// 223 - ADMIN_CLEARUSAGEDATA_SUCCESS_IDX
	"Data Wiped Successfully",
	// 224 - HF_MODE_USAGE_PROMPT_IDX
	"Heating Tube Better!",
	// 225 - ESLOP_IDX
	"ESlop",
	// 226 - CLEAR_USAGE_DATA_IDX
	"Wipe Usage Data",
	// 227 - MANAGER_SET_IDX
	"AdminSet",
	// 228 - NOSEANDMOUTHMASK_FM_1_IDX
	"Full Face Mask(FM\xE2\x85\xA0"")",
	// 229 - NOSEANDMOUTHMASK_FM_2_IDX
	"Full Face Mask(FM\xE2\x85\xA1"")",
	// 230 - NOSEANDMOUTHMASK_FM_2P_IDX
	"Full Face Mask(FMIIP)",
	// 231 - NOSEANDMOUTHMASK_FM_3_IDX
	"Full Face Mask(FM\xE2\x85\xA2"")",
	// 232 - ECO_MODE_TIPS_IDX
	"Wifi, Preheat, Humidifier, Backlight and Brightness will be disabled. Turn off to recover",
	// 233 - CHAR_IDX
	"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz,\xEF\xBC\x8C""~!@#$%^&*?-_:;/() \xE2\x84\x83""""'+,.<>[]\\`{|}",
	// 234 - NUMBER_IDX
	"-0.0123456789",
	// 235 - DEBUG_TITLE_IDX
	"Debug \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 236 - DEBUG_FLOWCALI_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE6\x89\x8B\xE5\x8A\xA8\xE5\xAE\x9A\xE6\xA0\x87",
	// 237 - DEBUG_FLOWAUTOCALI_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE8\x87\xAA\xE5\x8A\xA8\xE5\xAE\x9A\xE6\xA0\x87",
	// 238 - DEBUG_PRESCALI_IDX
	"\xE5\x8E\x8B\xE5\x8A\x9B\xE5\xAE\x9A\xE6\xA0\x87",
	// 239 - DEBUG_BUZZER_IDX
	"\xE8\x9C\x82\xE9\xB8\xA3\xE5\x99\xA8\xE6\xB5\x8B\xE8\xAF\x95",
	// 240 - DEBUG_HEATING_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\xB5\x8B\xE8\xAF\x95",
	// 241 - DEBUG_RESET_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE",
	// 242 - DEBUG_DEVICEINFO_IDX
	"\xE8\xAE\xBE\xE5\xA4\x87\xE4\xBF\xA1\xE6\x81\xAF",
	// 243 - DEBUG_SINGLEFAULT_IDX
	"\xE5\x8D\x95\xE4\xB8\x80\xE6\x95\x85\xE9\x9A\x9C\xE5\x8E\x8B\xE5\x8A\x9B\xE6\xB5\x8B\xE8\xAF\x95",
	// 244 - DEBUG_HARD_PROTECT_ON_IDX
	"\xE8\xBF\x87\xE5\x8E\x8B\xE4\xBF\x9D\xE6\x8A\xA4"":\xE5\xBC\x80",
	// 245 - DEBUG_HARD_PROTECT_OFF_IDX
	"\xE8\xBF\x87\xE5\x8E\x8B\xE4\xBF\x9D\xE6\x8A\xA4"":\xE5\x85\xB3",
	// 246 - DEBUG_CE_SWITCH_IDX
	"\xE7\x89\x88\xE6\x9C\xAC"":CE",
	// 247 - DEBUG_CHINA_SWITCH_IDX
	"\xE7\x89\x88\xE6\x9C\xAC"":\xE5\x9B\xBD\xE5\x86\x85",
	// 248 - DEBUG_FDA_SWITCH_IDX
	"\xE7\x89\x88\xE6\x9C\xAC"":FDA",
	// 249 - DEBUG_EXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 250 - FLOWCALI_TITLE_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE5\xAE\x9A\xE6\xA0\x87",
	// 251 - CALI_ADJUSTMOTOR_IDX
	"\xE8\xB0\x83\xE6\x95\xB4\xE9\xA9\xAC\xE8\xBE\xBE",
	// 252 - CALI_PRESSURE_IDX
	"\xE5\x8E\x8B\xE5\x8A\x9B\xEF\xBC\x9A",
	// 253 - CALI_PHYPRESSURE_IDX
	"\xE7\x89\xA9\xE7\x90\x86\xE5\x8E\x8B\xE5\x8A\x9B\xEF\xBC\x9A",
	// 254 - CALI_FLOW_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xEF\xBC\x9A",
	// 255 - CALI_PHYFLOW_IDX
	"\xE7\x89\xA9\xE7\x90\x86\xE6\xB5\x81\xE9\x87\x8F\xEF\xBC\x9A",
	// 256 - CALI_SPEED_IDX
	"\xE9\x80\x9F\xE5\xBA\xA6\xEF\xBC\x9A",
	// 257 - CALI_TIME_IDX
	"\xE5\xAE\x9A\xE6\xA0\x87\xE6\x97\xB6\xE9\x97\xB4\xEF\xBC\x9A",
	// 258 - CALI_SELF_CHECK_IDX
	"\xE8\x87\xAA\xE6\xA3\x80",
	// 259 - CALI_OK_IDX
	"\xE7\xA1\xAE\xE5\xAE\x9A",
	// 260 - CALI_PREV_IDX
	"\xE4\xB8\x8A\xE4\xB8\x80\xE4\xB8\xAA",
	// 261 - CALI_EXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 262 - AUTOCALI_EXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 263 - CALI_START_AUTOCALI_IDX
	"\xE5\xAE\x9A\xE6\xA0\x87\xE5\xBC\x80\xE5\xA7\x8B",
	// 264 - CALI_AUTOCALI_IDX
	"\xE8\x87\xAA\xE5\x8A\xA8\xE5\xAE\x9A\xE6\xA0\x87",
	// 265 - CALI_RETEST_EXPORT_DATA
	"\xE5\xA4\x8D\xE6\xB5\x8B\xE6\x95\xB0\xE6\x8D\xAE",
	// 266 - CALI_MODE_CHECK_IDX
	"\xE6\xA8\xA1\xE5\xBC\x8F\xE5\x88\x87\xE6\x8D\xA2",
	// 267 - AUTOCALI_TOTALTIME_IDX
	"\xE8\x80\x97\xE6\x97\xB6",
	// 268 - AUTOCALI_DATA_ERR_IDX
	"\xE6\xA0\xA1\xE5\x87\x86\xE6\x95\xB0\xE6\x8D\xAE\xE5\xBC\x82\xE5\xB8\xB8""!",
	// 269 - AUTOCALI_TIMEOUT_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE8\xAE\xA1\xE6\x9C\xAA\xE8\xBF\x9E\xE6\x8E\xA5""!",
	// 270 - AUTOCALI_RETEST_OK_IDX
	"\xE5\xA4\x8D\xE6\xB5\x8B\xE6\x88\x90\xE5\x8A\x9F""!",
	// 271 - AUTOCALI_RETEST_ERR_IDX
	"\xE5\xA4\x8D\xE6\xB5\x8B\xE9\x94\x99\xE8\xAF\xAF""!",
	// 272 - AUTOCALI_OK_IDX
	"\xE8\x87\xAA\xE5\x8A\xA8\xE5\xAE\x9A\xE6\xA0\x87\xE5\xAE\x8C\xE6\x88\x90""!",
	// 273 - AUTOCALI_MAX_FLOW_ERR_IDX
	"\xE6\x97\xA0\xE6\xB3\x95\xE8\xBE\xBE\xE5\x88\xB0\xE6\x9C\x80\xE5\xA4\xA7\xE5\xAE\x9A\xE6\xA0\x87\xE6\xB5\x81\xE9\x87\x8F""!",
	// 274 - CALI_MODE_TSI_IDX
	"\xE4\xBD\xBF\xE7\x94\xA8""TSI\xE5\xAE\x9A\xE6\xA0\x87\xE6\xAD\xA4\xE8\xAE\xBE\xE5\xA4\x87\xE3\x80\x82",
	// 275 - CALI_MODE_DEV_IDX
	"\xE4\xBD\xBF\xE7\x94\xA8\xE6\xB2\xBB\xE5\x85\xB7\xE5\xAE\x9A\xE6\xA0\x87\xE6\xAD\xA4\xE8\xAE\xBE\xE5\xA4\x87\xE3\x80\x82",
	// 276 - CALI_MODE_HOST_IDX
	"\xE4\xBD\x9C\xE4\xB8\xBA\xE6\xB2\xBB\xE5\x85\xB7\xE5\xAE\x9A\xE6\xA0\x87\xE5\x85\xB6\xE4\xBB\x96\xE8\xAE\xBE\xE5\xA4\x87\xE3\x80\x82",
	// 277 - DEBUG_FLOW_CALI_ERROR_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE5\xAE\x9A\xE6\xA0\x87\xE5\xBC\x82\xE5\xB8\xB8\xE8\xAF\xB7\xE7\xA1\xAE\xE8\xAE\xA4",
	// 278 - PRESSCALI_TITLE_IDX
	"\xE5\x8E\x8B\xE5\x8A\x9B\xE5\xAE\x9A\xE6\xA0\x87",
	// 279 - DEBUG_AMBIENTTEMP_IDX
	"\xE5\xAE\xA4\xE6\xB8\xA9"":",
	// 280 - DEBUG_HEATINGCHIPTEMP_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\x9D\xBF\xE6\xB8\xA9\xE5\xBA\xA6"":",
	// 281 - DEBUG_TUBETEMP_IDX
	"\xE7\xAE\xA1\xE8\xB7\xAF\xE6\xB8\xA9\xE5\xBA\xA6"":",
	// 282 - DEBUG_LANGUAGE_IDX
	"\xE5\xBD\x93\xE5\x89\x8D\xE8\xAF\xAD\xE8\xA8\x80"":",
	// 283 - DEBUG_TIME_IDX
	"\xE8\xAE\xBE\xE5\xA4\x87\xE6\x97\xB6\xE9\x97\xB4"":",
	// 284 - DEBUG_BRIGHTNESS_IDX
	"\xE7\x8E\xAF\xE5\xA2\x83\xE5\x85\x89\xE7\xBA\xBF"":",
	// 285 - DEBUG_MOTOR_TEMP_IDX
	"\xE7\x94\xB5\xE6\x9C\xBA\xE6\xB8\xA9\xE5\xBA\xA6"":",
	// 286 - DEBUG_POWER_VOL_IDX
	"\xE7\x94\xB5\xE6\xBA\x90\xE7\x94\xB5\xE5\x8E\x8B"":",
	// 287 - DEBUG_TEMPERATUREUNIT_IDX
	"\xE2\x84\x83",
	// 288 - DEBUG_BUZZERTITLE_IDX
	"\xE8\x9C\x82\xE9\xB8\xA3\xE5\x99\xA8\xE6\xB5\x8B\xE8\xAF\x95"" \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 289 - DEBUG_HIGHPRIORITYALARM_IDX
	"\xE9\xAB\x98\xE4\xBC\x98\xE5\x85\x88\xE7\xBA\xA7\xE6\x8A\xA5\xE8\xAD\xA6",
	// 290 - DEBUG_UNLOCKSOUNDS_IDX
	"\xE8\xA7\xA3\xE9\x94\x81\xE5\xA3\xB0\xE9\x9F\xB3",
	// 291 - DEBUG_POWERONSOUNDS_IDX
	"\xE5\xBC\x80\xE6\x9C\xBA\xE5\xA3\xB0\xE9\x9F\xB3",
	// 292 - DEBUG_PREHEATINGBEEP_IDX
	"\xE9\xA2\x84\xE7\x83\xAD\xE6\x8F\x90\xE7\xA4\xBA\xE9\x9F\xB3",
	// 293 - DEBUG_TOUCH_TONE_IDX
	"\xE6\x97\x8B\xE9\x92\xAE\xE9\x9F\xB3",
	// 294 - DEBUG_ABNORMALPOWEROFFSOUNDS_IDX
	"\xE6\x8E\x89\xE7\x94\xB5\xE6\x8A\xA5\xE8\xAD\xA6\xE9\x9F\xB3",
	// 295 - DEBUG_BUZZEREXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 296 - DEBUG_YES_IDX
	"\xE6\x98\xAF",
	// 297 - DEBUG_NO_IDX
	"\xE5\x90\xA6",
	// 298 - DEBUG_HEATINGTITLE_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\xB5\x8B\xE8\xAF\x95"" \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 299 - DEBUG_HEATINGCHIPTITLE_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\x9D\xBF\xE5\x8A\xA0\xE7\x83\xAD\xE6\xB5\x8B\xE8\xAF\x95",
	// 300 - DEBUG_TUBETITLE_IDX
	"\xE7\xAE\xA1\xE8\xB7\xAF\xE5\x8A\xA0\xE7\x83\xAD\xE6\xB5\x8B\xE8\xAF\x95",
	// 301 - DEBUG_HEATINGCHIPRESULT_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\x9D\xBF\xE5\x8A\xA0\xE7\x83\xAD\xE4\xB8\xAD\xEF\xBC\x81",
	// 302 - DEBUG_TUBERESULT_IDX
	"\xE5\x8A\xA0\xE6\xB8\xA9\xE7\xAE\xA1\xE8\xB7\xAF\xE5\x8A\xA0\xE7\x83\xAD\xE4\xB8\xAD\xEF\xBC\x81",
	// 303 - DEBUG_HEATINGCHIPRESULT_STOP_IDX
	"\xE5\x8A\xA0\xE7\x83\xAD\xE6\x9D\xBF\xE5\x81\x9C\xE6\xAD\xA2\xE5\x8A\xA0\xE7\x83\xAD\xEF\xBC\x81",
	// 304 - DEBUG_TUBERESULT_STOP_IDX
	"\xE5\x8A\xA0\xE6\xB8\xA9\xE7\xAE\xA1\xE8\xB7\xAF\xE5\x81\x9C\xE6\xAD\xA2\xE5\x8A\xA0\xE7\x83\xAD\xEF\xBC\x81",
	// 305 - DEBUG_STARTHEATING_IDX
	"\xE5\xBC\x80\xE5\xA7\x8B\xE5\x8A\xA0\xE7\x83\xAD",
	// 306 - DEBUG_STOPHEATING_IDX
	"\xE5\x81\x9C\xE6\xAD\xA2\xE5\x8A\xA0\xE7\x83\xAD",
	// 307 - DEBUG_HEATINGEXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 308 - DEBUG_SYSRESETTITLE_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE"" \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 309 - DEBUG_SYSRESETTITLE1_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE\xE8\xBF\x87\xE7\xA8\x8B\xE9\x9C\x80\xE8\xA6\x81""3-4\xE5\x88\x86\xE9\x92\x9F"",\xE8\xAF\xB7\xE5\x9C\xA8\xE6\xAD\xA4\xE8\xBF\x87\xE7\xA8\x8B\xE4\xB8\xAD\xE4\xB8\x8D\xE8\xA6\x81\xE5\x85\xB3\xE9\x97\xAD\xE7\x94\xB5\xE6\xBA\x90\xE3\x80\x82",
	// 310 - DEBUG_RESETSYSPARA_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE""--\xE5\x90\x84\xE7\xB3\xBB\xE7\xBB\x9F\xE5\x8F\x82\xE6\x95\xB0",
	// 311 - DEBUG_RESETTIPARA_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE""--\xE5\x90\x84\xE6\xBB\xB4\xE5\xAE\x9A\xE5\x8F\x82\xE6\x95\xB0",
	// 312 - DEBUG_RESETDATASAVE_IDX
	"\xE6\x81\xA2\xE5\xA4\x8D\xE5\x87\xBA\xE5\x8E\x82\xE8\xAE\xBE\xE7\xBD\xAE""--\xE6\x95\xB0\xE6\x8D\xAE\xE5\xAD\x98\xE5\x82\xA8",
	// 313 - DEBUG_RESETSTART_IDX
	"\xE5\xBC\x80\xE5\xA7\x8B\xE6\x81\xA2\xE5\xA4\x8D"",\xE8\xAF\xB7\xE8\x80\x90\xE5\xBF\x83\xE7\xAD\x89\xE5\xBE\x85""...",
	// 314 - DEBUG_FINISHSYSPARARESET_IDX
	"\xE7\xB3\xBB\xE7\xBB\x9F\xE5\x8F\x82\xE6\x95\xB0\xE6\x81\xA2\xE5\xA4\x8D\xE5\xAE\x8C\xE6\x88\x90\xEF\xBC\x81",
	// 315 - DEBUG_FINISHTIPARARESET_IDX
	"\xE6\xBB\xB4\xE5\xAE\x9A\xE5\x8F\x82\xE6\x95\xB0\xE6\x81\xA2\xE5\xA4\x8D\xE5\xAE\x8C\xE6\x88\x90\xEF\xBC\x81",
	// 316 - DEBUG_RESETSTART1_IDX
	"\xE5\xBC\x80\xE5\xA7\x8B\xE6\x81\xA2\xE5\xA4\x8D"",\xE8\xAF\xB7\xE8\x80\x90\xE5\xBF\x83\xE7\xAD\x89\xE5\xBE\x85"",\xE5\x85\xB1\xE4\xB8\xA4\xE9\x83\xA8\xE5\x88\x86""...",
	// 317 - DEBUG_FINISHFIRST_IDX
	"\xE5\x88\x9D\xE5\xA7\x8B\xE5\x8C\x96\xE7\xAC\xAC\xE4\xB8\x80\xE9\x83\xA8\xE5\x88\x86\xE5\xAE\x8C\xE6\x88\x90\xEF\xBC\x81",
	// 318 - DEBUG_FINISHDAYASAVERESET_IDX
	"\xE6\x95\xB0\xE6\x8D\xAE\xE5\xAD\x98\xE5\x82\xA8\xE6\x81\xA2\xE5\xA4\x8D\xE5\xAE\x8C\xE6\x88\x90",
	// 319 - DEBUG_DEVICEINFOTITLE_IDX
	"\xE8\xAE\xBE\xE5\xA4\x87\xE4\xBF\xA1\xE6\x81\xAF",
	// 320 - DEBUG_CHIPMODELTEXT_IDX
	"\xE8\x8A\xAF\xE7\x89\x87\xE5\x9E\x8B\xE5\x8F\xB7""  :",
	// 321 - DEBUG_FONTBMPVERSIONTEXT_IDX
	"\xE8\xB5\x84\xE6\xBA\x90\xE7\x89\x88\xE6\x9C\xAC""  :",
	// 322 - DEBUG_SOFTWAREVERTEXT_IDX
	"\xE8\xBD\xAF\xE4\xBB\xB6\xE7\x89\x88\xE6\x9C\xAC""  :",
	// 323 - DEBUG_BOOTLOADERTEXT_IDX
	"Boot\xE7\x89\x88\xE6\x9C\xAC"" :",
	// 324 - DEBUG_SNTEXT_IDX
	"\xE4\xBA\xA7\xE5\x93\x81\xE5\xBA\x8F\xE5\x88\x97\xE5\x8F\xB7""  :",
	// 325 - DEBUG_PRODUCTIONDATE_IDX
	"\xE7\x94\x9F\xE4\xBA\xA7\xE6\x97\xA5\xE6\x9C\x9F""  :",
	// 326 - DEBUG_SINGLEFAULTPTITLE_IDX
	"\xE5\x8D\x95\xE4\xB8\x80\xE6\x95\x85\xE9\x9A\x9C\xE5\x8E\x8B\xE5\x8A\x9B\xE6\xB5\x8B\xE8\xAF\x95"" \xE6\xA8\xA1\xE5\x9D\x97"" V1.0",
	// 327 - DEBUG_CPAPPRESSURE_IDX
	"\xE5\x8D\x95\xE6\xB0\xB4\xE5\xB9\xB3\xE5\x8E\x8B\xE5\x8A\x9B\xE6\xB5\x8B\xE8\xAF\x95",
	// 328 - DEBUG_BIPAPPRESSURE_IDX
	"\xE5\x8F\x8C\xE6\xB0\xB4\xE5\xB9\xB3\xE5\x8E\x8B\xE5\x8A\x9B\xE6\xB5\x8B\xE8\xAF\x95",
	// 329 - DEBUG_PWMON_IDX
	"PWM\xE5\xBC\x80",
	// 330 - DEBUG_PEMOFF_IDX
	"PWM\xE5\x85\xB3",
	// 331 - DEBUG_PWMADD_IDX
	"PWM\xE5\x8A\xA0",
	// 332 - DEBUG_PWMSUB_IDX
	"PWM\xE5\x87\x8F",
	// 333 - DEBUG_SINGLEPEXIT_IDX
	"\xE9\x80\x80\xE5\x87\xBA",
	// 334 - DEBUG_OFFSET_IDX
	"\xE5\x81\x8F\xE7\xA7\xBB",
	// 335 - DEBUG_FLOWOFFSET_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE5\x81\x8F\xE7\xA7\xBB",
	// 336 - DEBUG_PRESSOFFSET_IDX
	"\xE5\x8E\x8B\xE5\x8A\x9B\xE5\x81\x8F\xE7\xA7\xBB",
	// 337 - DEBUG_ALLOFFSET_IDX
	"\xE6\x95\xB4\xE4\xBD\x93\xE5\x81\x8F\xE7\xA7\xBB",
	// 338 - DEBUG_F30OFFSET_IDX
	"30L/min\xE5\x81\x8F\xE7\xA7\xBB",
	// 339 - DEBUG_F80OFFSET_IDX
	"80L/min\xE5\x81\x8F\xE7\xA7\xBB",
	// 340 - DEBUG_F120OFFSET_IDX
	"120L/min\xE5\x81\x8F\xE7\xA7\xBB",
	// 341 - DEBUG_F140OFFSET_IDX
	"140L/min\xE5\x81\x8F\xE7\xA7\xBB",
	// 342 - DEBUG_P4OFFSET_IDX
	"4cmH2o\xE5\x81\x8F\xE7\xA7\xBB",
	// 343 - DEBUG_P10OFFSET_IDX
	"10cmH2o\xE5\x81\x8F\xE7\xA7\xBB",
	// 344 - DEBUG_P20OFFSET_IDX
	"20cmH2o\xE5\x81\x8F\xE7\xA7\xBB",
	// 345 - DEBUG_RETEST_IDX
	"\xE5\xA4\x8D\xE6\xB5\x8B",
	// 346 - DEBUG_SHIFT_IDX
	"\xE6\x8D\xA2\xE6\x8C\xA1",
	// 347 - DEBUG_RETESTPRESS_IDX
	"\xE8\xAF\xB7\xE8\xB0\x83\xE6\x95\xB4\xE5\x8E\x8B\xE5\x8A\x9B\xE6\x8E\xA7\xE5\x88\xB6\xE5\x8E\x8B\xE5\x8A\x9B\xE8\xBE\x93\xE5\x87\xBA",
	// 348 - DEBUG_MOREOPTION_IDX
	"\xE6\x9B\xB4\xE5\xA4\x9A""...",
	// 349 - DEBUG_AUTOPRESSCALI_IDX
	"\xE8\x87\xAA\xE5\x8A\xA8\xE5\x8E\x8B\xE5\x8A\x9B\xE5\xAE\x9A\xE6\xA0\x87",
	// 350 - DEBUG_LEAKVIEWSET_ONMASK_IDX
	"\xE6\xBC\x8F\xE6\xB0\x94\xE9\x87\x8F\xE6\x98\xBE\xE7\xA4\xBA"":  \xE5\x90\xAB\xE9\x9D\xA2\xE7\xBD\xA9",
	// 351 - DEBUG_LEAKVIEWSET_OFFMASK_IDX
	"\xE6\xBC\x8F\xE6\xB0\x94\xE9\x87\x8F\xE6\x98\xBE\xE7\xA4\xBA"":  \xE4\xB8\x8D\xE5\x90\xAB\xE9\x9D\xA2\xE7\xBD\xA9",
	// 352 - DEBUG_CHINESESERVER_IDX
	"\xE6\x9C\x8D\xE5\x8A\xA1\xE5\x99\xA8"":  Dev",
	// 353 - DEBUG_FOREIGNSERVER_IDX
	"\xE6\x9C\x8D\xE5\x8A\xA1\xE5\x99\xA8"":  Test",
	// 354 - DEBUG_BECSERVER_IDX
	"\xE6\x9C\x8D\xE5\x8A\xA1\xE5\x99\xA8"":  Stage",
	// 355 - DEBUG_FLOWSENSORTYPE_IDX
	"\xE6\xB5\x81\xE9\x87\x8F\xE4\xBC\xA0\xE6\x84\x9F\xE5\x99\xA8\xEF\xBC\x9A",
	// 356 - BLANK_IDX
	" ",
};
