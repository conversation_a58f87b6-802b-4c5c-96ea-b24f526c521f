#ifndef __DATATYPE_H__
#define __DATATYPE_H__

#include "Version.h"
#include <string.h>
#include "Global.h"
#include "includes.h"
#include "GUI.h"
#include "WM.h"
#include "crypto.h"

#define LCD_5_TFT               1       //5寸屏
#define LCD_35_TFT              2       //3.5寸屏
#define LCD_28_TFT              3       //2.8寸屏

#define LCD_TYPE               TYPE_NUMBER

#define BLDC_BRAKE_TYPE         1        //电机刹车方式 0:下桥全导通刹车 1:下桥2500占空比PWM刹车

#define MOTOR_TYPE              1       //0:仓兴达 1:贝丰
#define PRESSURE_SENSOR_CHECK   1       //0:关闭 1:打开压力传感器坏在中间电压点检测

#define MOTOR_TEMP_TABLE        0       //0:仓兴达/贝丰 1:唯川

#define SUPPORT_ZJJ_SNORE_CHECK 0       //0:关闭 1:支持系统组组长张家佳提供的鼾声识别检测
#define SUPPORT_CJ_SNORE_CHECK  0       //0:关闭 1:支持陈娟提供的鼾声识别检测

#define SUPPORT_SNORE_CHECK             1   //0:关闭 1:支持鼾声检测
#define ENABLE_SAVE_DEBUG_SNORE_DATA    0   //0:关闭 1:用于存储鼾声识别的调试数据

#define EDF_DATA_UNIFIED        1       //0:关闭 1:使能数据统一功能

#define ADC3_SAMPLE_STYLE       0       //0:DMA 1:注入通道
#define DEBUG_TASK_RESOURCE     0       //0:调试各任务CPU占用率与栈使用情况

#define ENABLE_CATCH_PICTURE    0

#define ENABLE_SD_CHANGE_PARAMETER         0  //0:关闭 1:打开SD导入参数功能
#define ENABLE_NETWORK_CHANGE_PARAMETER    1  //0:关闭 1:打开网络调参功能

#define ENABLE_ENCRYPTION           1       //0:关闭 1:打开加密功能

// 定义是否使用完整的MTLS认证（包括客户端证书）
#define USE_FULL_MTLS_AUTH 0   // 0: 仅根证书认证, 1: 完整的MTLS双向认证

#if (EDF_DATA_UNIFIED == 1)
#define NETWORD_PROTOCAL_VERSION    2       //网络通讯协议的版本
#else
#define NETWORD_PROTOCAL_VERSION    1       //网络通讯协议的版本
#endif

#define HEAT_PLATE_VERSION          1      //0:加热盘中心温度点偏低，软件中进行补偿处理 1:加热盘重新寄了新样品，中心点温度符合要求

#define ENABLE_VECT_OFFSET      1
#if (ENABLE_CATCH_PICTURE == 1)
#define ENABLE_WATCHDOG         0
#else
#define ENABLE_WATCHDOG         0
#endif

#define MOTOR_DRIVER_TYPE       1        //0:MC33035 1:PWM+HALL
#define CURRENTRING             0       //电流环控制 0为单环  1为双环

#define NET_COMMUNICATION_TYPE  0        //0:WIFI 1:SIM

#define ENABLE_MOTOR_TEMP_DEBUG 0       //0:关闭 1:打开，状态条时间可显示电机温度

#define WIFI_NET_DEBUG          0       //0:关闭 1:打开网络数据调试，将发送数据与接收数据存至SD卡
#define NETWORK_REAL_SEND       0       //0:关闭 1:实时发送
#define SDCARD_PROTOCAL         1       //0:Resplus 1:ResFree

#define ENABLE_EMC_TEST         0       //0:关闭 1:打开

#define MAX_WIFI_PWD_LEN        16

#define __SPO2_DISABLE         1       //0:允许 1:禁用  血氧解析功能
#define __SWD_DISABLE          1       //0:允许 1:禁用  JTAG读取功能

#define FALSE 0
#define TRUE  1 

//MCU地址分配
#define MACHINEINFO_ADDR    0x080FFF80 
#define MACHINEINFO_DATA    (*(const uint32_t *)0x080FFF80) //驱动版本号占4个字节,1M前的128个字节,仅使用结构体中的数据大小容量

typedef enum EWorkMode
{
    SYS_WM_CPAP = 0,
    SYS_WM_APAP,
    SYS_WM_IAPAP,
    SYS_WM_S,   
    SYS_WM_T,    
    SYS_WM_ST,    
    SYS_WM_AUTOB,
    SYS_WM_APCV,
    SYS_WM_VAF_ST,
    SYS_WM_VAF_APCV,    
    SYS_WM_HIGHFLOW,
}EWORKMODE;

typedef enum EMarketArea
{
    MARKET_AREA_CHINA = 0,
    MARKET_AREA_CE,
    MARKET_AREA_FDA
}EMARKETAREA;

/*云平台数据上传通道类型*/
typedef enum UpLoadType
{
    WIFI_TYPE = 0,
    SIM_TYPE,
}UP_LOAD_TYPE;

#pragma pack(push)
#pragma pack(1)

typedef struct
{
    unsigned HardProtectSwitch  : 1; //硬件过压保护开关
    unsigned MarketAreaType     : 2; //0:国内版本 1:CE版本 2:FDA版本
    unsigned LeakageViewType    : 1; //漏气量显示:含面罩和不含面罩
    unsigned ServerType         : 2; //服务器: 国内服务器和国外服务器
    unsigned Reserved : 2;           //
}FunctionEnableType;

/*这个结构体的作用，主要是最早期有发样机出去，采用的序列号规则为机型代码+项目代码+年(22)+月(05)+尾码(0001)，
但后期调整为了机型代码+项目代码+年(A-Z)+月(A-L)+压力传感器类型+流量传感器类型+尾码(0001)
所以软件升级时序列号的兼容性存在问题
*/
typedef struct TOldMachineConfig
{    
    FunctionEnableType FunctionEnable;//bit 0:硬件过压保护开关
    U32 SupportLanguage;            //支持语言的种类 bit0-中文 bit1-English	
    unsigned TailCode : 16;
    unsigned Month : 4;
    unsigned Year : 8;
    unsigned ProNum: 4;//项目代码，固定为?
    unsigned DType: 4;
    U8 CheckSum;    
}OLDMACHINECONFIG, *POLDMACHINECONFIG;

typedef struct TMachineConfig
{    
    FunctionEnableType FunctionEnable;//bit 0:硬件过压保护开关
    U32 SupportLanguage;			//支持语言的种类 bit0-中文 bit1-English
    unsigned TailCode : 17;
    unsigned Month : 4;
    unsigned Year : 7;
    unsigned ProNum: 4;//项目代码，固定为?
    unsigned DType: 4;
    U8 FlowSensorType;
    U8 PressSensorType;
    U8 CheckSum;
    void operator = (OLDMACHINECONFIG &OldMachineConfig)
    {
        FunctionEnable = OldMachineConfig.FunctionEnable;
        SupportLanguage = OldMachineConfig.SupportLanguage;
        TailCode = OldMachineConfig.TailCode;
        Month = (OldMachineConfig.Month >= 1) ? OldMachineConfig.Month - 1 : 0;
        if (Month > 11)
        {
            Month = 0;
        }
        Year = (OldMachineConfig.Year >= 22) ? (OldMachineConfig.Year - 22) : 0;
        if (Year > 25)
        {
            Year = 0;
        }
        ProNum = OldMachineConfig.ProNum;
        DType = OldMachineConfig.DType;
        FlowSensorType = 0;
        PressSensorType = 0;
    }    
}MACHINECONFIG, *PMACHINECONFIG;

typedef struct TMachineParameterInfo
{
    U16 U16MaxIpap;
}MACHINEPARAMETERINFO, *PMACHINEPARAMETERINFO;
/*
 * @机型代码定义
 */
typedef enum EDeviceType
{
    RF_20C_S1_X1 = 0,
    RF_20A_S2_X2,
    RF_20I_S3_X3,
    RF_25A_S5_X5,
    RF_25S_S6_X6,
    RF_30A_S7_X7,
    RF_25T_H1_P1,
    RF_25P_H2_P2,
    RF_25V_H3_P3,
    RF_30T_H5_P5,
    RF_30P_H6_P6,
    RF_30S_H7_P7,
    RF_30V_H8_P8,
    RF_30F_H9_P9,
    AB_33F = 0,
    AA_35F,
}EDEVICETYPE;

typedef struct TRunTimeData
{
    U32 U32RunTime;
    U32 U32FilterChangeTime;
    U32 U32MaskChangeTime;          //更换面罩时间
    U32 U32TubeChangeTime;          //更换管路时间
    U8 Reserved[8];
    U8 CheckSum;
}RUNTIMEDATA, *PRUNTIMEDATA;

typedef struct TWarmConfig
{    
    U8 WarmLevel;       //湿化器档位
    
    U8 PreHeatSwitch;   //预热开关
    U8 PreHeatTime;     //预热时间为10-30分钟，步长为10分钟吗，默认设置30分钟，单位为分钟
    U8 TubeWarmLevel;   //管路加热档位,1-5档，默认为3档
}WARMCONFIG, *PWARMCONFIG;

typedef struct TSystemConfig
{
    U8 Language;								//语言
    U16 BackLightTime;					//背光时间
    U8 BackLightBrigntNess;			//背光亮度
    U8 PressUnit;					//压力单位 0-cmH2O;  1-hPa
    U8 UseCycle;                    //使用周期，0-5 0:1day 1:7days 2:30days 3:90days 4:180days 5:365days
    U8 TubeType;                    //管路类型，0：15mm普通管路、1：19mm普通管路、2：加热管路
    U8 MaskType;                    //面罩类型：0--鼻罩；1--口鼻罩(一代)；2--口鼻罩(二代)；3--口鼻罩(二代P)；4--口鼻罩(三代)；5--鼻枕
    U8 ECOType;                     //系统工作模式：0:舒适模式、1:节能模式、2:飞行模式 
    
    U8 HighPressureAlm;                     //报警高气压
    U8 LowPressureAlm;                     //报警低气压
    U8 HighLeakAlm;                     //报警高漏气
    U16 LowMVAlm;                          //低通气
    U8 ApneaTimeAlm;                          //窒息时间      
    U8 ReplaceFilterAlm;
    U8 ReplaceMaskAlm;
    U8 ReplaceTubeAlm;
    U8 SDCardUnConnectionPrompt;    //SD卡未插提示
    U8 KnobTone;        //旋钮音 开关
    U16 Reserved[3];
}SYSTEMCONFIG, *PSYSTEMCONFIG;

typedef struct TWifiConfig
{
    U8 Switch;                      //0:关 1:开
    char SSID[33];                  //SSID
    char PWD[MAX_WIFI_PWD_LEN + 1]; //Password
}WIFICONFIG, *PWIFICONFIG;

#if (ENABLE_ENCRYPTION == 1)
typedef struct {
    uint8_t encrypted_data[sizeof(WIFICONFIG) + IV_SIZE + TAG_SIZE]; // 加密后的WIFI配置（包含IV、GCM加密、标签）
    uint8_t checksum;                     // 结构体校验和
} SECURE_WIFI_CONTAINER;
#endif

typedef struct TBluetoothConfig
{
    U8 Switch;
}BLUETOOTHCONFIG, *PBLUETOOTHCONFIG;

#define PARAMETER_CONFIG_HEAD   0x02
#define MAX_WORK_MODE_CNT	    12

typedef struct  TParameterConfig
{
    U8 Head;					//包头
    SYSTEMCONFIG SysConfig;
    WIFICONFIG WifiConfig;     //WIFI配置(保留字段，不使用)
    BLUETOOTHCONFIG BtConfig;   //蓝牙配置

    U8 Ramp;					//延时升压

    U8 AutoOn;					//自动开机	
    U8 AutoOff;                 //自动关机
    U8 ESENS;
    U8 ISENS;	             
    
    WARMCONFIG WarmConfig;	
    
    U16 StartPress[MAX_WORK_MODE_CNT];	//初始压力
    U16 Epap[MAX_WORK_MODE_CNT];				//呼气压力
    U16 Ipap[MAX_WORK_MODE_CNT];				//吸气压力
    U16 MinEpap[MAX_WORK_MODE_CNT];			//最小呼气压力
    U16 MaxIpap[MAX_WORK_MODE_CNT];			//最大吸气压力
    U16 MaxPS[MAX_WORK_MODE_CNT];				//最大PS
    U16 MinIpap[MAX_WORK_MODE_CNT];			//最小吸气压力
    U16 MinPress[MAX_WORK_MODE_CNT];		//最小压力
    U16 MaxPress[MAX_WORK_MODE_CNT];		//最大压力
    U16 WorkPress[MAX_WORK_MODE_CNT];		//工作压力
    U16 VT[MAX_WORK_MODE_CNT];				//目标潮气量
    U8 InspTime[MAX_WORK_MODE_CNT];         //吸气时间
    U8 MaxInspTime[MAX_WORK_MODE_CNT];      //最大吸气时间
    U8 MinInspTime[MAX_WORK_MODE_CNT];      //最小吸气时间
    U8 ISlop[MAX_WORK_MODE_CNT];            //升压时间
    U8 Bpm[MAX_WORK_MODE_CNT];				//呼吸频率
    U8 Belex[MAX_WORK_MODE_CNT];            //压力卸载
    
    U8 iRamp;                               //自动延时开关，为1时为开，为0时为关
    U8 SmartPressure;                       //智能压力，仅iAPAP模式使用
    U8 SeparateNight;                       //分夜,0:关闭、120-240min，步长60Min
    U8 BoostSensitivity;                    //升压灵敏度 0-柔和 1-标准 2-灵敏
    U8 WorkMode;								//工作模式
    U8 TargetHighFlow;                      //目标流量值，仅高流量模式有效
    U8 VAFSwtich;                           //VAF开关
    U8 U8Reserved1[3];
    U8 ESlop[MAX_WORK_MODE_CNT];            //降压时间
    U8 KpapParm[2][18];                     //KPAP参数
    U8 Reserved[24];
    U8 NetConfigFlag;                       //用于存储是否收到网络配置数据，仅网络参数配置数据存储时使用，当收到网络配置数据时，则置标志为1，导入至设备后，置标志为0
    U8 AppConfigFlag;                       //用于APP导入配置数据，当导入数据后，置标志为0
    U8 U8Reserved2[2];
    U8 CheckSum;
}PARAMETERCONFIG, *PPARAMETERCONFIG;

#if (ENABLE_ENCRYPTION == 1)
typedef struct {
    uint8_t encrypted_data[sizeof(PARAMETERCONFIG) + IV_SIZE + TAG_SIZE]; // 加密后的参数配置（包含IV、GCM加密、标签）
    uint8_t checksum;                     // 结构体校验和
} SECURE_PARAMETER_CONTAINER;
#endif

typedef struct TTitConfig
{
    U16 TitPress[4];		//0:0 1:4 2:10 3:20
    U16 TitFlow[4];		  //0:30 1:80 2:120
    U16 TitPressPWM[4];	//0:0 1:4 2:10: 3:20
    U32 TitPressTime;
    U32 TitFlowTime;
    U16 Tit36_5cmH2OPWM;
    U16 TitSelfCheckPWM;
    U16 Adp801TitFlow[6];//0:0 1:30 2:80 3:140
    U16 Adp801TitFlowArray[6];
	U8 	Adp801TitFlowType;
	U8	Adp801TitFlowSize;
    U16 Reserved[5];
    U8 CheckSum;
}TITCONFIG, *PTITCONFIG;
extern const TITCONFIG* TitData;
#define INVALID_DATA    0xffff

typedef struct
{
#define FLOW_CALIB_DATA_NUM_MAX (32)
#define FLOW_CALIB_TYPE_MANUAL  (0)
#define FLOW_CALIB_TYPE_AUTO    (1)
    uint8_t bCalibType;
    uint8_t bDataSize;
    uint8_t bReserved[2];
    uint32_t wDateTime;
    int16_t shSensorCode[FLOW_CALIB_DATA_NUM_MAX];
    int16_t shPhyFlow[FLOW_CALIB_DATA_NUM_MAX];
    int16_t shOtherVal[FLOW_CALIB_DATA_NUM_MAX];
    uint8_t bCheckSum;
} AutoFlowCalibData_t;

typedef struct
{
#define PRESS_CALIB_DATA_NUM_MAX (8)
#define PRESS_CALIB_TYPE_MANUAL  (0)
#define PRESS_CALIB_TYPE_AUTO    (1)
    uint8_t bCalibType;
    uint8_t bDataSize;
    uint8_t bReserved[2];
    uint32_t wDateTime;
    int16_t shSensorCode[PRESS_CALIB_DATA_NUM_MAX];
    uint16_t hPhyPress[PRESS_CALIB_DATA_NUM_MAX];
    uint16_t hMotorPwmVal[PRESS_CALIB_DATA_NUM_MAX];
    uint8_t bCheckSum;
} AutoPressCalibData_t;

typedef struct TRealDisplayData
{
    U8  IEStatus;
    I16 Flow;
    U16 Pressure;
    U16 IERate;
    U16 InspTime;
    I16 Leak;
    U16 TV;
    U16 MV;
    U16 RR;
    U8 SpO2;
    U8 PR;
    U8 OxiConnectStatus;
    U16 PipelineTemperature;
}REALDISPLAYDATA, *PREALDISPLAYDATA;

//仅握手指令需要发送TNetDeviceInfo结构体完整数据
typedef struct TNetDeviceInfo
{
    U8 Model;
    U8 ProNum;
    U8 ProduceYear;
    U8 ProduceMon;
#if (NETWORD_PROTOCAL_VERSION)
    U32 ProductTailCode;
    U8 FlowSensorType;
    U8 PressSensorType;
#else
    U16 ProductTailCode;
#endif
    
    U32 AppVersion;
    U32 FontLibVersion;
    U32 PicLibVersion;
    U8 Customized;
    U8 Reserve[16];
}NETDEVICEINFO, *PNETDEVICEINFO;

typedef struct TNetDateTime
{
    U8 Year;
    U8 Mon;
    U8 Day;
    U8 Hour;
    U8 Min;
    U8 Sec;
}NETDATETIME, *PNETDATETIME;

typedef struct TNetSystemConfig
{
    U8 Language;								//语言
    U16 BackLightTime;					//背光时间
    U8 BackLightBrigntNess;			//背光亮度
    U8 PressUnit;					//压力单位 0-cmH2O;  1-hPa
    U8 UseCycle;                    //使用周期，0-6 0:1day 1:7days 2:30days 3:90days 4:180days 5:365days
    U8 TubeType;                    //管路类型，0：15mm普通管路、1：19mm普通管路、2：加热管路
    U8 MaskType;                    //面罩类型，0：鼻罩、1：口鼻罩、2:鼻枕
    U8 ECOType;                     //系统工作模式：0:舒适模式、1:节能模式、2:飞行模式
    U8 HighPressureAlm;                     //报警高气压
    U8 LowPressureAlm;                     //报警低气压
    U8 HighLeakAlm;                     //报警高漏气
    U16 LowMVAlm;                          //低通气
    U8 ApneaTimeAlm;                          //窒息时间      
    U8 ReplaceFilterAlm;
    U8 ReplaceMaskAlm;
    U8 ReplaceTubeAlm;
    U16 Reserved[7];
}NETSYSTEMCONFIG, *PNETSYSTEMCONFIG;

typedef struct TNetWarmConfig
{        
    U8 WarmLevel;        
    U8 PreHeatSwitch;   //预热开关
    U8 PreHeatTime;     //预热时间为10-30分钟，步长为10分钟吗，默认设置30分钟，单位为分钟
    U8 TubeWarmLevel;   //管路加热档位,1-5档，默认为3档
}NETWARMCONFIG, *PNETWARMCONFIG;

typedef struct  TNetParameterConfig
{
    NETSYSTEMCONFIG SysConfig;
    U8 Ramp;									//延时升压
    U8 MaxRamp;															
    U8 AutoOn;						
    U8 AutoOff;
    U8 ESENS;
    U8 ISENS;	
    U8 InspTime;
    U8 MaxInspTime;
    U8 MinInspTime;
    U8 ISlop;
    U8 Bpm;									//呼吸频率 
    U8 Belex;                 //压力卸载
    
    NETWARMCONFIG WarmConfig;	
    
    U16 StartPress;	//初始压力
    U16 Epap;				//呼气压力
    U16 Ipap;				//吸气压力
    U16 MinEpap;			//最小呼气压力
    U16 MaxIpap;			//最大吸气压力
    U16 MaxPS;				//最大PS
    U16 MinIpap;			//最小吸气压力
    U16 MinPress;		//最小压力
    U16 MaxPress;		//最大压力
    U16 WorkPress;		//工作压力
    U16 VT;					//目标潮气量
    U16 iRamp;   //bit0-bit15：分别对应各种模式自动延时开关，为1时为开，为0时为关
    U8 SmartPressure;                       //智能压力，仅iAPAP模式使用
    U8 SeparateNight;                       //分夜,0:关闭、120-240min，步长60Min
    U8 BoostSensitivity;                    //升压灵敏度 0-柔和 1-标准 2-灵敏
    U8 WorkMode;								//工作模式
    U8 TargetHighFlow;
    U8 VafReserved;                        //兼容二代云平台协议，增加VAF位置
	U8 UpLoadChannelType;                  //上传的通道类型 0:WIFI;1:SIM MODULE;...
    U8 KpapParm[18];   //KPAP参数
    U8 AppSoftVersion[32]; //软件版本号
	U8 Reserved0[21];      //保留字
}NETPARAMETERCONFIG, *PNETPARAMETERCONFIG;

typedef struct TNetPowerOnInfo
{
    U8 Model;
    U8 ProNum;
    U8 ProduceYear;
    U8 ProduceMon;
#if (NETWORD_PROTOCAL_VERSION)
    U32 ProductTailCode; 
    U8 FlowSensorType;
    U8 PressSensorType; 
#else
    U16 ProductTailCode; 
    U16 DayPowerOnTimes;
#endif    
    NETDATETIME DateTime;
    NETPARAMETERCONFIG Config;
}NETPOWERONINFO, *PNETPOWERONINFO;

typedef struct TNetPowerOffInfo
{
    U8 Model;
    U8 ProNum;
    U8 ProduceYear;
    U8 ProduceMon;
#if (NETWORD_PROTOCAL_VERSION)
    U32 ProductTailCode; 
    U8 FlowSensorType;
    U8 PressSensorType; 
#else
    U16 ProductTailCode; 
    U16 DayPowerOnTimes;
#endif    
    NETDATETIME DateTime;
}NETPOWEROFFINFO, *PNETPOWEROFFINFO;


typedef struct TNetDataHead
{
    U8 Head[2];
    U8 Cmd;
    U8 Len[2];
#if (NETWORD_PROTOCAL_VERSION)
    U8 ProtocolVersion;
#endif
    U8 Index;
}NETDATAHEAD, *PNETDATAHEAD;

typedef struct TRealDataInfo
{
    U8 Model;
    U8 ProNum;    
    U8 ProduceYear;
    U8 ProduceMon;
#if (NETWORD_PROTOCAL_VERSION)    
    U32 ProductTailCode; 
    U8 FlowSensorType;
    U8 PressSensorType;
#else
    U16 ProductTailCode; 
#endif    
    U16 DataSerialID;
    NETDATETIME DateTime;
    U8 Reserved[3];  
}REALDATAHEAD, *PREALDATAHEAD;

typedef struct TMachineInfo
{
    U8 SoftMajorVersion;
    U8 SoftMinorVersion;
    U8 SoftReVersion;
    U8 SoftBuildVersion;
    U8 LcdType;
    U32 SoftBuildFullVersion;         //完整版本号
    U8 SoftCorrectVersion;
    U8 Reserve[26];
    U8 CheckSum;
}MACHINEINFO, *PMACHINEINFO;

#pragma pack(pop)

#endif

