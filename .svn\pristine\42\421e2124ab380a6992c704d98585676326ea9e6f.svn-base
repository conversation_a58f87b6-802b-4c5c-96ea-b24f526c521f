#include "ConfigSave.h"
#include "MultiLanguage.h"
#include <assert.h>
#include "bsp_bldcm_control.h"
#include "GlobalVariable.h"
#include "StaterBar.h"
#include "WifiUartModule.h"
#include "bsp_MX25L256.h"
#include "CalcDateTime.h"
#include "SelfTest.h"
#include "TaskResourceManager.h"
#include "PipeDispose.h"
#include "GuiTask.h"
#include "FlowSensorType.h"


#if (ENABLE_ENCRYPTION == 1)
#include "crypto.h"
#endif
static OS_Q m_HumOrPiPeChgFlagGroup;
MQ_REGISTER(m_HumOrPiPeChgFlagGroup, "Hum or Pipe Change Flag", 8);

const RUNTIMEDATA g_DefaultRunTimeData =
{
    0,      //运行时间
    0,      //更换滤绵时间
    0,      //更换面罩时间
    0,      //更换管路时间
    {0},    //0
    0       //0
};

const MACHINECONFIG g_DefaultMachineConfig =
{
    {0x00, 0x00, 0x01, 0x00, 0x00},               //FunctionEnable
    0x0F,               //SupportLanguage
    0,                  //TailCode
    6,                  //Month
    22,                 //Year
    3,                  //ProNum
    0,                  //Flow Sensor Type
    0,                  //Pressure Sensor Type
    RF_20C_S1_X1,       //DType
    0,                  //CheckSum
};

const PARAMETERCONFIG g_DefaultParameterConfig =
{
    PARAMETER_CONFIG_HEAD,  //Head
    {
        0x00,       //Language
        600,        //BackLightTime
        3,          //BackLightBrigntNess
        0,          //PressUnit
        0,          //use cycle  0:1, 1:7, 2:30, 3:90, 4:180, 5:365

        1,          //管路类型，0：15mm普通管路、1：19mm普通管路、2：加热管路
        2,                    //面罩类型：0--鼻罩；1--口鼻罩(一代)；2--口鼻罩(二代)；3--口鼻罩(二代P)；4--口鼻罩(三代)；5--鼻枕
        0,          //系统工作模式：0:标准模式、1:节能模式、2:飞行模式

        0,          //报警高气压
        0,          //报警低气压
        0,          //高漏气
        0,          //低通气
        0,          //窒息时间
        0,          //更换滤绵开关，0:关 1:1个月 3:3个月 6:6个月 12:1年
        0,          //更换面罩开关，0:关 1:1个月 3:3个月 6:6个月 12:1年
        0,          //更换管路开关，0:关 1:1个月 3:3个月 6:6个月 12:1年
        0,      //SD卡未插提示
        1,      //旋钮音 开关
//        0,      //漏气量显示:含面罩和不含面罩
//        0,      //服务器: 国内服务器和国外服务器
        {0},
    },
    {
        //WIFI配置
        0,
        "",
        "",
    },
    {
        //蓝牙配置
        0,
    },
    0,      //Ramp
    0,      //AutoOn
    0,      //AutoOff
    3,      //ESENS
    2,      //ISENS

    {
        2,  //湿化器档位

        0,   //预热开关
        10,  //预热时间为10-30分钟，步长为10分钟吗，默认设置30分钟，单位为分钟
        16,   //管路加热档位,1-5档，默认为16温度
    },
    {40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40},  //初始压力
    {50, 50, 50, 50, 50, 40, 40, 50, 50, 50, 50, 50},   //呼气压力
    {150, 150, 150, 150, 150, 50, 70, 150, 150, 150, 150, 150}, //吸气压力
    {40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40},   //最小呼气压力
    {200, 200, 200, 200, 200, 250, 160, 200, 250, 250, 200, 200}, //最大吸气压力
    {80, 80, 80, 80, 80, 80, 60, 80, 80, 80, 80, 80},   //最大PS
    {150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150}, //最小吸气压力
    {50, 40, 40, 50, 50, 50, 40, 50, 50, 50, 50, 50},  //最小压力
    {150, 160, 160, 150, 150, 150, 200, 150, 150, 150, 150, 150}, //最大压力
    {100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100},  //工作压力
    {600, 600, 600, 600, 600, 600, 600, 600, 600, 600, 600, 600},//目标潮气量
    {10, 10, 10, 10, 10, 13, 10, 10, 10, 10, 10, 10},       //InspTime
    {20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20},       //MaxInspTime
    {5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5},               //MinInspTime
    {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2},                   //ISlop;
    {15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15},       //BMP
    {1, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1},               //Belex

    0,                                //自动延时开关，为1时为开，为0时为关
    50,                                  //智能压力，仅iAPAP模式使用
    0,                                  //分夜,0:关闭、120-240min，步长60Min
    1,      //升压灵敏度
    5,      //WorkMode
    60,     //Target High Flow
    0,      //VAF Swtich
    {0},
    {3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3},     //ESlop
    {0},
    0,
    0,
    {0},
    0,
};

const TITCONFIG g_DefaultTitConfig =
{
    {404, 733, 1237, 2066},     //TitPress[4];      //0:0 1:4 2:10 3:20
    {584, 3456, 7259},                  //TitFlow[4];         //0:30 1:80 2:120
    {500, 1000, 2000, 3000},        //TitPressPWM[4];   //0:0 1:4 2:10: 3:20
    0,
    0,
    MAX_HFDUTY_CYCLE,
    MAX_PERFORMANCE_TEST_CYCLE,
    {0, 660, 3654, 9660}, //Adp801TitFlow[4];    0:0 1:30 2:80 3:140
    {0},
    0,
};

const AutoFlowCalibData_t g_DefaultAutoFlowCalibData =
{
    0
};

const AutoPressCalibData_t g_DefaultAutoPressCalibData =
{
    0
};

U32 g_U32MotorEvent = 0;
void PostMotorEvent(U32 Event)
{
    OS_ERR err;
    g_U32MotorEvent = Event;
    OSQPost(&g_MotorMsg, (void *)&g_U32MotorEvent, (OS_MSG_SIZE)1, (OS_OPT)OS_OPT_POST_FIFO, (OS_ERR *)&err);
}

/*****************************************************************************
 函 数 名  : PostHumEvent
 功能描述  : 加湿器参数改变事件通知
 输入参数  : Event

 输出参数  :
 返 回 值  :
 调用函数  :

 修改历史      :
  1.日    期   :
    作    者   :CCK
    修改内容   :
*****************************************************************************/
void CConfigSave::PostHumOrPipeEvent(HUMORPIPEEVENT Event)
{
    OS_ERR err;
    m_U32HumOrPipeEvent = (U32)Event;
    OSQPost(&m_HumOrPiPeChgFlagGroup, (void *)&m_U32HumOrPipeEvent, (OS_MSG_SIZE)1, (OS_OPT)OS_OPT_POST_FIFO,
                    (OS_ERR *)&err);
}

/*****************************************************************************
 函 数 名  : GetHumFlagGroup
 功能描述  : 加湿器参数改变事件通知
 输入参数  : Event

 输出参数  :
 返 回 值  :
 调用函数  :

 修改历史      :
  1.日    期   :
    作    者   :CCK
    修改内容   :
*****************************************************************************/
U32 CConfigSave::GetHumOrPipeFlagGroup(void)
{
    OS_ERR  err;
    OS_MSG_SIZE msg_size;
    U32 *pU32Msg;
    /* 请求消息队列 g_MotorMsg 的消息 */
    pU32Msg = (U32 *)OSQPend((OS_Q *)&m_HumOrPiPeChgFlagGroup,                     //消息变量指针
                                    (OS_TICK)OS_CFG_TICK_RATE_HZ / 2,    //等待时长为500ms
                                    (OS_OPT)OS_OPT_PEND_BLOCKING,         //如果没有获取到信号量等待
                                    (OS_MSG_SIZE *)&msg_size,                   //获取消息的字节大小
                                    (CPU_TS *)0,                           //获取任务发送时的时间戳
                                    (OS_ERR *)&err);                       //返回错误、
    if(err == OS_ERR_TIMEOUT)
    {
        return OS_ERR_TIMEOUT;
    }
    return (*pU32Msg);
}

CConfigSave::CConfigSave()
{

}

CConfigSave::~CConfigSave()
{
}

U8 CConfigSave::CheckSum(void *b, int Size)
{
    U8 *spU8buff = (U8 *)b;
    U8 sU8Sum = 0;
    while(Size)
    {
        sU8Sum += *spU8buff++;
        Size--;
    }
    return (~sU8Sum);
}

/*************************************************
Function: LoadLanguage
Description: 装载当前可支持的语言，以及配置当前语言
Input: void
Output: void
Return: bool 成功标志
Others:
必须在装载/修改配置数据成功后，才能调用此函数，否则会装载错误
*************************************************/
void CConfigSave::LoadLanguage()
{
    if(g_ConfigSave.GetMarketAreaType() == MARKET_AREA_FDA && g_ConfigSave.GetParameter(LANGUAGE) > LANG_ENGLISH)
    {
        g_ConfigSave.SetParameter(LANGUAGE, LANG_ENGLISH);
        lang_setting(m_ParameterConfig.SysConfig.Language);
        UpdateTopStateBarMenuString(GetMultiLanguageString(SYSSETTING_IDX));
    }
    else
    {
        lang_setting(m_ParameterConfig.SysConfig.Language);
    }
}

extern uint16_t g_sU16ECOStandardModeValue[];
void CConfigSave::Init(CSdCardDataSave *pSdCardDataSave)
{
    uint8_t i = 0;
    m_pSdCardDataSave = pSdCardDataSave;
    LoadWifiConfig(); // 加载WIFI配置
    LoadMachineData(0);
//    m_MachineConfig.DType = RF_30F_H9_P9;
//    m_MachineConfig.SupportLanguage = 0x03;
    if(m_MachineConfig.ProNum > 0x03)
    {
#if (LCD_TYPE == LCD_5_TFT)
        m_MachineConfig.ProNum = 0;
#else
#if (LCD_TYPE == LCD_35_TFT)
        m_MachineConfig.ProNum = 1;
#else
        m_MachineConfig.ProNum = 3;
#endif
#endif
    }
//    m_MachineConfig.Year = 21;
//    m_MachineConfig.Month = 12;
//    m_MachineConfig.TailCode = 0x01;

    LoadParameterData();
#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
    LoadNetParameterData();
    if(m_OtherParameterConfig.NetConfigFlag)
    {
        m_OtherParameterConfig.NetConfigFlag = 0;
        ChangeParameterFromOtherConfig(&m_ParameterConfig, &m_OtherParameterConfig);
        g_EdfDataSave.UpdateSmartPressure();
        SaveParameterData();
        SaveNetParameterData();
    }
#endif
    LoadRunTimeData();
    LoadCaliData();
    load_auto_flow_calib_data();
    load_auto_press_calib_data();
    LoadLanguage();

    if (g_ConfigSave.GetMarketAreaType() > MARKET_AREA_CE)
    {
        g_ConfigSave.SetParameter(MARKET_AREA_TYPE, MARKET_AREA_CHINA, true);
    }

    //重新上电后，默认将预热开关关闭，管路类型为19mm普通管路
    m_ParameterConfig.WarmConfig.PreHeatSwitch = 0;
    m_ParameterConfig.SysConfig.TubeType = 1;

    //当ESlop数值异常时，写默认值
    for(i = SYS_WM_CPAP; i <= SYS_WM_HIGHFLOW; i++)
    {
        if(m_ParameterConfig.ESlop[i] < SYS_PARA_ESLOP_MIN || m_ParameterConfig.ESlop[i] > SYS_PARA_ESLOP_MAX)
        {
            m_ParameterConfig.ESlop[i] = 3;
        }
    }

    CalCntForJudgeAirwayObstruction();  //气道阻塞时长计算: FDA 8s; 其他: 5s
}

void CConfigSave::StartPressCali(U8 Flag, U16 PWM)
{
    if(Flag)
    {
        PostMotorEvent(BEGIN_PRESS_CALI);
        SetBldcmPwm(PWM);
    }
    else
    {
        PostMotorEvent(STOP_PRESS_CALI);
    }
}

void CConfigSave::StartFlowCali(U8 Flag, U16 PWM)
{
    if(Flag)
    {
        PostMotorEvent(BEGIN_FLOW_CALI);
        SetBldcmPwm(PWM);
    }
    else
    {
        PostMotorEvent(STOP_FLOW_CALI);
    }
}
void CConfigSave::StartRetest(U8 Flag)
{
    if(Flag)
    {
        PostMotorEvent(BEGIN_RETEST);
    }
    else
    {
        PostMotorEvent(STOP_RETEST);
    }
}

void CConfigSave::StartMaskWearingTest(U8 Flag)
{
    if(Flag)
    {
        PostMotorEvent(BEGIN_MASKTEST_FLAG);
    }
    else
    {
        PostMotorEvent(STOP_MASKTEST_FLAG);
        SetBldcmPwm(0);
    }
}

void CConfigSave::StartPerformanceTest(U8 Flag)
{
    if(Flag)
    {
        PostMotorEvent(BEGIN_PERFORMANCE_FLAG);
    }
    else
    {
        PostMotorEvent(STOP_PERFORMANCE_FLAG);
        SetBldcmPwm(0);
    }
}

void CConfigSave::LoadDefaultRunTimeData(U8 DefaultAll)
{
    U32 sU32RunTime = m_RunTimeData.U32RunTime;
    memcpy(&m_RunTimeData, &g_DefaultRunTimeData, sizeof(m_RunTimeData));
    if(DefaultAll == 0 && sU32RunTime != 0xFFFFFFFF)
    {
        m_RunTimeData.U32RunTime = sU32RunTime;
    }
    SaveRunTimeData();
}

bool CConfigSave::LoadRunTimeData()
{
    U8 i = 0;
    U16 sU16EEPROMAddr[2] = {EEPROM_RUNTIME_DATA_ADDR1, EEPROM_RUNTIME_DATA_ADDR2};
    U16 sU16SpiFlashAddr[2] = {SPI_FLASH_RUNTIME_DATA_ADDR1, SPI_FLASH_RUNTIME_DATA_ADDR2};
    for(i = 0; i < 2; i++)
    {
        if(ReadEEPROM(sU16EEPROMAddr[i], (U8 *)&m_RunTimeData, sizeof(m_RunTimeData)))      //此处有无失败可能?
        {
            if(CheckSum((U8 *)&m_RunTimeData, sizeof(m_RunTimeData) - 1) == m_RunTimeData.CheckSum)
            {
                return true;
            }
        }
    }
    for(i = 0; i < 2; i++)
    {
        if(SPIFLASH_ReadBuff((U8 *)&m_RunTimeData, sU16SpiFlashAddr[i],
                                        sizeof(m_RunTimeData)) == 0)        //此处有无失败可能?
        {
            if(CheckSum((U8 *)&m_RunTimeData, sizeof(m_RunTimeData) - 1) == m_RunTimeData.CheckSum)
            {
                return true;
            }
        }
    }

    LoadDefaultRunTimeData();
}

bool CConfigSave::SaveRunTimeData()
{
    m_RunTimeData.CheckSum = CheckSum(&m_RunTimeData, sizeof(m_RunTimeData) - 1);
    WriteEEPROM(EEPROM_RUNTIME_DATA_ADDR1, (U8 *)&m_RunTimeData, sizeof(m_RunTimeData));
    WriteEEPROM(EEPROM_RUNTIME_DATA_ADDR2, (U8 *)&m_RunTimeData, sizeof(m_RunTimeData));

    SPIFLASH_WriteBuff((U8 *)&m_RunTimeData, SPI_FLASH_RUNTIME_DATA_ADDR1, sizeof(m_RunTimeData));
    SPIFLASH_WriteBuff((U8 *)&m_RunTimeData, SPI_FLASH_RUNTIME_DATA_ADDR2, sizeof(m_RunTimeData));
}

void CConfigSave::LoadDefaultMachineData(U8 bNeedSaveFlag)
{
    memcpy(&m_MachineConfig, &g_DefaultMachineConfig, sizeof(m_MachineConfig));
    if(bNeedSaveFlag)
    {
        SaveMachineData();
    }
}

bool CConfigSave::LoadMachineData(bool DefaultFlag)
{
    U8 i = 0;
    U16 sU16EEPROMAddr[2] = {EEPROM_MACHINE_CONFIG_ADDR1, EEPROM_MACHINE_CONFIG_ADDR2};
    U16 sU16SpiFlashAddr[2] = {SPI_FLASH_MACHINE_CONFIG_ADDR1, SPI_FLASH_MACHINE_CONFIG_ADDR2};
    OLDMACHINECONFIG OldMachineConfig;
    if(DefaultFlag)
    {
        LoadDefaultMachineData();
        return true;
    }
    for(i = 0; i < 2; i++)
    {
        if(ReadEEPROM(sU16EEPROMAddr[i], (U8 *)&m_MachineConfig, sizeof(m_MachineConfig)))      //此处有无失败可能?
        {
            if(CheckSum((U8 *)&m_MachineConfig, sizeof(m_MachineConfig) - 1) == m_MachineConfig.CheckSum)
            {
                return true;
            }
        }
        if(SPIFLASH_ReadBuff((U8 *)&m_MachineConfig, sU16SpiFlashAddr[i],
                                        sizeof(m_MachineConfig)) == 0)        //此处有无失败可能?
        {
            if(CheckSum((U8 *)&m_MachineConfig, sizeof(m_MachineConfig) - 1) == m_MachineConfig.CheckSum)
            {
                return true;
            }
        }
    }
    for(i = 0; i < 2; i++)
    {
        if(ReadEEPROM(sU16EEPROMAddr[i], (U8 *)&OldMachineConfig, sizeof(OldMachineConfig)))        //此处有无失败可能?
        {
            if(CheckSum((U8 *)&OldMachineConfig, sizeof(OldMachineConfig) - 1) == OldMachineConfig.CheckSum)
            {
                m_MachineConfig = OldMachineConfig;
                SaveMachineData();
                return true;
            }
        }
        if(SPIFLASH_ReadBuff((U8 *)&OldMachineConfig, sU16SpiFlashAddr[i],
                                        sizeof(OldMachineConfig)) == 0)      //此处有无失败可能?
        {
            if(CheckSum((U8 *)&OldMachineConfig, sizeof(OldMachineConfig) - 1) == OldMachineConfig.CheckSum)
            {
                m_MachineConfig = OldMachineConfig;
                SaveMachineData();
                return true;
            }
        }
    }

    LoadDefaultMachineData(0);
    return false;
}

void CConfigSave::SaveMachineData()
{
    m_MachineConfig.CheckSum = CheckSum(&m_MachineConfig, sizeof(m_MachineConfig) - 1);
    WriteEEPROM(EEPROM_MACHINE_CONFIG_ADDR1, (U8 *)&m_MachineConfig, sizeof(m_MachineConfig));
    WriteEEPROM(EEPROM_MACHINE_CONFIG_ADDR2, (U8 *)&m_MachineConfig, sizeof(m_MachineConfig));

    SPIFLASH_WriteBuff((U8 *)&m_MachineConfig, SPI_FLASH_MACHINE_CONFIG_ADDR1, sizeof(m_MachineConfig));
    SPIFLASH_WriteBuff((U8 *)&m_MachineConfig, SPI_FLASH_MACHINE_CONFIG_ADDR2, sizeof(m_MachineConfig));
}

void CConfigSave::SaveMachineData(MACHINECONFIG *pConfig)
{
    memcpy(&m_MachineConfig, pConfig, sizeof(MACHINECONFIG));
    SaveMachineData();
}

//根据项目代码与设备机型获取默认工作模式
U8 CConfigSave::GetDefaultWorkMode()
{
    U8 sU8WorkMode = 0;
    if(m_MachineConfig.ProNum < 2)    //ResFree
    {
        switch(m_MachineConfig.DType)
        {
            case RF_20C_S1_X1:
            case RF_20A_S2_X2:
            case RF_20I_S3_X3:
            case RF_30A_S7_X7:
            {
                sU8WorkMode = SYS_WM_CPAP;
                break;
            }
            case RF_25A_S5_X5:
            {
                if(GetMarketAreaType())
                {
                    sU8WorkMode = SYS_WM_CPAP;
                }
                else
                {
                    sU8WorkMode = SYS_WM_APAP;
                }
                break;
            }
            case RF_25S_S6_X6:
            {
                sU8WorkMode = SYS_WM_S;
                break;
            }
            default:
            {
                sU8WorkMode = SYS_WM_ST;
                break;
            }
        }
    }
    else    //ResHope
    {
        switch(m_MachineConfig.DType)
        {
            case RF_20A_S2_X2:
            case RF_20I_S3_X3:
            case RF_30A_S7_X7:
            {
                sU8WorkMode = SYS_WM_CPAP;
                break;
            }
            case RF_20C_S1_X1:
            case RF_25A_S5_X5:
            {
                sU8WorkMode = SYS_WM_APAP;
                break;
            }
            case RF_25S_S6_X6:
            {
                sU8WorkMode = SYS_WM_S;
                break;
            }
            default:
            {
                sU8WorkMode = SYS_WM_ST;
                break;
            }
        }
    }
    return sU8WorkMode;
}

void CConfigSave::LoadDefaultParameterData(U8 DefaultAll, U8 bNeedSaveFlag)
{
    U8 Language = m_ParameterConfig.SysConfig.Language;
    memcpy(&m_ParameterConfig, &g_DefaultParameterConfig, sizeof(m_ParameterConfig));
    m_ParameterConfig.WorkMode = GetDefaultWorkMode();
    if(g_U8IsSleepVentilator)    //睡眠型默认为鼻罩
    {
        m_ParameterConfig.SysConfig.MaskType = 0;
    }
    else    //治疗型默认为口鼻罩
    {
        m_ParameterConfig.SysConfig.MaskType = 1;
    }
    if(DefaultAll == 0)
    {
        m_ParameterConfig.SysConfig.Language = Language;
    }
    if(bNeedSaveFlag)
    {
        SaveParameterData();
    }
}

void CConfigSave::ClearRunTime(void)
{
    m_RunTimeData.U32RunTime = 0;
    SaveRunTimeData();
}

bool CConfigSave::LoadParameterData()
{
    U8 i = 0;
    U16 sU16EEPROMAddr[2] = {EEPROM_PARAMETER_CONFIG_ADDR1, EEPROM_PARAMETER_CONFIG_ADDR2};
    U16 sU16SpiFlashAddr[2] = {SPI_FLASH_PARAMETER_CONFIG_ADDR1, SPI_FLASH_PARAMETER_CONFIG_ADDR2};

    for(i = 0; i < 2; i++)
    {
        if(ReadEEPROM(sU16EEPROMAddr[i], (U8 *)&m_ParameterConfig, sizeof(m_ParameterConfig)))      //此处有无失败可能?
        {
            if(CheckSum((U8 *)&m_ParameterConfig, sizeof(m_ParameterConfig) - 1) == m_ParameterConfig.CheckSum
                            && (m_ParameterConfig.Head == PARAMETER_CONFIG_HEAD))
            {
                if(m_ParameterConfig.SysConfig.Language >= LANG_NUM)
                {
                    m_ParameterConfig.SysConfig.Language = LANG_CHINESE;
                    SaveParameterData();
                }
                return true;
            }
        }
    }
    for(i = 0; i < 2; i++)
    {
        if(SPIFLASH_ReadBuff((U8 *)&m_ParameterConfig, sU16SpiFlashAddr[i],
                                        sizeof(m_ParameterConfig)) == 0)        //此处有无失败可能?
        {
            if(CheckSum((U8 *)&m_ParameterConfig, sizeof(m_ParameterConfig) - 1) == m_ParameterConfig.CheckSum
                            && (m_ParameterConfig.Head == PARAMETER_CONFIG_HEAD))
            {
                if(m_ParameterConfig.SysConfig.Language >= LANG_NUM)
                {
                    m_ParameterConfig.SysConfig.Language = LANG_CHINESE;
                    SaveParameterData();
                }
                return true;
            }
        }
    }
    LoadDefaultParameterData(1, 0);
    return false;
}

bool CConfigSave::SaveParameterData()
{
    m_ParameterConfig.CheckSum = CheckSum(&m_ParameterConfig, sizeof(m_ParameterConfig) - 1);
    WriteEEPROM(EEPROM_PARAMETER_CONFIG_ADDR1, (U8 *)&m_ParameterConfig, sizeof(m_ParameterConfig));
    WriteEEPROM(EEPROM_PARAMETER_CONFIG_ADDR2, (U8 *)&m_ParameterConfig, sizeof(m_ParameterConfig));

    SPIFLASH_WriteBuff((U8 *)&m_ParameterConfig, SPI_FLASH_PARAMETER_CONFIG_ADDR1, sizeof(m_ParameterConfig));
    SPIFLASH_WriteBuff((U8 *)&m_ParameterConfig, SPI_FLASH_PARAMETER_CONFIG_ADDR2, sizeof(m_ParameterConfig));

#if (ENABLE_SD_CHANGE_PARAMETER == 1)
    m_pSdCardDataSave->PostAddDataEvent(EXPORT_PARAMETER_CONFIG);
#endif
    return true;
}
#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
bool CConfigSave::LoadNetParameterData()
{
    U8 i = 0;
    U16 sU16EEPROMAddr[2] = {EEPROM_NET_PARAMETER_CONFIG_ADDR1, EEPROM_NET_PARAMETER_CONFIG_ADDR2};
    U16 sU16SpiFlashAddr[2] = {SPI_FLASH_NET_PARAMETER_ADDR1, SPI_FLASH_NET_PARAMETER_ADDR2};

    for(i = 0; i < 2; i++)
    {
        if(ReadEEPROM(sU16EEPROMAddr[i], (U8 *)&m_OtherParameterConfig, sizeof(m_OtherParameterConfig)))
        {
            if(CheckSum((U8 *)&m_OtherParameterConfig, sizeof(m_OtherParameterConfig) - 1) == m_OtherParameterConfig.CheckSum
                            && (m_OtherParameterConfig.Head == PARAMETER_CONFIG_HEAD))
            {
                return true;
            }
        }
    }
    for(i = 0; i < 2; i++)
    {
        if(SPIFLASH_ReadBuff((U8 *)&m_OtherParameterConfig, sU16SpiFlashAddr[i], sizeof(m_OtherParameterConfig)) == 0)
        {
            if(CheckSum((U8 *)&m_OtherParameterConfig, sizeof(m_OtherParameterConfig) - 1) == m_OtherParameterConfig.CheckSum
                            && (m_OtherParameterConfig.Head == PARAMETER_CONFIG_HEAD))
            {
                return true;
            }
        }
    }
    memcpy(&m_OtherParameterConfig, &m_ParameterConfig, sizeof(m_ParameterConfig));
    return false;
}

bool CConfigSave::SaveNetParameterData()
{
    m_OtherParameterConfig.CheckSum = CheckSum(&m_OtherParameterConfig, sizeof(m_OtherParameterConfig) - 1);
    WriteEEPROM(EEPROM_NET_PARAMETER_CONFIG_ADDR1, (U8 *)&m_OtherParameterConfig, sizeof(m_OtherParameterConfig));
    WriteEEPROM(EEPROM_NET_PARAMETER_CONFIG_ADDR2, (U8 *)&m_OtherParameterConfig, sizeof(m_OtherParameterConfig));

    SPIFLASH_WriteBuff((U8 *)&m_OtherParameterConfig, SPI_FLASH_NET_PARAMETER_ADDR1, sizeof(m_OtherParameterConfig));
    SPIFLASH_WriteBuff((U8 *)&m_OtherParameterConfig, SPI_FLASH_NET_PARAMETER_ADDR2, sizeof(m_OtherParameterConfig));

    return true;
}
#endif

void CConfigSave::LoadDefaultCaliData(U8 bNeedSaveFlag)
{
    memcpy(&m_TitConfig, &g_DefaultTitConfig, sizeof(m_TitConfig));
    if(bNeedSaveFlag)
    {
        SaveCaliData();
    }
}

bool CConfigSave::LoadCaliData()
{
    U8 i;
    U16 sU16EEPROMAddr[2] = {EEPROM_TIT_CONFIG_ADDR1, EEPROM_TIT_CONFIG_ADDR2};
    U16 sU16SpiFlashAddr[2] = {SPI_FLASH_TIT_CONFIG_ADDR1, SPI_FLASH_TIT_CONFIG_ADDR2};

    for(i = 0; i < 2; i++)
    {
        if(ReadEEPROM(sU16EEPROMAddr[i], (U8 *)&m_TitConfig, sizeof(m_TitConfig)))
        {
            if(CheckSum((U8 *)&m_TitConfig, sizeof(m_TitConfig) - 1) == m_TitConfig.CheckSum)
            {
                return true;
            }
        }
    }
    for(i = 0; i < 2; i++)
    {
        if(SPIFLASH_ReadBuff((U8 *)&m_TitConfig, sU16SpiFlashAddr[i],
                                        sizeof(m_TitConfig)) == 0)        //此处有无失败可能?
        {
            if(CheckSum((U8 *)&m_TitConfig, sizeof(m_TitConfig) - 1) == m_TitConfig.CheckSum)
            {
                return true;
            }
        }
    }

    LoadDefaultCaliData(0);
    return false;
}

bool CConfigSave::SaveCaliData()
{
    m_TitConfig.CheckSum = CheckSum(&m_TitConfig, sizeof(m_TitConfig) - 1);
    WriteEEPROM(EEPROM_TIT_CONFIG_ADDR1, (U8 *)&m_TitConfig, sizeof(m_TitConfig));
    WriteEEPROM(EEPROM_TIT_CONFIG_ADDR2, (U8 *)&m_TitConfig, sizeof(m_TitConfig));

    SPIFLASH_WriteBuff((U8 *)&m_TitConfig, SPI_FLASH_TIT_CONFIG_ADDR1, sizeof(m_TitConfig));
    SPIFLASH_WriteBuff((U8 *)&m_TitConfig, SPI_FLASH_TIT_CONFIG_ADDR2, sizeof(m_TitConfig));

    return true;
}

/**
 * @brief  获取自动流量校准数据
 * @retval 返回指向自动流量校准数据的指针
 */
const AutoFlowCalibData_t *CConfigSave::get_auto_flow_calib_data(void)
{
    return &m_AutoFlowCalibData;
}

/**
 * @brief  加载自动流量tit校准默认数据
 *         该函数用于将自动流量tit校准默认数据加载到m_AutoFlowCalibData中
 *         如果bNeedSaveFlag为1，则将默认数据保存到FLASH中
 * @param  bNeedSaveFlag  是否需要将默认数据保存到FLASH中
 */
void CConfigSave::load_default_auto_flow_calib_data(uint8_t bNeedSaveFlag)
{
    memcpy(&m_AutoFlowCalibData, &g_DefaultAutoFlowCalibData, sizeof(m_AutoFlowCalibData));
    if(bNeedSaveFlag)
    {
        save_auto_flow_calib_data(&g_DefaultAutoFlowCalibData);
    }
}

/**
 * @brief  加载自动流量tit校准数据
 *         该函数用于从FLASH中加载自动流量tit校准数据
 * @retval true  加载成功
 */
bool CConfigSave::load_auto_flow_calib_data(void)
{
    uint16_t hEEPROMAddr[2] = {EEPROM_AUTO_FLOW_CONFIG_ADDR1, EEPROM_AUTO_FLOW_CONFIG_ADDR2};
    uint16_t hSpiFlashAddr[2] = {SPI_FLASH_AUTO_FLOW_CALIB_ADDR1, SPI_FLASH_AUTO_FLOW_CALIB_ADDR2};

    for(int i = 0; i < 2; i++)
    {
        if(ReadEEPROM(hEEPROMAddr[i], (U8 *)&m_AutoFlowCalibData, sizeof(m_AutoFlowCalibData)))
        {
            if(CheckSum((U8 *)&m_AutoFlowCalibData, sizeof(m_AutoFlowCalibData) - 1) == m_AutoFlowCalibData.bCheckSum)
            {
                return true;
            }
        }
    }
    for(int i = 0; i < 2; i++)
    {
        if(SPIFLASH_ReadBuff((U8 *)&m_AutoFlowCalibData, hSpiFlashAddr[i],
                                        sizeof(m_AutoFlowCalibData)) == 0)
        {
            if(CheckSum((U8 *)&m_AutoFlowCalibData, sizeof(m_AutoFlowCalibData) - 1) == m_AutoFlowCalibData.bCheckSum)
            {
                return true;
            }
        }
    }

    load_default_auto_flow_calib_data(0);
    return false;
}

/**
 * @brief  保存自动流量tit校准数据
 *         该函数用于保存自动流量tit校准数据
 * @param  pAutoFlowCalibData  自动流量tit校准数据
 * @retval true 保存成功
 */
bool CConfigSave::save_auto_flow_calib_data(const AutoFlowCalibData_t *pAutoFlowCalibData)
{
    memcpy(&m_AutoFlowCalibData, pAutoFlowCalibData, sizeof(AutoFlowCalibData_t));
    m_AutoFlowCalibData.bCheckSum = CheckSum(&m_AutoFlowCalibData, sizeof(m_AutoFlowCalibData) - 1);
    WriteEEPROM(EEPROM_AUTO_FLOW_CONFIG_ADDR1, (U8 *)&m_AutoFlowCalibData, sizeof(m_AutoFlowCalibData));
    WriteEEPROM(EEPROM_AUTO_FLOW_CONFIG_ADDR2, (U8 *)&m_AutoFlowCalibData, sizeof(m_AutoFlowCalibData));

    SPIFLASH_WriteBuff((U8 *)&m_AutoFlowCalibData, SPI_FLASH_AUTO_FLOW_CALIB_ADDR1, sizeof(m_AutoFlowCalibData));
    SPIFLASH_WriteBuff((U8 *)&m_AutoFlowCalibData, SPI_FLASH_AUTO_FLOW_CALIB_ADDR2, sizeof(m_AutoFlowCalibData));
    return true;
}

/**
 * @brief  更新自动流量校准类型
 * @param  bType PRESS_CALIB_TYPE_MANUAL FLOW_CALIB_TYPE_AUTO
 * @return true
 * @return false
 */
bool CConfigSave::update_save_auto_flow_calib_type(uint8_t bType)
{
    if(bType != m_AutoFlowCalibData.bCalibType)
    {
        m_AutoFlowCalibData.bCalibType = bType;
        m_AutoFlowCalibData.bCheckSum = CheckSum(&m_AutoFlowCalibData, sizeof(m_AutoFlowCalibData) - 1);
        WriteEEPROM(EEPROM_AUTO_FLOW_CONFIG_ADDR1, (U8 *)&m_AutoFlowCalibData, sizeof(m_AutoFlowCalibData));
        WriteEEPROM(EEPROM_AUTO_FLOW_CONFIG_ADDR2, (U8 *)&m_AutoFlowCalibData, sizeof(m_AutoFlowCalibData));

        SPIFLASH_WriteBuff((U8 *)&m_AutoFlowCalibData, SPI_FLASH_AUTO_FLOW_CALIB_ADDR1, sizeof(m_AutoFlowCalibData));
        SPIFLASH_WriteBuff((U8 *)&m_AutoFlowCalibData, SPI_FLASH_AUTO_FLOW_CALIB_ADDR2, sizeof(m_AutoFlowCalibData));
    }
    return true;
}

/**
 * @brief  获取自动压力tit校准数据
 *         该函数用于获取自动压力tit校准数据
 * @retval 指向自动压力tit校准数据的指针
 */
const AutoPressCalibData_t *CConfigSave::get_auto_press_calib_data(void)
{
    return &m_AutoPressCalibData;
}

/**
 * @brief  加载自动压力tit校准默认数据
 *         该函数用于将自动压力tit校准默认数据加载到m_AutoPressCalibData中
 *         如果bNeedSaveFlag为1，则将默认数据保存到FLASH中
 * @param  bNeedSaveFlag  是否需要将默认数据保存到FLASH中
 */
void CConfigSave::load_default_auto_press_calib_data(U8 bNeedSaveFlag)
{
#ifdef AUTO_CALIB_PRESS_MODULE_ENABLE
    memcpy(&m_AutoPressCalibData, &g_DefaultAutoPressCalibData, sizeof(m_AutoPressCalibData));
    if(bNeedSaveFlag)
    {
        save_auto_press_calib_data(&g_DefaultAutoPressCalibData);
    }
#endif
}

/**
 * @brief  加载自动压力tit校准数据
 *         该函数用于从FLASH中加载自动压力tit校准数据
 * @retval true  加载成功
 */
bool CConfigSave::load_auto_press_calib_data(void)
{
#ifdef AUTO_CALIB_PRESS_MODULE_ENABLE
    uint16_t hEEPROMAddr[2] = {EEPROM_AUTO_PRESS_CONFIG_ADDR1, EEPROM_AUTO_PRESS_CONFIG_ADDR2};
    uint16_t hSpiFlashAddr[2] = {SPI_FLASH_AUTO_PRESS_CALIB_ADDR1, SPI_FLASH_AUTO_PRESS_CALIB_ADDR2};

    for(int i = 0; i < 2; i++)
    {
        if(ReadEEPROM(hEEPROMAddr[i], (U8 *)&m_AutoPressCalibData, sizeof(m_AutoPressCalibData)))
        {
            if(CheckSum((U8 *)&m_AutoPressCalibData, sizeof(m_AutoPressCalibData) - 1) == m_AutoPressCalibData.bCheckSum)
            {
                return true;
            }
        }
    }
    for(int i = 0; i < 2; i++)
    {
        if(SPIFLASH_ReadBuff((U8 *)&m_AutoPressCalibData, hSpiFlashAddr[i],
                                        sizeof(m_AutoPressCalibData)) == 0)
        {
            if(CheckSum((U8 *)&m_AutoPressCalibData, sizeof(m_AutoPressCalibData) - 1) == m_AutoPressCalibData.bCheckSum)
            {
                return true;
            }
        }
    }

    load_default_auto_press_calib_data(0);
#endif
    return false;
}

/**
 * @brief  保存自动压力tit校准数据
 *         该函数用于将自动压力tit校准数据保存到FLASH中
 * @param  pAutoPressCalibData  自动压力tit校准数据
 * @retval true  保存成功
 */
bool CConfigSave::save_auto_press_calib_data(const AutoPressCalibData_t *pAutoPressCalibData)
{
#ifdef AUTO_CALIB_PRESS_MODULE_ENABLE
    memcpy(&m_AutoPressCalibData, pAutoPressCalibData, sizeof(AutoPressCalibData_t));

    m_AutoPressCalibData.bCheckSum = CheckSum(&m_AutoPressCalibData, sizeof(m_AutoPressCalibData) - 1);
    WriteEEPROM(EEPROM_AUTO_FLOW_CONFIG_ADDR1, (U8 *)&m_AutoPressCalibData, sizeof(m_AutoPressCalibData));
    WriteEEPROM(EEPROM_AUTO_FLOW_CONFIG_ADDR2, (U8 *)&m_AutoPressCalibData, sizeof(m_AutoPressCalibData));

    SPIFLASH_WriteBuff((U8 *)&m_AutoPressCalibData, SPI_FLASH_AUTO_FLOW_CALIB_ADDR1, sizeof(m_AutoPressCalibData));
    SPIFLASH_WriteBuff((U8 *)&m_AutoPressCalibData, SPI_FLASH_AUTO_FLOW_CALIB_ADDR2, sizeof(m_AutoPressCalibData));
#endif
    return true;
}


#if (ENABLE_SD_CHANGE_PARAMETER == 1)
void CConfigSave::ImportParameterConfig()
{
//需要检测每一项数据的正确性
//    if (m_pSdCardDataSave->ImportParameterConfig((U8*)&m_OtherParameterConfig))
//    {
//        if (m_OtherParameterConfig.AppConfigFlag)
//        {
//            if (CheckSum((U8*)&m_OtherParameterConfig, sizeof(m_OtherParameterConfig) - 1) == m_OtherParameterConfig.CheckSum && (m_OtherParameterConfig.Head == PARAMETER_CONFIG_HEAD))
//            {
//                memcpy(&m_ParameterConfig, &m_OtherParameterConfig, sizeof(m_ParameterConfig));
//                SaveParameterData();
//            }
//        }
//    }
}
#endif

void CConfigSave::RestoreFactory()
{
    g_WifiUartModule.RestoreDefault();
    LoadDefaultParameterData(0);
    LoadDefaultRunTimeData();
    LoadDefaultWifiConfig(); // 重置WIFI配置
}

U8 CConfigSave::GetUseCycleForQRCode(void)
{
    return m_ParameterConfig.SysConfig.UseCycle;
}

U32 CConfigSave::GetParameter(ECONFIGTYPE Type)
{
    U32 Value;

    if(Type >= RUNTIME && Type <= TUBECHANGETIME)
    {
        switch(Type)
        {
            case RUNTIME:
                Value = m_RunTimeData.U32RunTime;
                break;
            case FILCHANGETIME:
                Value = m_RunTimeData.U32FilterChangeTime;
                break;
            case MASKCHANGETIME:
                Value = m_RunTimeData.U32MaskChangeTime;
                break;
            case TUBECHANGETIME:
                Value = m_RunTimeData.U32TubeChangeTime;
                break;
        }
        return Value;
    }
    else if(Type <= DEVICETYPE)
    {
        switch(Type)
        {
            case HARD_PROTECT_SWITCH:                                       //弃用，使用GetHardProtect()方法替代
                Value = m_MachineConfig.FunctionEnable.HardProtectSwitch;
                break;
            case MARKET_AREA_TYPE:
                Value = m_MachineConfig.FunctionEnable.MarketAreaType;
                break;
            case SUPPORTLANGUAGE:
                Value = m_MachineConfig.SupportLanguage;
                break;
            case DEVICETYPE:    //"机型"
                Value = m_MachineConfig.DType;
                break;
            case LEAKVIEWTYPE:  //漏气量显示:含面罩和不含面罩
                Value = m_MachineConfig.FunctionEnable.LeakageViewType;
                break;
            case SERVERTYPE:   //服务器: 国内服务器和国外服务器
                Value = m_MachineConfig.FunctionEnable.ServerType;
                break;
        }
        return Value;
    }
    else if(Type >= TIT0PRESS && Type <= TITFLOWTIME)
    {
        switch(Type)
        {
            case TIT0PRESS:
            case TIT4PRESS:
            case TIT10PRESS:
            case TIT20PRESS:
                Value = m_TitConfig.TitPress[Type - TIT0PRESS];
                break;
            case TIT0PWM:
            case TIT4PWM:
            case TIT10PWM:
            case TIT20PWM:
                Value = m_TitConfig.TitPressPWM[Type - TIT0PWM];
                break;
            case TIT0FLOW:
            case TIT30FLOW:
            case TIT60FLOW:
            case TIT80FLOW:
            case TIT120FLOW:
            case TIT140FLOW:
            {
                if ((GetSensorType() == ADP800) || (GetSensorType() == HDP800))
                {
                    Value = m_TitConfig.Adp801TitFlow[Type - TIT0FLOW];
                }
                else if(GetSensorType() == SDP810)
                {
                    Value = m_TitConfig.TitFlow[Type - TIT30FLOW];
                }
                break;
            }
            case TIT365CMH2OPWM:
                Value = m_TitConfig.Tit36_5cmH2OPWM;
                break;
            case TITSELFCHECKPWM:
                Value = m_TitConfig.TitSelfCheckPWM;
                break;
            case TITPRESSTIME:
                Value = m_TitConfig.TitPressTime;
                break;
            case TITFLOWTIME:
                Value = m_TitConfig.TitFlowTime;
                break;
        }
        return Value;
    }

    switch(Type)
    {
        case LANGUAGE:
            Value = m_ParameterConfig.SysConfig.Language;
            break;
        case BACKLIGHTTIME:
            Value = m_ParameterConfig.SysConfig.BackLightTime;
            break;
        case BACKLIGHTBRIGHTNESS:
            Value = m_ParameterConfig.SysConfig.BackLightBrigntNess;
            break;
        case PRESSUNIT:
            Value = m_ParameterConfig.SysConfig.PressUnit;
            break;
        case USECYCLE:
        {
            U16 sU16Days[] = {1, 7, 30, 90, 180, 365};
            if(m_ParameterConfig.SysConfig.UseCycle < 6)
            {
                Value = sU16Days[m_ParameterConfig.SysConfig.UseCycle];
            }
            else
            {
                Value = 7;
            }
        }
        break;
        case TUBETYPE:
            Value = m_ParameterConfig.SysConfig.TubeType;
            break;
        case MASKTYPE:
            Value = m_ParameterConfig.SysConfig.MaskType;
            break;
        case ECOTYPE:
            Value = m_ParameterConfig.SysConfig.ECOType;
            break;
        case WIFI_SWITCH:
            Value = m_WifiConfig.Switch;
            break;
        case WIFI_SSID:
            Value = (U32)m_WifiConfig.SSID;
            break;
        case WIFI_PWD:
            Value = (U32)m_WifiConfig.PWD;
            break;
        case BT_SWITCH:
            Value = (U32)m_ParameterConfig.BtConfig.Switch;
            break;
        case RAMP:
            Value = m_ParameterConfig.Ramp;
            break;
        case AUTOON:
            Value = m_ParameterConfig.AutoOn;
            break;
        case AUTOOFF:
            Value = m_ParameterConfig.AutoOff;
            break;
        case ESENS:
            Value = m_ParameterConfig.ESENS;
            break;
        case ISENS:
            Value = m_ParameterConfig.ISENS;
            break;
        case INSPTIME:
            Value = m_ParameterConfig.InspTime[m_ParameterConfig.WorkMode];
            break;
        case MAXINSPTIME:
            Value = m_ParameterConfig.MaxInspTime[m_ParameterConfig.WorkMode];
            break;
        case MININSPTIME:
            Value = m_ParameterConfig.MinInspTime[m_ParameterConfig.WorkMode];
            break;
        case ISLOP:
            Value = m_ParameterConfig.ISlop[m_ParameterConfig.WorkMode];
            break;
        case ESLOP:
            Value = m_ParameterConfig.ESlop[m_ParameterConfig.WorkMode];
            break;
        case BPM:
            Value = m_ParameterConfig.Bpm[m_ParameterConfig.WorkMode];
            break;
        case BELEX:
            Value = m_ParameterConfig.Belex[m_ParameterConfig.WorkMode];
            break;
        case WARMLEVEL:
            Value = m_ParameterConfig.WarmConfig.WarmLevel;
            break;
        case PREHEATSWITCH:      //预热开关
            Value = m_ParameterConfig.WarmConfig.PreHeatSwitch;
            break;
        case PREHEATTIME:        //预热时间为10-30分钟，步长为10分钟吗，默认设置30分钟，单位为分钟
            Value = m_ParameterConfig.WarmConfig.PreHeatTime;
            break;
        case TUBEWARMLEVEL:      //管路加热档位,1-5档，默认为3档
            Value = m_ParameterConfig.WarmConfig.TubeWarmLevel;
            break;
        case STARTPRESSURE:
            Value = m_ParameterConfig.StartPress[m_ParameterConfig.WorkMode];
            break;
        case EPAP:
            Value = m_ParameterConfig.Epap[m_ParameterConfig.WorkMode];
            break;
        case IPAP:
            Value = m_ParameterConfig.Ipap[m_ParameterConfig.WorkMode];
            break;
        case MINEPAP:
            Value = m_ParameterConfig.MinEpap[m_ParameterConfig.WorkMode];
            break;
        case MAXIPAP:
            Value = m_ParameterConfig.MaxIpap[m_ParameterConfig.WorkMode];
            break;
        case MAXPS:
            Value = m_ParameterConfig.MaxPS[m_ParameterConfig.WorkMode];
            break;
        case MINIPAP:
            Value = m_ParameterConfig.MinIpap[m_ParameterConfig.WorkMode];
            break;
        case MINPRESS:
            Value = m_ParameterConfig.MinPress[m_ParameterConfig.WorkMode];
            break;
        case MAXPRESS:
            Value = m_ParameterConfig.MaxPress[m_ParameterConfig.WorkMode];
            break;
        case WORKPRESS:
            Value = m_ParameterConfig.WorkPress[m_ParameterConfig.WorkMode];
            break;
        case VT:
            Value = m_ParameterConfig.VT[m_ParameterConfig.WorkMode];
            break;
        case VAFSWITCH:
            Value = m_ParameterConfig.VAFSwtich;
            break;
        case IRAMP:     //bit0-bit15：分别对应各种模式自动延时开关，为1时为开，为0时为关
            Value = m_ParameterConfig.iRamp;
            break;
        case SMARTPRESSURE: //智能压力，仅iAPAP模式使用
            Value = m_ParameterConfig.SmartPressure;
            break;
        case SEPARATENIGHT:
            Value = m_ParameterConfig.SeparateNight;
            break;
        case BOOTSTSENSITIVITY:
            Value = m_ParameterConfig.BoostSensitivity;
            break;
        case WORKMODE:
            Value = m_ParameterConfig.WorkMode;
            break;
        case TARGETHIGHFLOW:
            Value = m_ParameterConfig.TargetHighFlow;
            break;
        case HIGHPRESSUREALM:
            Value = m_ParameterConfig.SysConfig.HighPressureAlm;
            break;
        case LOWPRESSUREALM:
            Value = m_ParameterConfig.SysConfig.LowPressureAlm;
            break;
        case HIGHLEAKALM:
            Value = m_ParameterConfig.SysConfig.HighLeakAlm;
            break;
        case LOWMVALARM:
            Value = m_ParameterConfig.SysConfig.LowMVAlm;
            break;
        case APNEATIMEALM:
            Value = m_ParameterConfig.SysConfig.ApneaTimeAlm;
            break;
        case REPLACEFILTERALM:
            Value = m_ParameterConfig.SysConfig.ReplaceFilterAlm;
            break;
        case REPLACEMASKALM:
            Value = m_ParameterConfig.SysConfig.ReplaceMaskAlm;
            break;
        case REPLACETUBEALM:
            Value = m_ParameterConfig.SysConfig.ReplaceTubeAlm;
            break;
        case SDCARDPROMPT:  //SD卡未插提示
            Value = m_ParameterConfig.SysConfig.SDCardUnConnectionPrompt;
            break;
        case KNOBTONE:  //旋钮音 开关
            Value = m_ParameterConfig.SysConfig.KnobTone;
            break;
        default:
            assert("Read Parameter Type Error");
            break;
    }
    return Value;
}

void CConfigSave::CheckVAFMaxIPAPAndMinIPAP()
{
    if(m_ParameterConfig.Ipap[m_ParameterConfig.WorkMode] < m_ParameterConfig.MinIpap[m_ParameterConfig.WorkMode])
    {
        SetParameter(IPAP, m_ParameterConfig.MinIpap[m_ParameterConfig.WorkMode], 0);
        //m_ParameterConfig.Ipap[m_ParameterConfig.WorkMode] = m_ParameterConfig.MinIpap[m_ParameterConfig.WorkMode];
    }
    if(m_ParameterConfig.Ipap[m_ParameterConfig.WorkMode] > m_ParameterConfig.MaxIpap[m_ParameterConfig.WorkMode])
    {
        SetParameter(IPAP, m_ParameterConfig.MaxIpap[m_ParameterConfig.WorkMode], 0);
        //m_ParameterConfig.Ipap[m_ParameterConfig.WorkMode] = m_ParameterConfig.MaxIpap[m_ParameterConfig.WorkMode];
    }
//    m_ParameterConfig.MaxIpap[m_ParameterConfig.WorkMode] = MAX_IPAP;
//    m_ParameterConfig.MinIpap[m_ParameterConfig.WorkMode] = m_ParameterConfig.Ipap[m_ParameterConfig.WorkMode];
}

void CConfigSave::SetParameter(ECONFIGTYPE Type, U32 Value, U8 SaveFlag)
{
    if(Type >= RUNTIME && Type <= TUBECHANGETIME)
    {
        switch(Type)
        {
            case RUNTIME:
                m_RunTimeData.U32RunTime = Value;
                break;
            case FILCHANGETIME:
                m_RunTimeData.U32FilterChangeTime = Value;
                break;
            case MASKCHANGETIME:
                m_RunTimeData.U32MaskChangeTime = Value;
                break;
            case TUBECHANGETIME:
                m_RunTimeData.U32TubeChangeTime = Value;
                break;
        }
        if(SaveFlag)
        {
            SaveRunTimeData();
        }
        return;
    }
    else if(Type >= TIT0PRESS && Type <= TITFLOWCALIMODE)
    {
        switch(Type)
        {
            case TIT0PRESS:
            case TIT4PRESS:
            case TIT10PRESS:
            case TIT20PRESS:
                m_TitConfig.TitPress[Type - TIT0PRESS] = Value;
                break;
            case TIT0PWM:
            case TIT4PWM:
            case TIT10PWM:
            case TIT20PWM:
                m_TitConfig.TitPressPWM[Type - TIT0PWM] = Value;
                break;
            case TIT0FLOW:
            case TIT30FLOW:
            case TIT60FLOW:
            case TIT80FLOW:
            case TIT120FLOW:
            case TIT140FLOW:
            {
                if ((GetSensorType() == ADP800) || (GetSensorType() == HDP800))
                {
                    m_TitConfig.Adp801TitFlow[Type - TIT0FLOW] = Value;
                }
                else if(GetSensorType() == SDP810)
                {
                    m_TitConfig.TitFlow[Type - TIT30FLOW] = Value;
                }
                break;
            }
            case TIT365CMH2OPWM:
                m_TitConfig.Tit36_5cmH2OPWM = Value;
                break;
            case TITSELFCHECKPWM:
                m_TitConfig.TitSelfCheckPWM = Value;
                break;
            case TITPRESSTIME:
                m_TitConfig.TitPressTime = Value;
                break;
            case TITFLOWTIME:
                m_TitConfig.TitFlowTime = Value;
                break;
            case TITFLOWCALIMODE:
                m_TitConfig.Adp801TitFlowType = Value;
                break;
        }
        if(SaveFlag)
        {
            SaveCaliData();
        }
        return;
    }
    else if(Type <= SUPPORTLANGUAGE)
    {
        switch(Type)
        {
            case HARD_PROTECT_SWITCH:
                m_MachineConfig.FunctionEnable.HardProtectSwitch = (Value > 0 ? 1 : 0);
                break;
            case MARKET_AREA_TYPE:
                m_MachineConfig.FunctionEnable.MarketAreaType = (Value <= 3 ? Value : 0);
                LoadDefaultParameterData(0, 1);   //更改CFDA、CE、FDA机型，强制恢复默认配置数据
                SaveParameterData();
                break;
            case SUPPORTLANGUAGE:
                m_MachineConfig.SupportLanguage = Value;
                break;
            case LEAKVIEWTYPE:  //漏气量显示:含面罩和不含面罩
                m_MachineConfig.FunctionEnable.LeakageViewType = Value;
                break;
            case SERVERTYPE:   //服务器: 国内服务器和国外服务器
                m_MachineConfig.FunctionEnable.ServerType = Value;
                break;
        }
        if(SaveFlag)
        {
            SaveMachineData();
        }
        return;
    }
    switch(Type)
    {
        case LANGUAGE:
            m_ParameterConfig.SysConfig.Language = Value;
            break;
        case BACKLIGHTTIME:
            m_ParameterConfig.SysConfig.BackLightTime = Value;
            break;
        case BACKLIGHTBRIGHTNESS:
            m_ParameterConfig.SysConfig.BackLightBrigntNess = Value;
            if(Value)
            {
                SetLcdBrightness(((U8)Value) * 3);
            }
            break;
        case PRESSUNIT:
            m_ParameterConfig.SysConfig.PressUnit = Value;
            break;
        case USECYCLE:
        {
            U16 sU16Days[] = {1, 7, 30, 90, 180, 365};
            for(U8 i = 0; i < 6; i++)
            {
                if(Value == sU16Days[i])
                {
                    m_ParameterConfig.SysConfig.UseCycle = i;
                    break;
                }
            }
            //m_ParameterConfig.SysConfig.UseCycle = Value;
        }
        break;
        case TUBETYPE:
            if(m_ParameterConfig.SysConfig.TubeType != Value)
            {
                m_ParameterConfig.SysConfig.TubeType = Value;
                PostHumOrPipeEvent(TUBETYPE_FLAG);
            }
            else
            {
                return;
            }
            break;
        case MASKTYPE:
            m_ParameterConfig.SysConfig.MaskType = Value;
            break;
        case ECOTYPE:
            m_ParameterConfig.SysConfig.ECOType = Value;
            PostHumOrPipeEvent(ECO_FLAG);
            break;
        case WIFI_SWITCH:
            m_WifiConfig.Switch = Value;
            if(Value == 0)
            {
                UpdateIconStatus(WIFI_COMM_STATE_INDICATION_BIT, 0);
                g_WifiUartModule.UpdateAPConnectStatus(0);
                HAL_GPIO_WritePin(WIFI_CONTROL_GPIO_Port, WIFI_CONTROL_Pin, GPIO_PIN_SET);
            }
            else
            {
                HAL_GPIO_WritePin(WIFI_CONTROL_GPIO_Port, WIFI_CONTROL_Pin, GPIO_PIN_RESET);
                g_WifiUartModule.ConfigBaseInfo();
            }
            if(SaveFlag)
                SaveWifiConfig();
            break;
        case WIFI_SSID:
            strcpy(m_WifiConfig.SSID, (char *)Value);
            if(SaveFlag)
                SaveWifiConfig();
            break;
        case WIFI_PWD:
            strcpy(m_WifiConfig.PWD, (char *)Value);
            if(SaveFlag)
                SaveWifiConfig();
            break;
        case BT_SWITCH:
            m_ParameterConfig.BtConfig.Switch = Value;
            break;
        case RAMP:
            m_ParameterConfig.Ramp = Value;
            PostMotorEvent(RAMP);
            break;
        case AUTOON:
            m_ParameterConfig.AutoOn = Value;
//            PostMotorEvent(AUTOON);
            break;
        case AUTOOFF:
            m_ParameterConfig.AutoOff = Value;
//            PostMotorEvent(AUTOOFF);
            break;
        case ESENS:
            m_ParameterConfig.ESENS = Value;
//            PostMotorEvent(ESENS);
            break;
        case ISENS:
            m_ParameterConfig.ISENS = Value;
//            PostMotorEvent(ISENS);
            break;
        case INSPTIME:
            m_ParameterConfig.InspTime[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(INSPTIME);
            break;
        case MAXINSPTIME:
            m_ParameterConfig.MaxInspTime[m_ParameterConfig.WorkMode] = Value;
//            PostMotorEvent(MAXINSPTIME);
            break;
        case MININSPTIME:
            m_ParameterConfig.MinInspTime[m_ParameterConfig.WorkMode] = Value;
//            PostMotorEvent(MININSPTIME);
            break;
        case ISLOP:
            m_ParameterConfig.ISlop[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(ISLOP);
            break;
        case ESLOP:
        {
            m_ParameterConfig.ESlop[m_ParameterConfig.WorkMode] = Value;
            if(m_ParameterConfig.WorkMode >= SYS_WM_S && m_ParameterConfig.WorkMode <= SYS_WM_VAF_APCV)
            {
                PostMotorEvent(ESLOP);
            }
            break;
        }
        case BPM:
            m_ParameterConfig.Bpm[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(BPM);
            break;
        case BELEX:
            m_ParameterConfig.Belex[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(BELEX);
            break;
        case WARMLEVEL:
            m_ParameterConfig.WarmConfig.WarmLevel = Value;
            PostHumOrPipeEvent(WARMLEVEL_FLAG);
            break;
        case PREHEATSWITCH:      //预热开关
            if(m_ParameterConfig.WarmConfig.PreHeatSwitch != Value)
            {
                m_ParameterConfig.WarmConfig.PreHeatSwitch = Value;
                PostHumOrPipeEvent(PREHEATSWITCH_FLAG);
            }
            else
            {
                if(SaveFlag == 0)
                {
                    return;
                }
            }
            break;
        case PREHEATTIME:        //预热时间为10-30分钟，步长为10分钟吗，默认设置30分钟，单位为分钟
            m_ParameterConfig.WarmConfig.PreHeatTime = Value;
            PostHumOrPipeEvent(PREHEATTIME_FLAG);
            break;
        case TUBEWARMLEVEL:      //管路加热档位,1-5档，默认为3档
            m_ParameterConfig.WarmConfig.TubeWarmLevel = Value;
            PostHumOrPipeEvent(TUBEWARMLEVEL_FLAG);
            break;
        case STARTPRESSURE:
            m_ParameterConfig.StartPress[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(STARTPRESSURE);
            break;
        case SEPARATENIGHT:
            m_ParameterConfig.SeparateNight = Value;
            PostMotorEvent(SEPARATENIGHT);
            break;
        case EPAP:
            m_ParameterConfig.Epap[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(EPAP);
            break;
        case IPAP:
            m_ParameterConfig.Ipap[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(IPAP);
            break;
        case MINEPAP:
            m_ParameterConfig.MinEpap[m_ParameterConfig.WorkMode] = Value;
//            PostMotorEvent(MINEPAP);
            break;
        case MAXIPAP:
            m_ParameterConfig.MaxIpap[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(MAXIPAP);
            break;
        case MAXPS:
            m_ParameterConfig.MaxPS[m_ParameterConfig.WorkMode] = Value;
//            PostMotorEvent(MAXPS);
            break;
        case MINIPAP:
            m_ParameterConfig.MinIpap[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(MINIPAP);
            break;
        case MINPRESS:
            m_ParameterConfig.MinPress[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(MINPRESS);
            break;
        case MAXPRESS:
            m_ParameterConfig.MaxPress[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(MAXPRESS);
            break;
        case WORKPRESS:
            m_ParameterConfig.WorkPress[m_ParameterConfig.WorkMode] = Value;
            PostMotorEvent(WORKPRESS);
            break;
        case VT:
            m_ParameterConfig.VT[m_ParameterConfig.WorkMode] = Value;
//            PostMotorEvent(VT);
            break;
        case VAFSWITCH:
            m_ParameterConfig.VAFSwtich = Value;
            if(m_ParameterConfig.VAFSwtich)
            {
                CheckVAFMaxIPAPAndMinIPAP();
            }
            break;
        case IRAMP:     //bit0-bit15：分别对应各种模式自动延时开关，为1时为开，为0时为关
            m_ParameterConfig.iRamp = Value;
            break;
        case SMARTPRESSURE: //智能压力，仅iAPAP模式使用
            m_ParameterConfig.SmartPressure = Value;
            PostMotorEvent(SMARTPRESSURE);
            break;
        case BOOTSTSENSITIVITY:
            m_ParameterConfig.BoostSensitivity = Value;
            //PostMotorEvent(BOOTSTSENSITIVITY);
            break;
        case WORKMODE:
            m_ParameterConfig.WorkMode = Value;
            PostMotorEvent(WORKMODE);
            break;
        case TARGETHIGHFLOW:
            m_ParameterConfig.TargetHighFlow = Value;
            PostMotorEvent(TARGETHIGHFLOW);
            break;
        case HIGHPRESSUREALM:
            m_ParameterConfig.SysConfig.HighPressureAlm = Value;
            if(Value == 0)
            {
                UpdateAlarmMarket(HIPRESS, RESET);
            }
            break;
        case LOWPRESSUREALM:
            m_ParameterConfig.SysConfig.LowPressureAlm = Value;
            if(Value == 0)
            {
                UpdateAlarmMarket(LOWPRESS, RESET);
            }
            break;
        case HIGHLEAKALM:
            m_ParameterConfig.SysConfig.HighLeakAlm = Value;
            if(Value == 0)
            {
                UpdateAlarmMarket(HIGHLEAK, RESET);
            }
            break;
        case LOWMVALARM:
            m_ParameterConfig.SysConfig.LowMVAlm = Value;
            if(Value == 0)
            {
                UpdateAlarmMarket(LOWMV, RESET);
            }
            break;
        case APNEATIMEALM:
            m_ParameterConfig.SysConfig.ApneaTimeAlm = Value;
            if(Value == 0)
            {
                UpdateAlarmMarket(APNEA, RESET);
            }
            break;
        case REPLACEFILTERALM:
            m_ParameterConfig.SysConfig.ReplaceFilterAlm = Value;
            if(Value == 0)
            {
                UpdateTipMarket(REPLACEFILTER, RESET);
            }
            else
            {
                m_RunTimeData.U32FilterChangeTime = GetCurrentDateTime() / (60 * 60 * 24);
                SaveRunTimeData();
            }
            break;
        case REPLACEMASKALM:
            m_ParameterConfig.SysConfig.ReplaceMaskAlm = Value;
            if(Value == 0)
            {
                UpdateTipMarket(REPLACEMASK, RESET);
            }
            else
            {
                m_RunTimeData.U32MaskChangeTime = GetCurrentDateTime() / (60 * 60 * 24);
                SaveRunTimeData();
            }
            break;
        case REPLACETUBEALM:
            m_ParameterConfig.SysConfig.ReplaceTubeAlm = Value;
            if(Value == 0)
            {
                UpdateTipMarket(REPLACETUBE, RESET);
            }
            else
            {
                m_RunTimeData.U32TubeChangeTime = GetCurrentDateTime() / (60 * 60 * 24);
                SaveRunTimeData();
            }
            break;
        case SDCARDPROMPT:  //SD卡未插提示
            m_ParameterConfig.SysConfig.SDCardUnConnectionPrompt = Value;
            if(Value == 0)
            {
                UpdateTipMarket(SDUNCONNECTED, RESET);
            }
            break;
        case KNOBTONE:  //旋钮音 开关
            m_ParameterConfig.SysConfig.KnobTone = Value;
            break;
        default:
            assert("Write Parameter Type Error");
            break;
    }
    if(Type >= LANGUAGE && Type <= MOTOR_ABNORMAL_STOP)
    {
        if(GetBldcmStatus() == MOTOR_NORMAL_RUN_STATE || GetBldcmStatus() == MOTOR_START_STATE)
        {
            m_pSdCardDataSave->AddEvent(PARA_CHANGED_LOGEVENT, Type, Value, GetSysRunTime() / 100, 0);
        }
        if(SaveFlag)
        {
            SaveParameterData();
        }
    }
}

U8 CConfigSave::GetHardProtect(void)
{
    return m_MachineConfig.FunctionEnable.HardProtectSwitch;
}

U8 CConfigSave::GetMarketAreaType(void)
{
    return m_MachineConfig.FunctionEnable.MarketAreaType;
}

U8 CConfigSave::GetServerType(void)
{
    return m_MachineConfig.FunctionEnable.ServerType;
}

const PARAMETERCONFIG *CConfigSave::GetParameterConfig()
{
    return &m_ParameterConfig;
}

const RUNTIMEDATA *CConfigSave::GetRunTimeData()
{
    return &m_RunTimeData;
}

const TITCONFIG *CConfigSave::GetTitConfig()
{
    return &m_TitConfig;
}

const MACHINECONFIG *CConfigSave::GetMachineConfig()
{
    return &m_MachineConfig;
}

void CConfigSave::I2CWriteEEPROM(U8 DeviceAddr, U16 WriteAddr, U8 *pData, U16 size)
{
    if(HAL_I2C_Mem_Write(&hi2c2, DeviceAddr, WriteAddr, I2C_MEMADD_SIZE_16BIT, pData, size, 1000) != HAL_OK)
    {
    }
    while(HAL_I2C_GetState(&hi2c2) != HAL_I2C_STATE_READY);                                     //增加超时处理
    HAL_Delay(6);
}

bool CConfigSave::WriteEEPROM(U16 WriteAddr, U8 *pData, U16 size)
{
    Lock();
    while(size > 0)
    {
        if(size <= EEPROM_PAGE_SIZE)
        {
            I2CWriteEEPROM(0xa0, WriteAddr, pData, size);
            break;
        }
        I2CWriteEEPROM(0xa0, WriteAddr, pData, EEPROM_PAGE_SIZE);
        size -= EEPROM_PAGE_SIZE;
        pData += EEPROM_PAGE_SIZE;
        WriteAddr += EEPROM_PAGE_SIZE;
    }
    UnLock();
    return true;
}

bool CConfigSave::ReadEEPROM(U16 ReadAddr, U8 *pData, U16 Size)
{
    HAL_StatusTypeDef sta;
    U8 i, Cnt = 0;
    Lock();
    if((Size & 0x7f) == 0)
    {
        Cnt = Size / 128;
    }
    else
    {
        Cnt = Size / 128 + 1;
    }
    for(i = 0; i < Cnt; i++)
    {
        sta = HAL_I2C_Mem_Read(&hi2c2, 0xa1, ReadAddr, I2C_MEMADD_SIZE_16BIT, pData,
                                        Size > EEPROM_PAGE_SIZE ? EEPROM_PAGE_SIZE : Size, 1000);
        if(sta != HAL_OK)
        {
            UnLock();
            SetEEPROMErrorFlag(1);
            return false;
        }
        pData += EEPROM_PAGE_SIZE;
        ReadAddr += EEPROM_PAGE_SIZE;
        Size -= EEPROM_PAGE_SIZE;
        HAL_Delay(6);
    }
    UnLock();
    SetEEPROMErrorFlag(0);
    return true;
}

void CConfigSave::SetApNamePwd(const char *ApName, const char *Pwd)
{
    strcpy(m_WifiConfig.SSID, ApName);
    strcpy(m_WifiConfig.PWD, Pwd);
    SaveWifiConfig();
}

void CConfigSave::UpdateNetSystemConfig(PNETPOWERONINFO pConfig)
{
    pConfig->Config.SysConfig.Language = m_ParameterConfig.SysConfig.Language;
    pConfig->Config.SysConfig.BackLightTime = m_ParameterConfig.SysConfig.BackLightTime;
    pConfig->Config.SysConfig.BackLightBrigntNess = m_ParameterConfig.SysConfig.BackLightBrigntNess;
    pConfig->Config.SysConfig.PressUnit = m_ParameterConfig.SysConfig.PressUnit;
    pConfig->Config.SysConfig.UseCycle = m_ParameterConfig.SysConfig.UseCycle;
    pConfig->Config.SysConfig.TubeType = m_ParameterConfig.SysConfig.TubeType;
    pConfig->Config.SysConfig.MaskType = m_ParameterConfig.SysConfig.MaskType;
    pConfig->Config.SysConfig.ECOType = m_ParameterConfig.SysConfig.ECOType;
    pConfig->Config.SysConfig.HighPressureAlm = m_ParameterConfig.SysConfig.HighPressureAlm;
    pConfig->Config.SysConfig.LowPressureAlm = m_ParameterConfig.SysConfig.LowPressureAlm;
    pConfig->Config.SysConfig.HighLeakAlm = m_ParameterConfig.SysConfig.HighLeakAlm;
    pConfig->Config.SysConfig.LowMVAlm = m_ParameterConfig.SysConfig.LowMVAlm;
    pConfig->Config.SysConfig.ApneaTimeAlm = m_ParameterConfig.SysConfig.ApneaTimeAlm;
    pConfig->Config.SysConfig.ReplaceFilterAlm = m_ParameterConfig.SysConfig.ReplaceFilterAlm;
    pConfig->Config.SysConfig.ReplaceMaskAlm = m_ParameterConfig.SysConfig.ReplaceMaskAlm;
    pConfig->Config.SysConfig.ReplaceTubeAlm = m_ParameterConfig.SysConfig.ReplaceTubeAlm;

    pConfig->Config.Ramp = m_ParameterConfig.Ramp;
    pConfig->Config.AutoOn = m_ParameterConfig.AutoOn;
    pConfig->Config.AutoOff = m_ParameterConfig.AutoOff;
    pConfig->Config.ESENS = m_ParameterConfig.ESENS;
    pConfig->Config.ISENS = m_ParameterConfig.ISENS;
    pConfig->Config.InspTime = m_ParameterConfig.InspTime[m_ParameterConfig.WorkMode];
    pConfig->Config.MaxInspTime = m_ParameterConfig.MaxInspTime[m_ParameterConfig.WorkMode];
    pConfig->Config.MinInspTime = m_ParameterConfig.MinInspTime[m_ParameterConfig.WorkMode];
    pConfig->Config.ISlop = m_ParameterConfig.ISlop[m_ParameterConfig.WorkMode];
    pConfig->Config.Bpm = m_ParameterConfig.Bpm[m_ParameterConfig.WorkMode];
    pConfig->Config.Belex = m_ParameterConfig.Belex[m_ParameterConfig.WorkMode];

    pConfig->Config.WarmConfig.WarmLevel = m_ParameterConfig.WarmConfig.WarmLevel;
    pConfig->Config.WarmConfig.PreHeatSwitch = m_ParameterConfig.WarmConfig.PreHeatSwitch;
    pConfig->Config.WarmConfig.PreHeatTime = m_ParameterConfig.WarmConfig.PreHeatTime;
    pConfig->Config.WarmConfig.TubeWarmLevel = m_ParameterConfig.WarmConfig.TubeWarmLevel;

    pConfig->Config.StartPress = m_ParameterConfig.StartPress[m_ParameterConfig.WorkMode];
    pConfig->Config.Epap = m_ParameterConfig.Epap[m_ParameterConfig.WorkMode];
    pConfig->Config.Ipap = m_ParameterConfig.Ipap[m_ParameterConfig.WorkMode];
    pConfig->Config.MinEpap = m_ParameterConfig.MinEpap[m_ParameterConfig.WorkMode];
    pConfig->Config.MaxIpap = m_ParameterConfig.MaxIpap[m_ParameterConfig.WorkMode];
    pConfig->Config.MaxPS = m_ParameterConfig.MaxPS[m_ParameterConfig.WorkMode];
    pConfig->Config.MinIpap = m_ParameterConfig.MinIpap[m_ParameterConfig.WorkMode];
    pConfig->Config.MinPress = m_ParameterConfig.MinPress[m_ParameterConfig.WorkMode];
    pConfig->Config.MaxPress = m_ParameterConfig.MaxPress[m_ParameterConfig.WorkMode];
    pConfig->Config.WorkPress = m_ParameterConfig.WorkPress[m_ParameterConfig.WorkMode];
    pConfig->Config.VT = m_ParameterConfig.VT[m_ParameterConfig.WorkMode];

    pConfig->Config.iRamp = m_ParameterConfig.iRamp;
    pConfig->Config.SmartPressure = m_ParameterConfig.SmartPressure;
    pConfig->Config.SeparateNight = m_ParameterConfig.SeparateNight;
    pConfig->Config.BoostSensitivity = m_ParameterConfig.BoostSensitivity;
    pConfig->Config.WorkMode = m_ParameterConfig.WorkMode;
    pConfig->Config.TargetHighFlow = m_ParameterConfig.TargetHighFlow;
}

#if (ENABLE_NETWORK_CHANGE_PARAMETER == 1)
U8 CConfigSave::CheckNetParameter()
{
    if(m_OtherParameterConfig.NetConfigFlag)
    {
        ChangeParameterFromOtherConfig(&m_ParameterConfig, &m_OtherParameterConfig);
        m_OtherParameterConfig.NetConfigFlag = 0;
        SaveParameterData();
        SaveNetParameterData();
        return 1;
    }
    return 0;
}

void CConfigSave::ChangeParameterFromOtherConfig(PPARAMETERCONFIG pDestConfig, PPARAMETERCONFIG pSourceConfig)
{
    int i = 0;

    if(pSourceConfig->WorkMode != SYS_WM_HIGHFLOW)
    {
        if(pSourceConfig->AutoOn >= 0 && pSourceConfig->AutoOn <= 1) //"自动开机"
        {
            pDestConfig->AutoOn = pSourceConfig->AutoOn;
        }

        if(pSourceConfig->AutoOff >= 0 && pSourceConfig->AutoOff <= 1) //"自动关机"
        {
            pDestConfig->AutoOff = pSourceConfig->AutoOff;
        }
    }

    switch(GetDeviceType()) //工作模式
    {
        case RF_20C_S1_X1:
        {
            if(pSourceConfig->WorkMode == SYS_WM_CPAP)
            {
                pDestConfig->WorkMode = pSourceConfig->WorkMode;
            }
            break;
        }
        case RF_20A_S2_X2:
        {
            if(m_MachineConfig.ProNum > 1)
            {
                if(pSourceConfig->WorkMode == SYS_WM_CPAP || pSourceConfig->WorkMode == SYS_WM_S)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                }
            }
            else
            {
                if(pSourceConfig->WorkMode == SYS_WM_CPAP || pSourceConfig->WorkMode == SYS_WM_APAP)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                }
            }
            break;
        }
        case RF_20I_S3_X3:
        {
            if(pSourceConfig->WorkMode >= SYS_WM_CPAP && pSourceConfig->WorkMode <= SYS_WM_IAPAP)
            {
                pDestConfig->WorkMode = pSourceConfig->WorkMode;
            }
            break;
        }
        case RF_25A_S5_X5:
        {
            for(i = 0; i <= SYS_PARA_WORKMODE_RF_25A_MAX; i++)
            {
                if(GetMarketAreaType() && m_MachineConfig.ProNum < 2)
                {
                    if(WorkMode_RF_25A_CE[i] == pSourceConfig->WorkMode)
                    {
                        pDestConfig->WorkMode = pSourceConfig->WorkMode;
                    }
                }
                else
                {
                    if(WorkMode_RF_25A[i] == pSourceConfig->WorkMode)
                    {
                        pDestConfig->WorkMode = pSourceConfig->WorkMode;
                    }
                }
            }
            break;
        }
        case RF_25S_S6_X6:
        {
            if(GetMarketAreaType() && m_MachineConfig.ProNum < 2)
            {
                if(pSourceConfig->WorkMode == SYS_WM_CPAP || pSourceConfig->WorkMode == SYS_WM_S)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                }
            }
            else
            {
                if(pSourceConfig->WorkMode == SYS_WM_S || pSourceConfig->WorkMode == SYS_WM_AUTOB)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                }
            }
            break;
        }
        case RF_30A_S7_X7:
        {
            for(i = 0; i <= SYS_PARA_WORKMODE_RF_30A_MAX; i++)
            {
                if(WorkMode_RF_30A[i] == pSourceConfig->WorkMode)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                }
            }
            break;
        }
        case RF_25T_H1_P1:
        {
            for(i = 0; i <= SYS_PARA_WORKMODE_RF_25T_MAX; i++)
            {
                if(WorkMode_RF_25T[i] == pSourceConfig->WorkMode)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                }
            }
            break;
        }
        case RF_25P_H2_P2:
        {
            for(i = 0; i <= SYS_PARA_WORKMODE_RF_25P_MAX; i++)
            {
                if(WorkMode_RF_25P[i] == pSourceConfig->WorkMode)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                }
            }
            break;
        }
        case RF_25V_H3_P3:
        {
            for(i = 0; i <= SYS_PARA_WORKMODE_RF_25V_MAX; i++)
            {
                if(WorkMode_RF_25V[i] == pSourceConfig->WorkMode)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                }
            }
            break;
        }
        case RF_30T_H5_P5:
        {
            for(i = 0; i <= SYS_PARA_WORKMODE_RF_30T_MAX; i++)
            {
                if(WorkMode_RF_30T[i] == pSourceConfig->WorkMode)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                }
            }
            break;
        }
        case RF_30P_H6_P6:
        {
            for(i = 0; i <= SYS_PARA_WORKMODE_RF_30P_MAX; i++)
            {
                if(WorkMode_RF_30P[i] == pSourceConfig->WorkMode)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;
                    if(pSourceConfig->WorkMode == SYS_WM_HIGHFLOW)
                    {
                        if(GetPipeConnectFlag() == FALSE)
                        {
                            UpdateTipMarket(HFMODEPROMPT, SET);
                        }
                    }
                }
            }
            break;
        }
        case RF_30S_H7_P7:
        {
            if(pSourceConfig->WorkMode >= SYS_WM_CPAP && pSourceConfig->WorkMode <= SYS_WM_APCV)
            {
                pDestConfig->WorkMode = pSourceConfig->WorkMode;
            }
            break;
        }
        case RF_30V_H8_P8:
        {
            for(i = 0; i <= SYS_PARA_WORKMODE_RF_30V_MAX; i++)
            {
                if(WorkMode_RF_30V[i] == pSourceConfig->WorkMode)
                {
                    pDestConfig->WorkMode = pSourceConfig->WorkMode;

                    if(pSourceConfig->WorkMode == SYS_WM_HIGHFLOW)
                    {
                        if(GetPipeConnectFlag() == FALSE)
                        {
                            UpdateTipMarket(HFMODEPROMPT, SET);
                        }
                    }

                    break;
                }
            }
            break;
        }
        case RF_30F_H9_P9:
        {
            //当连接加温管路时，才可以设置HF模式
            if(pSourceConfig->WorkMode >= SYS_WM_CPAP && pSourceConfig->WorkMode <= SYS_WM_HIGHFLOW)
            {
                pDestConfig->WorkMode = pSourceConfig->WorkMode;
                if(pSourceConfig->WorkMode == SYS_WM_HIGHFLOW)
                {
                    if(GetPipeConnectFlag() == FALSE)
                    {
                        UpdateTipMarket(HFMODEPROMPT, SET);
                    }
                }
            }
            break;
        }
    }

    if(pDestConfig->WorkMode == SYS_WM_S || pDestConfig->WorkMode == SYS_WM_T || pDestConfig->WorkMode == SYS_WM_ST
                    || pDestConfig->WorkMode == SYS_WM_AUTOB
                    || pDestConfig->WorkMode == SYS_WM_APCV || pDestConfig->WorkMode == SYS_WM_VAF_ST
                    || pDestConfig->WorkMode == SYS_WM_VAF_APCV)
    {
        if(pSourceConfig->ESENS >= 0 && pSourceConfig->ESENS <= SYS_PARA_ESENS_MAX) //"呼气灵敏度"
        {
            pDestConfig->ESENS = pSourceConfig->ESENS;
        }
        if(pSourceConfig->ISENS >= 0 && pSourceConfig->ISENS <= SYS_PARA_ISENS_MAX) //"吸气灵敏度"
        {
            pDestConfig->ISENS = pSourceConfig->ISENS;
        }
        if(pSourceConfig->Ipap[pDestConfig->WorkMode] >= SYS_PARA_EPAP_MIN
                        && pSourceConfig->Ipap[pDestConfig->WorkMode] <= MAX_IPAP)
        {
            pDestConfig->Ipap[pDestConfig->WorkMode] = pSourceConfig->Ipap[pDestConfig->WorkMode];
        }

        if((pSourceConfig->Epap[pDestConfig->WorkMode] > SYS_PARA_EPAP_MIN
                                        && pSourceConfig->Epap[pDestConfig->WorkMode] <= pDestConfig->Ipap[pDestConfig->WorkMode] - 20)
                        || (pSourceConfig->Epap[pDestConfig->WorkMode] == SYS_PARA_EPAP_MIN
                                        && pSourceConfig->Epap[pDestConfig->WorkMode] <= pDestConfig->Ipap[pDestConfig->WorkMode]))
        {
            pDestConfig->Epap[pDestConfig->WorkMode] = pSourceConfig->Epap[pDestConfig->WorkMode];
        }
        else
        {
            pDestConfig->Epap[pDestConfig->WorkMode] = SYS_PARA_EPAP_MIN;
        }
    }

    switch(pDestConfig->WorkMode)
    {
        case SYS_WM_CPAP:
            if(pSourceConfig->WorkPress[pDestConfig->WorkMode] >= SYS_PARA_STARTPRESS_MIN
                            && pSourceConfig->WorkPress[pDestConfig->WorkMode] <= SYS_PARA_WORKPRESS_MAX)
            {
                pDestConfig->WorkPress[pDestConfig->WorkMode] = pSourceConfig->WorkPress[pDestConfig->WorkMode];
            }
            if(pSourceConfig->StartPress[pDestConfig->WorkMode] >= SYS_PARA_STARTPRESS_MIN
                            && pSourceConfig->StartPress[pDestConfig->WorkMode] <= pDestConfig->WorkPress[pDestConfig->WorkMode])
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = pSourceConfig->StartPress[pDestConfig->WorkMode];
            }
            if(pSourceConfig->Ramp >= SYS_PARA_RAMP_MIN && pSourceConfig->Ramp <= SYS_PARA_RAMP_MAX)
            {
                pDestConfig->Ramp = pSourceConfig->Ramp;
            }
            if(pSourceConfig->iRamp >= 0 && pSourceConfig->iRamp <= 1)
            {
                pDestConfig->iRamp = pSourceConfig->iRamp;
            }
            if(pSourceConfig->Belex[pDestConfig->WorkMode] >= SYS_PARA_BELEX_MIN
                            && pSourceConfig->Belex[pDestConfig->WorkMode] <= SYS_PARA_BELEX_MAX)
            {
                pDestConfig->Belex[pDestConfig->WorkMode] = pSourceConfig->Belex[pDestConfig->WorkMode];
            }
            break;
        case SYS_WM_APAP:
            if(pSourceConfig->MaxPress[pDestConfig->WorkMode] >= SYS_PARA_MAXPRESS_MIN
                            && pSourceConfig->MaxPress[pDestConfig->WorkMode] <= SYS_PARA_MAXPRESS_MAX)
            {
                pDestConfig->MaxPress[pDestConfig->WorkMode] = pSourceConfig->MaxPress[pDestConfig->WorkMode];
            }
            if(pSourceConfig->MinPress[pDestConfig->WorkMode] >= SYS_PARA_MAXPRESS_MIN
                            && pSourceConfig->MinPress[pDestConfig->WorkMode] <= SYS_PARA_MAXPRESS_MAX
                            && pSourceConfig->MinPress[pDestConfig->WorkMode] <= pDestConfig->MaxPress[pDestConfig->WorkMode])
            {
                pDestConfig->MinPress[pDestConfig->WorkMode] = pSourceConfig->MinPress[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MinPress[pDestConfig->WorkMode] = SYS_PARA_MAXPRESS_MIN;
            }

            if(pSourceConfig->StartPress[pDestConfig->WorkMode] >= SYS_PARA_STARTPRESS_MIN
                            && pSourceConfig->StartPress[pDestConfig->WorkMode] <= pDestConfig->MinPress[pDestConfig->WorkMode])
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = pSourceConfig->StartPress[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = SYS_PARA_STARTPRESS_MIN;
            }
            if(pSourceConfig->Ramp >= SYS_PARA_RAMP_MIN && pSourceConfig->Ramp <= SYS_PARA_RAMP_MAX)
            {
                pDestConfig->Ramp = pSourceConfig->Ramp;
            }
            if(pSourceConfig->iRamp >= 0 && pSourceConfig->iRamp <= 1)
            {
                pDestConfig->iRamp = pSourceConfig->iRamp;
            }
            if(pSourceConfig->Belex[pDestConfig->WorkMode] >= SYS_PARA_BELEX_MIN
                            && pSourceConfig->Belex[pDestConfig->WorkMode] <= SYS_PARA_BELEX_MAX)
            {
                pDestConfig->Belex[pDestConfig->WorkMode] = pSourceConfig->Belex[pDestConfig->WorkMode];
            }
            if(pSourceConfig->BoostSensitivity >= SYS_PARA_SLOP_SENS_MIN
                            && pSourceConfig->BoostSensitivity <= SYS_PARA_SLOP_SENS_MAX)
            {
                pDestConfig->BoostSensitivity = pSourceConfig->BoostSensitivity;
            }
            break;
        case SYS_WM_IAPAP:
            if(pSourceConfig->MaxPress[pDestConfig->WorkMode] >= SYS_PARA_MAXPRESS_MIN
                            && pSourceConfig->MaxPress[pDestConfig->WorkMode] <= SYS_PARA_MAXPRESS_MAX)
            {
                pDestConfig->MaxPress[pDestConfig->WorkMode] = pSourceConfig->MaxPress[pDestConfig->WorkMode];
            }
            if(pSourceConfig->MinPress[pDestConfig->WorkMode] >= SYS_PARA_MAXPRESS_MIN
                            && pSourceConfig->MinPress[pDestConfig->WorkMode] <= SYS_PARA_MAXPRESS_MAX
                            && pSourceConfig->MinPress[pDestConfig->WorkMode] <= pDestConfig->MaxPress[pDestConfig->WorkMode])
            {
                pDestConfig->MinPress[pDestConfig->WorkMode] = pSourceConfig->MinPress[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MinPress[pDestConfig->WorkMode] = SYS_PARA_MAXPRESS_MIN;
            }
            if(pSourceConfig->Belex[pDestConfig->WorkMode] >= SYS_PARA_BELEX_MIN
                            && pSourceConfig->Belex[pDestConfig->WorkMode] <= SYS_PARA_BELEX_MAX)
            {
                pDestConfig->Belex[pDestConfig->WorkMode] = pSourceConfig->Belex[pDestConfig->WorkMode];
            }
            if(pSourceConfig->BoostSensitivity >= SYS_PARA_SLOP_SENS_MIN
                            && pSourceConfig->BoostSensitivity <= SYS_PARA_SLOP_SENS_MAX)
            {
                pDestConfig->BoostSensitivity = pSourceConfig->BoostSensitivity;
            }
            break;
        case SYS_WM_S:
            if(pSourceConfig->StartPress[pDestConfig->WorkMode] <= pDestConfig->Epap[pDestConfig->WorkMode])
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = pSourceConfig->StartPress[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = SYS_PARA_EPAP_MIN;
            }
            if(pSourceConfig->MaxInspTime[pDestConfig->WorkMode] >= SYS_PARA_MAXINSPTIME_MIN
                            && pSourceConfig->MaxInspTime[pDestConfig->WorkMode] <= SYS_PARA_MAXINSPTIME_MAX)
            {
                pDestConfig->MaxInspTime[pDestConfig->WorkMode] = pSourceConfig->MaxInspTime[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MaxInspTime[pDestConfig->WorkMode] = SYS_PARA_MAXINSPTIME_MIN;
            }
            if(pSourceConfig->MinInspTime[pDestConfig->WorkMode] >= SYS_PARA_MAXINSPTIME_MIN
                            && pSourceConfig->MinInspTime[pDestConfig->WorkMode] <= SYS_PARA_MAXINSPTIME_MAX
                            && pSourceConfig->MinInspTime[pDestConfig->WorkMode] <= pDestConfig->MaxInspTime[pDestConfig->WorkMode])
            {
                pDestConfig->MinInspTime[pDestConfig->WorkMode] = pSourceConfig->MinInspTime[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MinInspTime[pDestConfig->WorkMode] = SYS_PARA_MAXINSPTIME_MIN;
            }
            if(pSourceConfig->Ramp >= SYS_PARA_RAMP_MIN && pSourceConfig->Ramp <= SYS_PARA_RAMP_MAX)
            {
                pDestConfig->Ramp = pSourceConfig->Ramp;
            }
            if(pSourceConfig->iRamp >= 0 && pSourceConfig->iRamp <= 1)
            {
                pDestConfig->iRamp = pSourceConfig->iRamp;
            }
            if(pSourceConfig->ISlop[pDestConfig->WorkMode] >= SYS_PARA_ISLOP_MIN
                            && pSourceConfig->ISlop[pDestConfig->WorkMode] <= SYS_PARA_ISLOP_MAX)
            {
                pDestConfig->ISlop[pDestConfig->WorkMode] = pSourceConfig->ISlop[pDestConfig->WorkMode];
            }
            break;
        case SYS_WM_T:
            if(pSourceConfig->Bpm[pDestConfig->WorkMode] >= SYS_PARA_BPM_MIN
                            && pSourceConfig->Bpm[pDestConfig->WorkMode] <= SYS_PARA_BPM_MAX)
            {
                pDestConfig->Bpm[pDestConfig->WorkMode] = pSourceConfig->Bpm[pDestConfig->WorkMode];
            }
            if(pSourceConfig->InspTime[pDestConfig->WorkMode] >= SYS_PARA_INSPTIME_MIN
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= SYS_PARA_INSPTIME_MAX
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= (60 * 8 / pDestConfig->Bpm[pDestConfig->WorkMode]))
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = pSourceConfig->InspTime[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = SYS_PARA_INSPTIME_MIN;
            }
            if(pSourceConfig->StartPress[pDestConfig->WorkMode] <= pDestConfig->Epap[pDestConfig->WorkMode])
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = pSourceConfig->StartPress[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = SYS_PARA_EPAP_MIN;
            }
            if(pSourceConfig->Ramp >= SYS_PARA_RAMP_MIN && pSourceConfig->Ramp <= SYS_PARA_RAMP_MAX)
            {
                pDestConfig->Ramp = pSourceConfig->Ramp;
            }
            if(pSourceConfig->iRamp >= 0 && pSourceConfig->iRamp <= 1)
            {
                pDestConfig->iRamp = pSourceConfig->iRamp;
            }
            if(pSourceConfig->ISlop[pDestConfig->WorkMode] >= SYS_PARA_ISLOP_MIN
                            && pSourceConfig->ISlop[pDestConfig->WorkMode] <= SYS_PARA_ISLOP_MAX)
            {
                pDestConfig->ISlop[pDestConfig->WorkMode] = pSourceConfig->ISlop[pDestConfig->WorkMode];
            }
            break;
        case SYS_WM_ST:
            if(pSourceConfig->Bpm[pDestConfig->WorkMode] >= SYS_PARA_BPM_MIN
                            && pSourceConfig->Bpm[pDestConfig->WorkMode] <= SYS_PARA_BPM_MAX)
            {
                pDestConfig->Bpm[pDestConfig->WorkMode] = pSourceConfig->Bpm[pDestConfig->WorkMode];
            }
            if(pSourceConfig->InspTime[pDestConfig->WorkMode] >= SYS_PARA_INSPTIME_MIN
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= SYS_PARA_INSPTIME_MAX
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= (60 * 8 / pDestConfig->Bpm[pDestConfig->WorkMode]))
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = pSourceConfig->InspTime[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = SYS_PARA_INSPTIME_MIN;
            }
            if(pSourceConfig->MaxInspTime[pDestConfig->WorkMode] >= SYS_PARA_MAXINSPTIME_MIN
                            && pSourceConfig->MaxInspTime[pDestConfig->WorkMode] <= SYS_PARA_MAXINSPTIME_MAX)
            {
                pDestConfig->MaxInspTime[pDestConfig->WorkMode] = pSourceConfig->MaxInspTime[pDestConfig->WorkMode];
            }
            if(pSourceConfig->MinInspTime[pDestConfig->WorkMode] >= SYS_PARA_MAXINSPTIME_MIN
                            && pSourceConfig->MinInspTime[pDestConfig->WorkMode] <= SYS_PARA_MAXINSPTIME_MAX
                            && pSourceConfig->MinInspTime[pDestConfig->WorkMode] <= pDestConfig->MaxInspTime[pDestConfig->WorkMode])
            {
                pDestConfig->MinInspTime[pDestConfig->WorkMode] = pSourceConfig->MinInspTime[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MinInspTime[pDestConfig->WorkMode] = SYS_PARA_MAXINSPTIME_MIN;
            }
            if(pSourceConfig->StartPress[pDestConfig->WorkMode] <= pDestConfig->Epap[pDestConfig->WorkMode])
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = pSourceConfig->StartPress[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = SYS_PARA_EPAP_MIN;
            }
            if(pSourceConfig->Ramp >= SYS_PARA_RAMP_MIN && pSourceConfig->Ramp <= SYS_PARA_RAMP_MAX)
            {
                pDestConfig->Ramp = pSourceConfig->Ramp;
            }
            if(pSourceConfig->iRamp >= 0 && pSourceConfig->iRamp <= 1)
            {
                pDestConfig->iRamp = pSourceConfig->iRamp;
            }
            if(pSourceConfig->ISlop[pDestConfig->WorkMode] >= SYS_PARA_ISLOP_MIN
                            && pSourceConfig->ISlop[pDestConfig->WorkMode] <= SYS_PARA_ISLOP_MAX)
            {
                pDestConfig->ISlop[pDestConfig->WorkMode] = pSourceConfig->ISlop[pDestConfig->WorkMode];
            }
            break;
        case SYS_WM_AUTOB:
            if(pSourceConfig->MaxPS[pDestConfig->WorkMode] >= SYS_PARA_MAXPS_MIN
                            && pSourceConfig->MaxPS[pDestConfig->WorkMode] <= SYS_PARA_MAXPS_MAX)
            {
                pDestConfig->MaxPS[pDestConfig->WorkMode] = pSourceConfig->MaxPS[pDestConfig->WorkMode];
            }
            if(pSourceConfig->MaxIpap[pDestConfig->WorkMode] >= pDestConfig->Ipap[pDestConfig->WorkMode]
                            && pSourceConfig->MaxIpap[pDestConfig->WorkMode] <= MAX_IPAP)
            {
                pDestConfig->MaxIpap[pDestConfig->WorkMode] = pSourceConfig->MaxIpap[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MaxIpap[pDestConfig->WorkMode] = MAX_IPAP;
            }
            if(pSourceConfig->MinEpap[pDestConfig->WorkMode] <= pDestConfig->Epap[pDestConfig->WorkMode])
            {
                pDestConfig->MinEpap[pDestConfig->WorkMode] = (pSourceConfig->MinEpap[pDestConfig->WorkMode]);
            }
            else
            {
                pDestConfig->MinEpap[pDestConfig->WorkMode] = SYS_PARA_EPAP_MIN;
            }
            if(pSourceConfig->StartPress[pDestConfig->WorkMode] <= pDestConfig->Epap[pDestConfig->WorkMode])
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = pSourceConfig->StartPress[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = SYS_PARA_EPAP_MIN;
            }
            if(pSourceConfig->Ramp >= SYS_PARA_RAMP_MIN && pSourceConfig->Ramp <= SYS_PARA_RAMP_MAX)
            {
                pDestConfig->Ramp = pSourceConfig->Ramp;
            }
            if(pSourceConfig->iRamp >= 0 && pSourceConfig->iRamp <= 1)
            {
                pDestConfig->iRamp = pSourceConfig->iRamp;
            }
            if(pSourceConfig->ISlop[pDestConfig->WorkMode] >= SYS_PARA_ISLOP_MIN
                            && pSourceConfig->ISlop[pDestConfig->WorkMode] <= SYS_PARA_ISLOP_MAX)
            {
                pDestConfig->ISlop[pDestConfig->WorkMode] = pSourceConfig->ISlop[pDestConfig->WorkMode];
            }
            if(pSourceConfig->BoostSensitivity >= SYS_PARA_SLOP_SENS_MIN
                            && pSourceConfig->BoostSensitivity <= SYS_PARA_SLOP_SENS_MAX)
            {
                pDestConfig->BoostSensitivity = pSourceConfig->BoostSensitivity;
            }
            if(pSourceConfig->SeparateNight >= SYS_PARA_DIVIDENIGHT_MIN && pSourceConfig->SeparateNight <= SYS_PARA_DIVIDENIGHT_MAX)
            {
                pDestConfig->SeparateNight = pSourceConfig->SeparateNight;
            }
            break;
        case SYS_WM_APCV:
            if(pSourceConfig->Bpm[pDestConfig->WorkMode] >= SYS_PARA_BPM_MIN
                            && pSourceConfig->Bpm[pDestConfig->WorkMode] <= SYS_PARA_BPM_MAX)
            {
                pDestConfig->Bpm[pDestConfig->WorkMode] = pSourceConfig->Bpm[pDestConfig->WorkMode];
            }
            if(pSourceConfig->InspTime[pDestConfig->WorkMode] >= SYS_PARA_INSPTIME_MIN
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= SYS_PARA_INSPTIME_MAX
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= (60 * 8 / pDestConfig->Bpm[pDestConfig->WorkMode]))
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = pSourceConfig->InspTime[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = SYS_PARA_INSPTIME_MIN;
            }
            if(pSourceConfig->StartPress[pDestConfig->WorkMode] <= pDestConfig->Epap[pDestConfig->WorkMode])
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = pSourceConfig->StartPress[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->StartPress[pDestConfig->WorkMode] = SYS_PARA_EPAP_MIN;
            }
            if(pSourceConfig->Ramp >= SYS_PARA_RAMP_MIN && pSourceConfig->Ramp <= SYS_PARA_RAMP_MAX)
            {
                pDestConfig->Ramp = pSourceConfig->Ramp;
            }
            if(pSourceConfig->iRamp >= 0 && pSourceConfig->iRamp <= 1)
            {
                pDestConfig->iRamp = pSourceConfig->iRamp;
            }
            if(pSourceConfig->ISlop[pDestConfig->WorkMode] >= SYS_PARA_ISLOP_MIN
                            && pSourceConfig->ISlop[pDestConfig->WorkMode] <= SYS_PARA_ISLOP_MAX)
            {
                pDestConfig->ISlop[pDestConfig->WorkMode] = pSourceConfig->ISlop[pDestConfig->WorkMode];
            }
            break;
        case SYS_WM_VAF_ST:
            if(pSourceConfig->VT[pDestConfig->WorkMode] >= SYS_PARA_TARGETVT_MIN
                            && pSourceConfig->VT[pDestConfig->WorkMode] <= SYS_PARA_TARGETVT_MAX
                            && ((pSourceConfig->VT[pDestConfig->WorkMode] % 50) == 0))
            {
                pDestConfig->VT[pDestConfig->WorkMode] = pSourceConfig->VT[pDestConfig->WorkMode];
            }
            if(pSourceConfig->MaxIpap[pDestConfig->WorkMode] >= pDestConfig->Ipap[pDestConfig->WorkMode]
                            && pSourceConfig->MaxIpap[pDestConfig->WorkMode] <= MAX_IPAP)
            {
                pDestConfig->MaxIpap[pDestConfig->WorkMode] = pSourceConfig->MaxIpap[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MaxIpap[pDestConfig->WorkMode] = MAX_IPAP;
            }

            if(pSourceConfig->MinIpap[pDestConfig->WorkMode] <= pDestConfig->Ipap[pDestConfig->WorkMode])
            {
                pDestConfig->MinIpap[pDestConfig->WorkMode] = pSourceConfig->MinIpap[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MinIpap[pDestConfig->WorkMode] = SYS_PARA_EPAP_MIN;
            }

            if(pSourceConfig->Bpm[pDestConfig->WorkMode] >= SYS_PARA_BPM_MIN
                            && pSourceConfig->Bpm[pDestConfig->WorkMode] <= SYS_PARA_BPM_MAX)
            {
                pDestConfig->Bpm[pDestConfig->WorkMode] = pSourceConfig->Bpm[pDestConfig->WorkMode];
            }
            if(pSourceConfig->InspTime[pDestConfig->WorkMode] >= SYS_PARA_INSPTIME_MIN
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= SYS_PARA_INSPTIME_MAX
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= (60 * 8 / pDestConfig->Bpm[pDestConfig->WorkMode]))
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = pSourceConfig->InspTime[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = SYS_PARA_INSPTIME_MIN;
            }
            if(pSourceConfig->MaxInspTime[pDestConfig->WorkMode] >= SYS_PARA_MAXINSPTIME_MIN
                            && pSourceConfig->MaxInspTime[pDestConfig->WorkMode] <= SYS_PARA_MAXINSPTIME_MAX)
            {
                pDestConfig->MaxInspTime[pDestConfig->WorkMode] = pSourceConfig->MaxInspTime[pDestConfig->WorkMode];
            }
            if(pSourceConfig->MinInspTime[pDestConfig->WorkMode] >= SYS_PARA_MAXINSPTIME_MIN
                            && pSourceConfig->MinInspTime[pDestConfig->WorkMode] <= SYS_PARA_MAXINSPTIME_MAX
                            && pSourceConfig->MinInspTime[pDestConfig->WorkMode] <= pDestConfig->MaxInspTime[pDestConfig->WorkMode])
            {
                pDestConfig->MinInspTime[pDestConfig->WorkMode] = pSourceConfig->MinInspTime[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MinInspTime[pDestConfig->WorkMode] = SYS_PARA_MAXINSPTIME_MIN;
            }
            if(pSourceConfig->ISlop[pDestConfig->WorkMode] >= SYS_PARA_ISLOP_MIN
                            && pSourceConfig->ISlop[pDestConfig->WorkMode] <= SYS_PARA_ISLOP_MAX)
            {
                pDestConfig->ISlop[pDestConfig->WorkMode] = pSourceConfig->ISlop[pDestConfig->WorkMode];
            }
            break;
        case SYS_WM_VAF_APCV:
            if(pSourceConfig->VT[pDestConfig->WorkMode] >= SYS_PARA_TARGETVT_MIN
                            && pSourceConfig->VT[pDestConfig->WorkMode] <= SYS_PARA_TARGETVT_MAX
                            && ((pSourceConfig->VT[pDestConfig->WorkMode] % 50) == 0))
            {
                pDestConfig->VT[pDestConfig->WorkMode] = pSourceConfig->VT[pDestConfig->WorkMode];
            }
            if(pSourceConfig->MaxIpap[pDestConfig->WorkMode] >= pDestConfig->Ipap[pDestConfig->WorkMode]
                            && pSourceConfig->MaxIpap[pDestConfig->WorkMode] <= MAX_IPAP)
            {
                pDestConfig->MaxIpap[pDestConfig->WorkMode] = pSourceConfig->MaxIpap[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MaxIpap[pDestConfig->WorkMode] = MAX_IPAP;
            }
            if(pSourceConfig->MinIpap[pDestConfig->WorkMode] <= pDestConfig->Ipap[pDestConfig->WorkMode])
            {
                pDestConfig->MinIpap[pDestConfig->WorkMode] = pSourceConfig->MinIpap[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->MinIpap[pDestConfig->WorkMode] = SYS_PARA_EPAP_MIN;
            }
            if(pSourceConfig->Bpm[pDestConfig->WorkMode] >= SYS_PARA_BPM_MIN
                            && pSourceConfig->Bpm[pDestConfig->WorkMode] <= SYS_PARA_BPM_MAX)
            {
                pDestConfig->Bpm[pDestConfig->WorkMode] = pSourceConfig->Bpm[pDestConfig->WorkMode];
            }
            if(pSourceConfig->InspTime[pDestConfig->WorkMode] >= SYS_PARA_INSPTIME_MIN
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= SYS_PARA_INSPTIME_MAX
                            && pSourceConfig->InspTime[pDestConfig->WorkMode] <= (60 * 8 / pDestConfig->Bpm[pDestConfig->WorkMode]))
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = pSourceConfig->InspTime[pDestConfig->WorkMode];
            }
            else
            {
                pDestConfig->InspTime[pDestConfig->WorkMode] = SYS_PARA_INSPTIME_MIN;
            }
            if(pSourceConfig->ISlop[pDestConfig->WorkMode] >= SYS_PARA_ISLOP_MIN
                            && pSourceConfig->ISlop[pDestConfig->WorkMode] <= SYS_PARA_ISLOP_MAX)
            {
                pDestConfig->ISlop[pDestConfig->WorkMode] = pSourceConfig->ISlop[pDestConfig->WorkMode];
            }
            break;
        case SYS_WM_HIGHFLOW:
            if(pSourceConfig->TargetHighFlow >= SYS_PARA_TARGETFLOW_MIN && pSourceConfig->TargetHighFlow <= SYS_PARA_TARGETFLOW_MAX)
            {
                pDestConfig->TargetHighFlow = pSourceConfig->TargetHighFlow;
            }
            break;
    }
}

void CConfigSave::ChangeParameter(PPARAMETERCONFIG pConfig, U8 EffectiveImmediately)
{
    PPARAMETERCONFIG pDestConfig = NULL;     //被配置的参数区

    if(EffectiveImmediately)
    {
        pDestConfig = &m_ParameterConfig;
    }
    else
    {
        pDestConfig = &m_OtherParameterConfig;
    }

    if(memcmp(&(pDestConfig->Ramp), &(pConfig->Ramp), (U32)(&(pConfig->WarmConfig)) - (U32)(&(pConfig->Ramp))) == 0
                    && memcmp(&(pDestConfig->StartPress[0]), &(pConfig->StartPress[0]),
                                    (U32)(&(pConfig->SmartPressure)) - (U32)(&(pConfig->StartPress[0]))) == 0
                    && memcmp(&(pDestConfig->SeparateNight), &(pConfig->SeparateNight),
                                    (U32)(&(pConfig->U8Reserved1[0])) - (U32)(&(pConfig->SeparateNight))) == 0)
    {
        return;
    }
    ChangeParameterFromOtherConfig(pDestConfig, pConfig);

    if(EffectiveImmediately)
    {
        g_EdfDataSave.UpdateSmartPressure();
        SaveParameterData();
        m_OtherParameterConfig.NetConfigFlag = 0;
        PostKeyEvent(MODIFY_PARAMETER);
    }
    else
    {
        m_OtherParameterConfig.NetConfigFlag = 1;
    }
    SaveNetParameterData();
}

/**
* @berif: 管路漏气量是否含面罩漏气量的检查，第一次更新到本程序时，必须不包含面罩漏气量。
*/
void CConfigSave::NoMaskLeakParameterCheck(void)
{
    uint8_t bAirLeakageMark = 0;
    if(true == ReadEEPROM(EEPROM_AIR_LEAKAGE_MARK, &bAirLeakageMark, sizeof(bAirLeakageMark)))
    {
#define NO_MASK_LEAKAGE_MARK_VAL 0x8D
        if(NO_MASK_LEAKAGE_MARK_VAL != bAirLeakageMark)
        {
            bAirLeakageMark = NO_MASK_LEAKAGE_MARK_VAL;
            WriteEEPROM(EEPROM_AIR_LEAKAGE_MARK, &bAirLeakageMark, sizeof(bAirLeakageMark));
            g_ConfigSave.SetParameter(LEAKVIEWTYPE, 1, true);
        }
    }
}

void ChangeParameter(PPARAMETERCONFIG pConfig, U8 EffectiveImmediately)
{
    g_ConfigSave.ChangeParameter(pConfig, EffectiveImmediately);
}

void CheckNetParameter(void)
{
    if(g_ConfigSave.CheckNetParameter())
    {
        PostKeyEvent(MODIFY_PARAMETER);
    }
}
#endif

void UpdateNetSystemConfig(PNETPOWERONINFO pConfig)
{
    g_ConfigSave.UpdateNetSystemConfig(pConfig);
}

CConfigSave g_ConfigSave;


void CConfigSave::LoadDefaultWifiConfig()
{
    memset(&m_WifiConfig, 0, sizeof(m_WifiConfig));
    m_WifiConfig.Switch = 0; // 默认关闭WIFI
    strcpy(m_WifiConfig.SSID, ""); // 清空SSID
    strcpy(m_WifiConfig.PWD, ""); // 清空密码
    
    SaveWifiConfig();
}

bool CConfigSave::LoadWifiConfig()
{
    U8 i = 0;
    U16 sU16EEPROMAddr[2] = {EEPROM_WIFI_CONFIG_ADDR1, EEPROM_WIFI_CONFIG_ADDR2};
    U16 sU16SpiFlashAddr[2] = {SPI_FLASH_WIFI_CONFIG_ADDR1, SPI_FLASH_WIFI_CONFIG_ADDR2};
    bool loadSuccess = false;

#if (ENABLE_ENCRYPTION == 1)
    // 尝试从EEPROM读取WIFI安全容器
    for(i = 0; i < 2; i++)
    {
        if(ReadEEPROM(sU16EEPROMAddr[i], (U8 *)&m_SecureWifiContainer, sizeof(m_SecureWifiContainer)))
        {
            // 解密并验证WIFI配置
            if(DecryptWifiConfig())
            {
                loadSuccess = true;
                break;
            }
        }
    }
    
    // 如果从EEPROM读取失败，尝试从SPI Flash读取
    if (!loadSuccess) {
        for(i = 0; i < 2; i++)
        {
            if(SPIFLASH_ReadBuff((U8 *)&m_SecureWifiContainer, sU16SpiFlashAddr[i], 
                                 sizeof(m_SecureWifiContainer)) == 0)
            {
                // 解密并验证WIFI配置
                if(DecryptWifiConfig())
                {
                    loadSuccess = true;
                    break;
                }
            }
        }
    }
#else
    // 非加密模式，直接读取WIFI配置
    for(i = 0; i < 2; i++)
    {
        if(ReadEEPROM(sU16EEPROMAddr[i], (U8 *)&m_WifiConfig, sizeof(m_WifiConfig)))
        {
            loadSuccess = true;
            break;
        }
    }
    
    if (!loadSuccess) {
        for(i = 0; i < 2; i++)
        {
            if(SPIFLASH_ReadBuff((U8 *)&m_WifiConfig, sU16SpiFlashAddr[i], 
                                 sizeof(m_WifiConfig)) == 0)
            {
                loadSuccess = true;
                break;
            }
        }
    }
#endif

    if (loadSuccess) {
        return true;
    }

    // 所有读取尝试都失败，加载默认配置
    LoadDefaultWifiConfig();
    return false;
}

bool CConfigSave::SaveWifiConfig()
{
#if (ENABLE_ENCRYPTION == 1)
    // 加密WIFI配置并放入安全容器
    if (!EncryptWifiConfig()) {
        DEBUG_INFO("EncryptWifiConfig failed");
        return false;  // 加密失败
    }

    WriteEEPROM(EEPROM_WIFI_CONFIG_ADDR1, (U8 *)&m_SecureWifiContainer, sizeof(m_SecureWifiContainer));
    WriteEEPROM(EEPROM_WIFI_CONFIG_ADDR2, (U8 *)&m_SecureWifiContainer, sizeof(m_SecureWifiContainer));

    SPIFLASH_WriteBuff((U8 *)&m_SecureWifiContainer, SPI_FLASH_WIFI_CONFIG_ADDR1, sizeof(m_SecureWifiContainer));
    SPIFLASH_WriteBuff((U8 *)&m_SecureWifiContainer, SPI_FLASH_WIFI_CONFIG_ADDR2, sizeof(m_SecureWifiContainer));
#else
    // 非加密模式，直接保存WIFI配置
    WriteEEPROM(EEPROM_WIFI_CONFIG_ADDR1, (U8 *)&m_WifiConfig, sizeof(m_WifiConfig));
    WriteEEPROM(EEPROM_WIFI_CONFIG_ADDR2, (U8 *)&m_WifiConfig, sizeof(m_WifiConfig));

    SPIFLASH_WriteBuff((U8 *)&m_WifiConfig, SPI_FLASH_WIFI_CONFIG_ADDR1, sizeof(m_WifiConfig));
    SPIFLASH_WriteBuff((U8 *)&m_WifiConfig, SPI_FLASH_WIFI_CONFIG_ADDR2, sizeof(m_WifiConfig));
#endif

    return true;
}

#if (ENABLE_ENCRYPTION == 1)
bool CConfigSave::EncryptWifiConfig()
{    
    // 加密WIFI配置
    size_t encrypted_size = encrypt_data(
        AES_LOCAL_DATA_KEY,
        (uint8_t*)&m_WifiConfig, 
        m_SecureWifiContainer.encrypted_data, 
        sizeof(m_WifiConfig)
    );
    
    if (encrypted_size == 0)
        return false;
    
    // 计算安全容器的校验和
    m_SecureWifiContainer.checksum = CheckSum(&m_SecureWifiContainer, sizeof(m_SecureWifiContainer) - 1);
    
    return true;
}

bool CConfigSave::DecryptWifiConfig()
{
    // 验证安全容器的校验和
    if (CheckSum(&m_SecureWifiContainer, sizeof(m_SecureWifiContainer) - 1) != m_SecureWifiContainer.checksum)
        return false;
    
    // 解密WIFI配置
    size_t decrypted_size = decrypt_data(
        AES_LOCAL_DATA_KEY,
        m_SecureWifiContainer.encrypted_data,
        (uint8_t*)&m_WifiConfig,
        sizeof(m_SecureWifiContainer.encrypted_data)
    );
    
    if (decrypted_size == 0)
        return false;
    
    return true;
}
#endif

// 添加获取WIFICONFIG的方法
const WIFICONFIG* CConfigSave::GetWifiConfig()
{
    return &m_WifiConfig;
}

