/*********************************************************************
*                                                                    *
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
*                                                                    *
**********************************************************************
*                                                                    *
* C-file generated by:                                               *
*                                                                    *
*        GUI_Builder for emWin version 5.32                          *
*        Compiled Oct  8 2015, 11:59:02                              *
*        (c) 2015 Segger Microcontroller GmbH & Co. KG               *
*                                                                    *
**********************************************************************
*                                                                    *
*        Internet: www.segger.com  Support: <EMAIL>       *
*                                                                    *
**********************************************************************
*/

// USER START (Optionally insert additional includes)
#include "StaterBar.h"
#include "Key.h"
#include "GlobalVariable.h"
#include "bsp_MX25L256.h"
#include "configsave.h"
#include "stack.h"
#include "StaterBar.h"
// USER END

#include "DIALOG.h"


/*********************************************************************
*
*       Defines
*
**********************************************************************
*/
#define ID_WINDOW_0    (GUI_ID_USER + 0x00)
#define ID_TEXT_0    (GUI_ID_USER + 0x01)
#define ID_TEXT_1    (GUI_ID_USER + 0x02)
#define ID_TEXT_2    (GUI_ID_USER + 0x03)
#define ID_TEXT_3    (GUI_ID_USER + 0x04)
#define ID_TEXT_4    (GUI_ID_USER + 0x05)
#define ID_TEXT_5    (GUI_ID_USER + 0x06)
#define ID_TEXT_6    (GUI_ID_USER + 0x07)
#define ID_TEXT_7    (GUI_ID_USER + 0x08)
#define ID_TEXT_8    (GUI_ID_USER + 0x09)
#define ID_TEXT_9    (GUI_ID_USER + 0x0A)
#define ID_TEXT_10    (GUI_ID_USER + 0x0B)
#define ID_TEXT_11    (GUI_ID_USER + 0x0C)
#define ID_TEXT_12    (GUI_ID_USER + 0x0D)
//#define ID_TEXT_13    (GUI_ID_USER + 0x0E)
//#define ID_TEXT_14    (GUI_ID_USER + 0x0F)
#define ID_BUTTON_0    (GUI_ID_USER + 0x10)


// USER START (Optionally insert additional defines)
WM_HWIN	g_DeviceInfoHwin = NULL;
static void ShowProcess(void);
WINDOWINFO g_DeviceInfoWInfo = {NULL, NULL, NULL, NULL, ShowProcess, NULL, 0, 0, DEVICEINFO_ID};
// USER END


/*********************************************************************
*
*       Static data
*
**********************************************************************
*/

// USER START (Optionally insert additional static data)
// USER END

/*********************************************************************
*
*       _aDialogCreate
*/
#if (LCD_TYPE == LCD_5_TFT)
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] = {
  { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 70, 800, 410, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_0, 0, 0, 800, 80, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "ChipModelText", ID_TEXT_1, 80, 85, 210, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "ChipModelValue", ID_TEXT_2, 300, 85, 220, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PCBVerText", ID_TEXT_3, 80, 119, 210, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PCBVerValue", ID_TEXT_4, 300, 119, 220, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SoftwareVerText", ID_TEXT_5, 80, 153, 210, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SoftwareVerValue", ID_TEXT_6, 300, 153, 220, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "BootloaderVerText", ID_TEXT_7, 80, 187, 210, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "BootloaderVerValue", ID_TEXT_8, 300, 187, 220, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SNText", ID_TEXT_9, 80, 221, 250, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SNValue", ID_TEXT_10, 300, 221, 260, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "ProductionDateText", ID_TEXT_11, 80, 255, 210, 24, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "TextProductionDateValue", ID_TEXT_12, 300, 255, 220, 24, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "ProductionDateText", ID_TEXT_13, 80, 289, 210, 24, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "TextProductionDateValue", ID_TEXT_14, 300, 289, 220, 24, 0, 0x64, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_0, 80, 364, 80, 30, 0, 0x0, 0 },
  // USER START (Optionally insert additional widgets)
  // USER END
};
#else
#if (LCD_TYPE == LCD_28_TFT)
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] = {
  { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 0, 240, 320, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_0, 0, 0, 240, 53, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "ChipModelText",      ID_TEXT_1, 1,   50, 119, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "ChipModelValue",     ID_TEXT_2, 110, 50, 132, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PCBVerText",         ID_TEXT_3, 1,   73, 119, 46, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PCBVerValue",        ID_TEXT_4, 110, 73, 132, 46, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SoftwareVerText",    ID_TEXT_5, 1,   119, 119, 46, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SoftwareVerValue",   ID_TEXT_6, 110, 119, 132, 46, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "BootloaderVerText",  ID_TEXT_7, 1,   165, 119, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "BootloaderVerValue", ID_TEXT_8, 110, 165, 132, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SNText",             ID_TEXT_9, 1,   188, 119, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SNValue",            ID_TEXT_10,110, 188, 175, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "ProductionDateText", ID_TEXT_11,1,   211, 119, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "TextProductionDateValue", ID_TEXT_12, 110, 211, 132, 16, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "ProductionDateText", ID_TEXT_13, 1, 192, 119, 16, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "TextProductionDateValue", ID_TEXT_14, 120, 192, 132, 16, 0, 0x64, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_0, 1, 244, 48, 20, 0, 0x0, 0 },
  // USER START (Optionally insert additional widgets)
  // USER END
#else
static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] = {
  { WINDOW_CreateIndirect, "Window", ID_WINDOW_0, 0, 0, 480, 320, 0, 0x0, 0 },
  { TEXT_CreateIndirect, "Text", ID_TEXT_0, 0, 0, 480, 53, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "ChipModelText", ID_TEXT_1, 48, 56, 126, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "ChipModelValue", ID_TEXT_2, 180, 56, 200, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PCBVerText", ID_TEXT_3, 48, 79, 126, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "PCBVerValue", ID_TEXT_4, 180, 79, 200, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SoftwareVerText", ID_TEXT_5, 48, 102, 126, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SoftwareVerValue", ID_TEXT_6, 180, 102, 200, 16, 0, 0x64, 0 },
  
  { TEXT_CreateIndirect, "BootloaderVerText", ID_TEXT_7, 48, 124, 126, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "BootloaderVerValue", ID_TEXT_8, 180, 124, 200, 16, 0, 0x64, 0 },
  
  { TEXT_CreateIndirect, "SNText", ID_TEXT_9, 48, 147, 126, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "SNValue", ID_TEXT_10, 180, 147, 170, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "ProductionDateText", ID_TEXT_11, 48, 170, 126, 16, 0, 0x64, 0 },
  { TEXT_CreateIndirect, "TextProductionDateValue", ID_TEXT_12, 180, 170, 132, 16, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "ProductionDateText", ID_TEXT_13, 48, 192, 126, 16, 0, 0x64, 0 },
//  { TEXT_CreateIndirect, "TextProductionDateValue", ID_TEXT_14, 180, 192, 132, 16, 0, 0x64, 0 },
  { BUTTON_CreateIndirect, "Button", ID_BUTTON_0, 48, 244, 48, 20, 0, 0x0, 0 },
  // USER START (Optionally insert additional widgets)
  // USER END
#endif
};
#endif

/*********************************************************************
*
*       Static code
*
**********************************************************************
*/

// USER START (Optionally insert additional static code)
// USER END

/*********************************************************************
*
*       _cbDialog
*/
U32 ID[2] = {0};
static void _cbDialog(WM_MESSAGE * pMsg) {
  WM_HWIN hItem;
  int     NCode;
  int     Id;
  // USER START (Optionally insert additional variables)
    MACHINEINFO MachineInfo;
    const MACHINECONFIG* pMachineConfig = g_ConfigSave.GetMachineConfig();
    char str[32];
    memcpy(&MachineInfo, (PMACHINEINFO)MACHINEINFO_ADDR, sizeof(MACHINEINFO));
  // USER END

  switch (pMsg->MsgId) {
  case WM_INIT_DIALOG:
    //
    // Initialization of 'Window'
    //
    hItem = pMsg->hWin;
    WINDOW_SetBkColor(hItem, GUI_MAKE_COLOR(0x00000000));
    //
    // Initialization of 'Text'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_0);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_HCENTER | GUI_TA_VCENTER);
    TEXT_SetText(hItem, GetMultiLanguageString(DEBUG_DEVICEINFOTITLE_IDX));
    //
    // Initialization of 'ChipModelText'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_1);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    TEXT_SetText(hItem, GetMultiLanguageString(DEBUG_CHIPMODELTEXT_IDX));
    //
    // Initialization of 'ChipModelValue'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_2);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetText(hItem, "STM32F407ZGT6");
    //
    // Initialization of 'PCBVerText'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_3);
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif    
    TEXT_SetText(hItem, GetMultiLanguageString(DEBUG_FONTBMPVERSIONTEXT_IDX));
    //
    // Initialization of 'PCBVerValue'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_4);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    TEXT_SetWrapMode(hItem, GUI_WRAPMODE_CHAR);
    strncat(g_I8ResourceVersion,GetResourceVersion(),RESOURCE_VERSION_MAX-strlen(g_I8ResourceVersion));
    TEXT_SetText(hItem, g_I8ResourceVersion);
    //
    // Initialization of 'SoftwareVerText'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_5);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    TEXT_SetText(hItem, GetMultiLanguageString(DEBUG_SOFTWAREVERTEXT_IDX));
    //
    // Initialization of 'SoftwareVerValue'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_6);
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    #if (LCD_TYPE == LCD_28_TFT)
    TEXT_SetWrapMode(hItem, GUI_WRAPMODE_CHAR);
	#endif
    TEXT_SetText(hItem, g_I8AppSoftVersion);
    //
    // Initialization of 'BootloaderVerText'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_7);
    ID[1] = DEBUG_BOOTLOADERTEXT_IDX;
    TEXT_SetText(hItem, GetMultiLanguageString(DEBUG_BOOTLOADERTEXT_IDX));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    //
    // Initialization of 'BootloaderVerValue'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_8);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    
//    if (MachineInfo.CheckSum != CheckSum(&MachineInfo, sizeof(MACHINEINFO) - 1))
//    {
//        SPIFLASH_ReadBuff((uint8_t*)&MachineInfo, SPI_FLASH_MACHINE_INFO_ADDR, sizeof(MACHINEINFO));
//    }
//    switch (MachineInfo.LcdType)
//    {
//        case 1:
//            sprintf(str, "R V%d.%d.%d.%02d.%05d", MachineInfo.SoftMajorVersion, MachineInfo.SoftMinorVersion, MachineInfo.SoftCorrectVersion, (MachineInfo.SoftReVersion >> 2), (int)MachineInfo.SoftBuildFullVersion);    
//            break;
//        case 2:
//#if RESFREE_MODEL_3_5
//            sprintf(str, "F V%d.%d.%d.%02d.%05d", MachineInfo.SoftMajorVersion, MachineInfo.SoftMinorVersion, MachineInfo.SoftCorrectVersion, (MachineInfo.SoftReVersion >> 2), (int)MachineInfo.SoftBuildFullVersion);
//#elif RESHOPE_MODEL_3_5
//            sprintf(str, "SH V%d.%d.%d.%02d.%05d", MachineInfo.SoftMajorVersion, MachineInfo.SoftMinorVersion, MachineInfo.SoftCorrectVersion, (MachineInfo.SoftReVersion >> 2), (int)MachineInfo.SoftBuildFullVersion);
//#else
//            sprintf(str, "F V%d.%d.%d.%02d.%05d", MachineInfo.SoftMajorVersion, MachineInfo.SoftMinorVersion, MachineInfo.SoftCorrectVersion, (MachineInfo.SoftReVersion >> 2), (int)MachineInfo.SoftBuildFullVersion);
//#endif
//                
//            break;
//        case 3:
//            sprintf(str, "XP V%d.%d.%d.%02d.%05d", MachineInfo.SoftMajorVersion, MachineInfo.SoftMinorVersion, MachineInfo.SoftCorrectVersion, (MachineInfo.SoftReVersion >> 2), (int)MachineInfo.SoftBuildFullVersion); 
//            break;
//        default:
//            sprintf(str, "V%d.%d.%d.%02d.%05d", MachineInfo.SoftMajorVersion, MachineInfo.SoftMinorVersion, MachineInfo.SoftCorrectVersion, (MachineInfo.SoftReVersion >> 2), (int)MachineInfo.SoftBuildFullVersion);
//            break;
//    }
	#if (LCD_TYPE == LCD_28_TFT)
    TEXT_SetWrapMode(hItem,GUI_WRAPMODE_CHAR);
	#endif
    TEXT_SetText(hItem, g_I8AppSoftVersion);
    //
    // Initialization of 'SNText'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_9);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetText(hItem, GetMultiLanguageString(DEBUG_SNTEXT_IDX));
    //
    // Initialization of 'SNValue'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_10);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    TEXT_SetText(hItem, g_I8Sn);
    //
    // Initialization of 'ProductionDateText'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_11);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x0000FF00));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    TEXT_SetText(hItem, GetMultiLanguageString(DEBUG_PRODUCTIONDATE_IDX));
    //
    // Initialization of 'TextProductionDateValue'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_12);
    TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
    #if (LCD_TYPE == LCD_5_TFT)
        TEXT_SetFont(hItem, FONT_24);
    #else
        TEXT_SetFont(hItem, FONT_18);
    #endif
    TEXT_SetTextAlign(hItem, GUI_TA_LEFT | GUI_TA_VCENTER);
    
    sprintf(str, "%04d-%02d", 2022 + pMachineConfig->Year, pMachineConfig->Month + 1);
    
    TEXT_SetText(hItem, str);

    //
    // Initialization of 'Button'
    //
    hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0);
    #if (LCD_TYPE == LCD_5_TFT)
        BUTTON_SetFont(hItem, FONT_24);
    #else
        BUTTON_SetFont(hItem, FONT_18);
    #endif
    
    BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0), GetMultiLanguageString(DEBUG_BUZZEREXIT_IDX));
    #if (LCD_TYPE == LCD_5_TFT)
        BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0), FONT_24);
    #else
        BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0), FONT_18);
    #endif
    _SetButtonSkin(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0));
    
    // USER START (Optionally insert additional code for further widget initialization)
    // USER END
    break;
  case WM_NOTIFY_PARENT:
    Id    = WM_GetId(pMsg->hWinSrc);
    NCode = pMsg->Data.v;
    switch(Id) {
    case ID_BUTTON_0: // Notifications sent by 'Button'
      switch(NCode) {
      case WM_NOTIFICATION_CLICKED:
        // USER START (Optionally insert code for reacting on notification message)
        ShowDebugDeviceInfo(0);
        // USER END
        break;
      case WM_NOTIFICATION_RELEASED:
        // USER START (Optionally insert code for reacting on notification message)
        // USER END
        break;
      // USER START (Optionally insert additional code for further notification handling)
      // USER END
      }
      break;
    // USER START (Optionally insert additional code for further Ids)
    // USER END
    }
    break;
  // USER START (Optionally insert additional message handling)
  // USER END
  default:
    WM_DefaultProc(pMsg);
    break;
  }
}

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/

// USER START (Optionally insert additional public code)
void ShowDebugDeviceInfo(U8 Flag)
{
    if (Flag)
    {
        if (g_DeviceInfoHwin == NULL)
        {        
            g_DeviceInfoHwin = GUI_CreateDialogBox(_aDialogCreate, GUI_COUNTOF(_aDialogCreate), _cbDialog, WM_HBKWIN, 0, 0);
        }
        ShowProcess();
        g_WindowDrv.PushWindow(&g_DeviceInfoWInfo);        
    }
    else
    {
        WM_HideWindow(g_DeviceInfoHwin);
        g_WindowDrv.PopWindow(&g_DeviceInfoWInfo);
#if (LCD_TYPE != LCD_5_TFT)
        RepaintTopStateBar();
#endif
    }	
}

static void ShowProcess(void)
{
    WM_SetFocus(WM_GetDialogItem(g_DeviceInfoHwin, ID_BUTTON_0));
    WM_ShowWindow(g_DeviceInfoHwin);
}
// USER END

/*************************** End of file ****************************/


