#include "update.h"
#include "fatfs.h"
#include "bsp_tft_lcd.h"
#include "bsp_MX25L256.h"
#include "GlobalVariable.h"
#include "SSD1963.h"
#include "ili9488.h"
#include "crypto.h" 

// 添加函数声明
extern eFileStatus_t validate_file_content(FIL* fil, uint32_t fileSize);

uint8_t CheckSum(void *b, int Size)
{
    uint8_t* spU8buff = (uint8_t*)b;
    uint8_t sU8Sum = 0;
    while (Size)
    {
        sU8Sum += *spU8buff++;
        Size--;
    }
    return (~sU8Sum);
}

uint32_t GetBank(uint32_t Addr)
{
//    if (Addr < 0x100000)    
//    {
        return FLASH_BANK_1;
//    }
//    return FLASH_BANK_2;
}

uint32_t GetSector(uint32_t Addr)
{
    return FLASH_SECTOR_0 + Addr / 0x20000;    
}

//const char* g_pI8FileName[] = {APP_FILE, FONT_FILE, BMP_FILE};
const char* g_pI8FileName[] = {APP_FILE, FONTBMP_FILE, CONFIG_FILE};
unsigned char g_UpdateFileFlag = 0;         //bit 0:App 1:Font 2:Config

typedef void(*pFun)(void);

#if (LCD_TYPE == LCD_28_TFT)
short g_S16X = 5;
short g_S16Y = 0;
#else
short g_S16X = 5;
short g_S16Y = 5;
#endif

uint32_t g_U32BTick = 0, g_U32ETick = 0;
uint32_t g_U32EraseTick = 0;
uint32_t g_U32ProgramTick = 0;
volatile uint32_t g_U32AppStartTick = 0;

void SetLcdBrightness(uint8_t Level)
{
//    U16 PWMDuty[] = {0, 100, 300, 500, 700, 900};
    uint16_t PWMDuty[] = {0, 33, 66, 100, 166, 233, 300, 366, 433, 500, 566, 633, 700, 766, 833, 900};
    if (Level > 15)
    {
        Level = 15;
    }
#if (LCD_TYPE != LCD_5_TFT) //60KHZ
  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
  TIM_OC_InitTypeDef sConfigOC = {0};

//  HAL_TIM_PWM_Stop(&htim12, TIM_CHANNEL_1);
//  HAL_GPIO_WritePin(GPIOA1, GPIO_PIN_1, GPIO_PIN_RESET);
  htim5.Instance = TIM5;
  htim5.Init.Prescaler = 1;
  htim5.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim5.Init.Period = 999;
  htim5.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim5.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
  if (HAL_TIM_Base_Init(&htim5) != HAL_OK)
  {
    Error_Handler();
  }
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
  if (HAL_TIM_ConfigClockSource(&htim5, &sClockSourceConfig) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_PWM_Init(&htim5) != HAL_OK)
  {
    Error_Handler();
  }
  sConfigOC.OCMode = TIM_OCMODE_PWM1;
  sConfigOC.Pulse = PWMDuty[Level];
  sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
  sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
  if (HAL_TIM_PWM_ConfigChannel(&htim5, &sConfigOC, TIM_CHANNEL_2) != HAL_OK)
  {
    Error_Handler();
  }
  HAL_TIM_MspPostInit(&htim5);
  HAL_TIM_PWM_Start(&htim5, TIM_CHANNEL_2);
  
#else
    TIM_ClockConfigTypeDef sClockSourceConfig = {0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    TIM_OC_InitTypeDef sConfigOC = {0};
    TIM_BreakDeadTimeConfigTypeDef sBreakDeadTimeConfig = {0};
    htim5.Instance = TIM5;
    htim5.Init.Prescaler = 119;
    htim5.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim5.Init.Period = 999;
    htim5.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim5.Init.RepetitionCounter = 0;
    htim5.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
    if (HAL_TIM_Base_Init(&htim5) != HAL_OK)
    {
        Error_Handler();
    }
    sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
    if (HAL_TIM_ConfigClockSource(&htim5, &sClockSourceConfig) != HAL_OK)
    {
        Error_Handler();
    }
    if (HAL_TIM_PWM_Init(&htim5) != HAL_OK)
    {
        Error_Handler();
    }
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim5, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = PWMDuty[Level];
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCNPolarity = TIM_OCNPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    sConfigOC.OCIdleState = TIM_OCIDLESTATE_RESET;
    sConfigOC.OCNIdleState = TIM_OCNIDLESTATE_RESET;
    if (HAL_TIM_PWM_ConfigChannel(&htim5, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
    {
        Error_Handler();
    }
    sBreakDeadTimeConfig.OffStateRunMode = TIM_OSSR_DISABLE;
    sBreakDeadTimeConfig.OffStateIDLEMode = TIM_OSSI_DISABLE;
    sBreakDeadTimeConfig.LockLevel = TIM_LOCKLEVEL_OFF;
    sBreakDeadTimeConfig.DeadTime = 0;
    sBreakDeadTimeConfig.BreakState = TIM_BREAK_DISABLE;
    sBreakDeadTimeConfig.BreakPolarity = TIM_BREAKPOLARITY_HIGH;
    sBreakDeadTimeConfig.BreakFilter = 0;
    sBreakDeadTimeConfig.AutomaticOutput = TIM_AUTOMATICOUTPUT_DISABLE;
    if (HAL_TIMEx_ConfigBreakDeadTime(&htim5, &sBreakDeadTimeConfig) != HAL_OK)
    {
        Error_Handler();
    }
    HAL_TIM_MspPostInit(&htim5);
	HAL_TIM_PWM_Start(&htim5, TIM_CHANNEL_1);
#endif    
    if (Level)
    {
        HAL_GPIO_WritePin(LCD_ON_OFF_GPIO_Port, LCD_ON_OFF_Pin, GPIO_PIN_SET);
//        HAL_GPIO_WritePin(LED_ALARM_GPIO_Port, LED_ALARM_Pin, GPIO_PIN_SET);
    }
    else
    {
        HAL_GPIO_WritePin(LCD_ON_OFF_GPIO_Port, LCD_ON_OFF_Pin, GPIO_PIN_RESET);
//        HAL_GPIO_WritePin(LED_ALARM_GPIO_Port, LED_ALARM_Pin, GPIO_PIN_RESET);
    } 
}

/*****************************************************************************
 函 数 名  : CheckMachineInfo
 功能描述  : 驱动版本号存储
 输入参数  : 无
 输出参数  : 无

 调用函数  :
 被调函数  :
 注        : 上电会检测一下版本号flash中版本号是否一至 不一至重新写入

*****************************************************************************/
void CheckMachineInfo(void)
{
    HAL_StatusTypeDef status;
    FLASH_EraseInitTypeDef EraseInitStruct;    
    uint32_t pageErr;
    MACHINEINFO Info; //软件版本号;
    uint8_t* pInfo;
    uint32_t sU32SectorAddr = 0, sU32SoftBuildVersion;
    uint8_t sU8SoftMajorVersion, sU8SoftMinorVersion, sU8SoftCorrectVersion, sU8SoftReVersion, sU8SoftBuildVersion;
    
    sscanf(TOSTRING(VERSION_NUMBER), "V%hhu.%hhu.%hhu.%hhu", &sU8SoftMajorVersion, &sU8SoftMinorVersion, &sU8SoftCorrectVersion, &sU8SoftReVersion);
    
    sU32SoftBuildVersion = atoi(TOSTRING(SVN_NUMBER));
    
    sU8SoftReVersion = ((sU8SoftReVersion << 2) & 0xff) | (((sU32SoftBuildVersion % 1000) >> 8) & 0x03);
    sU8SoftBuildVersion = (sU32SoftBuildVersion % 1000) & 0xff;
    
    memcpy(&Info, (PMACHINEINFO)MACHINEINFO_ADDR, sizeof(MACHINEINFO));
	   
    if (Info.CheckSum == CheckSum(&Info, sizeof(MACHINEINFO) - 1))
    {
        if (Info.SoftMajorVersion == sU8SoftMajorVersion &&
            Info.SoftMinorVersion == sU8SoftMinorVersion &&
            Info.SoftCorrectVersion == sU8SoftCorrectVersion &&
            Info.SoftReVersion == sU8SoftReVersion &&
            Info.SoftBuildVersion == sU8SoftBuildVersion && Info.SoftBuildFullVersion == sU32SoftBuildVersion && Info.LcdType == LCD_TYPE)
        {
            return;
        }
    }
    else
    {
        SPI_FLASH_BufferRead((uint8_t*)&Info, MACHINE_INFO_ADDR, sizeof(MACHINEINFO));
        if (Info.CheckSum == CheckSum(&Info, sizeof(MACHINEINFO) - 1))
        {
            if (Info.SoftMajorVersion == sU8SoftMajorVersion &&
                Info.SoftMinorVersion == sU8SoftMinorVersion &&
                Info.SoftCorrectVersion == sU8SoftCorrectVersion &&
                Info.SoftReVersion    == sU8SoftReVersion &&
                Info.SoftBuildVersion == sU8SoftBuildVersion && Info.SoftBuildFullVersion == sU32SoftBuildVersion && Info.LcdType == LCD_TYPE)
            {
                return;
            }
        }
    }
    
    sU32SectorAddr = MACHINEINFO_ADDR / 2048 * 2048;
    HAL_FLASH_Unlock();
    __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_OPERR | FLASH_FLAG_WRPERR | 
                          FLASH_FLAG_PGAERR | FLASH_FLAG_PGPERR | FLASH_FLAG_PGSERR);
               
    Info.SoftMajorVersion = sU8SoftMajorVersion;
    Info.SoftMinorVersion = sU8SoftMinorVersion;
    Info.SoftCorrectVersion = sU8SoftCorrectVersion;
    Info.SoftReVersion = sU8SoftReVersion;
    Info.SoftBuildVersion = sU8SoftBuildVersion;
    Info.LcdType = LCD_TYPE;
    Info.SoftBuildFullVersion = sU32SoftBuildVersion;
    
    memset(Info.Reserve, 0, sizeof(Info.Reserve));
    
    Info.CheckSum = CheckSum(&Info, sizeof(MACHINEINFO) - 1);
        
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_SECTORS;
//    EraseInitStruct.Banks = GetBank(MACHINEINFO_ADDR - 0x8000000);
    EraseInitStruct.Sector = FLASH_SECTOR_11;//GetSector(MACHINEINFO_ADDR - 0x8000000);
    EraseInitStruct.NbSectors = 1;
    EraseInitStruct.VoltageRange = FLASH_VOLTAGE_RANGE_3;
    status = HAL_FLASHEx_Erase(&EraseInitStruct, &pageErr);    
    FLASH_WaitForLastOperation(50000);
    
    if (status != HAL_OK)
    {
        /*while (1)
        { 
            HAL_GPIO_TogglePin(BJ_LED1_GPIO_Port, BJ_LED1_Pin);
            HAL_Delay(100);
        }*/
    }
        
    pInfo = (uint8_t*)&Info;
    for(sU32SectorAddr = MACHINEINFO_ADDR; sU32SectorAddr < MACHINEINFO_ADDR + sizeof(Info); sU32SectorAddr += 4)
    {     
        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, (uint32_t)sU32SectorAddr, *(uint32_t*)pInfo);
        /*if (status == HAL_ERROR)
        {
            while (1)
            {            
                HAL_GPIO_TogglePin(GPIOE, GPIO_PIN_6);
                HAL_Delay(100);
            } 
        }*/
        FLASH_WaitForLastOperation(50000);                        
        pInfo += 4;
    }
    HAL_FLASH_Lock();   
//    SPI_FLASH_SectorErase(MACHINE_INFO_ADDR);
    SPI_FLASH_BufferWrite((uint8_t*)&Info, MACHINE_INFO_ADDR, sizeof(Info));    
}

volatile pFun AppFun;

/*static void Delay(void)
{
    int i;
    for (i = 0; i < 1000000; i++)
    {}
}*/

FONT_T tFont16;			/* 定义一个字体结构体变量，用于设置字体参数 */

void InitFont(void)
{
	/* 设置字体参数 */
	{
		tFont16.FontCode = FC_ST_16;	    /* 字体代码 16点阵 */
		tFont16.FrontColor = CL_WHITE;		/* 字体颜色 */
		tFont16.BackColor = CL_BLUE;	    /* 文字背景颜色 */
		tFont16.Space = 0;					/* 文字间距，单位 = 像素 */
	}
}

void DisplayStr(char* str, uint8_t ChangeLine)
{    
    if (ChangeLine)
    {
        LCD_DispStr(g_S16X, g_S16Y, str, &tFont16); 
        #if (LCD_TYPE == LCD_28_TFT)
        g_S16Y += 16;
        #else
        g_S16Y += 20;
        #endif
    }
    else
    {
        LCD_Fill_Rect(g_S16X, g_S16Y, g_LcdWidth, 20, CL_BLUE);
        LCD_DispStr(g_S16X, g_S16Y, str, &tFont16);  
    }
}

static HAL_StatusTypeDef eraseFlash(void)
{
    char str[64];	
	  FLASH_EraseInitTypeDef EraseInitStruct;
	  uint32_t pageErr;
    HAL_StatusTypeDef status;	
	
	
    // Unlock Flash and erase necessary sectors
    HAL_FLASH_Unlock();
    __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_OPERR | FLASH_FLAG_WRPERR | 
                           FLASH_FLAG_PGAERR | FLASH_FLAG_PGPERR | FLASH_FLAG_PGSERR);

    DisplayStr("Erasing MCU Flash...", 1);
    g_U32BTick = HAL_GetTick();

    EraseInitStruct.TypeErase = FLASH_TYPEERASE_SECTORS ;
    EraseInitStruct.Sector = FLASH_SECTOR_5;
    EraseInitStruct.NbSectors = 6;
    EraseInitStruct.VoltageRange = FLASH_VOLTAGE_RANGE_3;
    status = HAL_FLASHEx_Erase(&EraseInitStruct, &pageErr);    
    if (status != HAL_OK)
    {
        return status;
    } 

    g_U32ETick = HAL_GetTick();
    g_U32EraseTick = g_U32ETick - g_U32BTick;

    snprintf(str, sizeof(str), "Erase Time = %.1f s", (float)g_U32EraseTick);
    DisplayStr(str, 1);
//    DisplayStr("Erase done.", 1);
		
    return status;
}

static void InAppFontBmpProgramFailed(EWRITEERRORTYPE EWriteType)
{
	HAL_StatusTypeDef hal_check;
	// TODO: Create a generaol screen buffer
	char str[64];
    switch (EWriteType)
    {
    case ERASE_APP_ADDR_ERROR:
        DisplayStr("Program App fail,Erase Addr Error.", 1);
        break;
    case OPEN_APP_FILE_ERROR:
        DisplayStr("Open App File failed.", 1);
        break;
    case READ_APP_FILE_ERROR:
        DisplayStr("Read App File failed.", 1);
        break;
    case CHECK_APP_DATA_ERROR:
        DisplayStr("Check App Data failed.", 1);
        break;
    case OPEN_BMPFONT_FILE_ERROR:
        DisplayStr("Open BmpFont File failed.", 1);
        break;
    case READ_BMPFONT_FILE_ERROR:
        DisplayStr("Read BmpFont File failed.", 1);
        break;
    case CHECK_BMPFONT_DATA_ERROR:
        DisplayStr("Check BmpFont File failed.", 1);
        break;
    case OPEN_CONFIG_FILE_ERROR:
        DisplayStr("Open Config File failed.", 1);
        break;
    case READ_CONFIG_FILE_ERROR:
        DisplayStr("Read Config File failed.", 1);
        break;
    case PARSE_CONFIG_FILE_ERROR:
        DisplayStr("Parse Config File failed.", 1);
        break;
    case CHECK_CONFIG_DATA_ERROR:
        DisplayStr("Check Config Data failed.", 1);
        break;
    case WRITE_CONFIG_ERROR:
        DisplayStr("Write Config Data failed.", 1);
        break;
		// TODO: This could be simplified I think. 
		// TODO: Erase flash contents on upgrade error? 
		case INVALID_IV_ERROR:
				// TODO: Remove all insecure memory functions
				snprintf(str, sizeof(str), "Crypto Error: 0x%08X", (uint32_t)INVALID_IV_ERROR_CODE);
				DisplayStr(str, 1);
				//hal_check = eraseFlash();		
				break;
		case DECRYPTION_ERROR:
				snprintf(str, sizeof(str), "Crypto Error: 0x%08X", (uint32_t)DECRYPTION_ERROR_CODE);
				DisplayStr(str, 1);
				//hal_check = eraseFlash();		
				break;		
		case INVALID_TAG_ERROR:
				snprintf(str, sizeof(str), "Crypto Error: 0x%08X", (uint32_t)INVALID_TAG_ERROR_CODE);
				DisplayStr(str, 1);
				//hal_check = eraseFlash();		
				break;			
		case TAG_VALIDATION_ERROR:
				snprintf(str, sizeof(str), "Crypto Error: 0x%08X", (uint32_t)TAG_VALIDATION_ERROR_CODE);
				DisplayStr(str, 1);
				//hal_check = eraseFlash();
				break;			
		case FILE_VERIFY_ERROR:	
				snprintf(str, sizeof(str), "Crypto Error: 0x%08X", (uint32_t)FILE_VERIFY_ERROR_CODE);
				DisplayStr(str, 1);
				//hal_check = eraseFlash();		
				break;			
    default:
        break;
    }

		if(hal_check != HAL_OK)
		{
			//DisplayStr("Program App fail,Erase Addr Error.", 1);
		}
    
    DisplayStr("Programming failed, Please power on again.", 1);
    HAL_GPIO_WritePin(GPIOE, GPIO_PIN_6, GPIO_PIN_RESET);
  
    while (1)
    {
        HAL_GPIO_TogglePin(BJ_LED1_GPIO_Port, BJ_LED1_Pin);
        HAL_Delay(1000);
    }
}

static void checkIntegrity(void)
{
	// Standalone function to handle verifying the integrity of the application and screen data
//    char str[32];
    InitFont();
  
#if (LCD_TYPE == LCD_5_TFT)
    Init_SSD1963();
#elif (LCD_TYPE == LCD_35_TFT)    
    Init_9488();
#elif (LCD_TYPE == LCD_28_TFT)
    Init_9488();
#endif    
    
    // Check firmware validity before jumping
		eFileStatus_t fileStatus = FILE_UNSET;
		fileStatus = validate_flash_file();
    if (fileStatus != FILE_OK) 
		{
			LCD_ClrScr(CL_BLUE);
			SetLcdBrightness(5);			
			InAppFontBmpProgramFailed(FILE_VERIFY_ERROR);
		}
}

void JumpToApp(void)
{    
		// Check File Integrity First
		checkIntegrity(); 

    g_U32ETick = HAL_GetTick();
    g_U32AppStartTick = g_U32ETick - g_U32BTick;

    __set_FAULTMASK(1);

    SysTick->CTRL = 0;
    SysTick->LOAD = 0;
    SysTick->VAL = 0;    

    AppFun = (pFun) * (__IO uint32_t *)(FLASH_APP_ADDR + 4);
    unsigned int sU32Val = ((unsigned int)AppFun);

    if((sU32Val & 0x8000000) == 0)
    {
        while (1)
        {
            printf("Application not found.\r\n");
            HAL_Delay(1000);
        }
    }

    HAL_RCC_DeInit();
    
    // Disable NVIC interrupts
    for (unsigned char i = 0; i < 8; i++)
    {
        NVIC->ICER[i] = 0xFFFFFFFF;
        NVIC->ICPR[i] = 0xFFFFFFFF;
    }    
    SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;

    __set_MSP(*(__IO uint32_t *)FLASH_APP_ADDR);
    __set_CONTROL(0);

    (*AppFun)();
}

void InAppProgram(void)
{
    uint32_t sU32Address = FLASH_APP_ADDR;
    FRESULT result;
    char str[64];
    FIL fil;
    HAL_StatusTypeDef status;
    uint8_t encrypted_chunk[2048];
    uint8_t decrypted_chunk[2048];
    uint8_t iv[IV_SIZE];  
    uint8_t tag[TAG_SIZE]; 
    uint32_t bytesRead, decryptedSize;
    uint32_t totalBytesRead = 0;
    uint8_t first_chunk = 1;
    uint8_t sU8OldPrecent = 0xff;
    eFileStatus_t fileStatus = FILE_UNSET;
    uint8_t tagValidationResult = 0;

    // 1. 打开加密固件文件
    result = f_open(&fil, APP_FILE, FA_READ);
    if (result != FR_OK)
    { 
        InAppFontBmpProgramFailed(OPEN_APP_FILE_ERROR);
    }

    // 2. 获取文件大小并验证
    uint32_t fileSize = f_size(&fil);
    if (fileSize == 0 || fileSize < (IV_SIZE + TAG_SIZE + SIGNATURE_SIZE) || fileSize > (FILE_SIZE + SIGNATURE_SIZE + IV_SIZE + TAG_SIZE))
    {
        f_close(&fil);
        InAppFontBmpProgramFailed(ERASE_APP_ADDR_ERROR);
    }

    uint32_t encryptedDataSize = fileSize - IV_SIZE - TAG_SIZE; // 包括签名在内的加密数据大小
//    uint32_t firmwareSize = encryptedDataSize - SIGNATURE_SIZE; // 不包括签名的固件大小

    snprintf(str, sizeof(str), "File Size = %.1f KB", ((float)fileSize) / 1024);
    DisplayStr(str, 1);    

    // 3. 读取IV和TAG
   // DisplayStr("Verifying file integrity...", 1);
    
    // 读取IV
    result = f_read(&fil, iv, IV_SIZE, &bytesRead);
    if (result != FR_OK || bytesRead != IV_SIZE)
    {
        f_close(&fil);
        InAppFontBmpProgramFailed(INVALID_IV_ERROR);
    }
    
    // 读取文件末尾的TAG
    f_lseek(&fil, fileSize - TAG_SIZE);
    result = f_read(&fil, tag, TAG_SIZE, &bytesRead);
    if (result != FR_OK || bytesRead != TAG_SIZE)
    {
        f_close(&fil);
        InAppFontBmpProgramFailed(INVALID_TAG_ERROR);
    }
    
    // 4. 验证文件签名和完整性
   // DisplayStr("Verifying file signature...", 1);
    
    // 验证文件签名 - 新版validate_file_content会分块解密并验证签名
    f_lseek(&fil, 0);
    fileStatus = validate_file_content(&fil, fileSize);
    if (fileStatus != FILE_OK)
    {
        f_close(&fil);
        InAppFontBmpProgramFailed(FILE_VERIFY_ERROR);
    }
    
    // 5. 验证TAG（AES-GCM认证）
    tagValidationResult = validate_tag(AES_FW_KEY, tag);
    if (tagValidationResult != FILE_OK)
    {
        f_close(&fil);
        InAppFontBmpProgramFailed(TAG_VALIDATION_ERROR);
    }
    
   // DisplayStr("File verification succeeded", 1);
    
    // 6. 擦除Flash
    if (HAL_OK != eraseFlash())
    {
        f_close(&fil);
        InAppFontBmpProgramFailed(ERASE_APP_ADDR_ERROR);
    }
   // DisplayStr("Programming...", 1);
                
    g_U32BTick = HAL_GetTick();

    // 7. 重置文件指针和解密状态，准备写入Flash
    f_lseek(&fil, IV_SIZE);
    first_chunk = 1;
    totalBytesRead = 0;
    sU8OldPrecent = 0xff;

    // 8. 分块读取、解密和写入（包括固件和签名部分）
    while (totalBytesRead < encryptedDataSize)
    {
        uint16_t chunkSize = (encryptedDataSize - totalBytesRead) > sizeof(encrypted_chunk) ? 
                            sizeof(encrypted_chunk) : (encryptedDataSize - totalBytesRead);

        memset(encrypted_chunk, 0, sizeof(encrypted_chunk)); // 重置缓冲区
        result = f_read(&fil, encrypted_chunk, chunkSize, &bytesRead);
        if (result != FR_OK || bytesRead == 0)
        {
            f_close(&fil);
            InAppFontBmpProgramFailed(READ_APP_FILE_ERROR);
        }

        decryptedSize = decrypt_data_chunk(AES_FW_KEY, encrypted_chunk, decrypted_chunk, bytesRead, iv, first_chunk);

        if (decryptedSize == 0)
        {
            f_close(&fil);
            InAppFontBmpProgramFailed(DECRYPTION_ERROR);
        }

        first_chunk = 0; // 确保IV只设置一次
        
        // 写入解密后的数据到Flash
        for (uint32_t i = 0; i < decryptedSize; i += 4)
        {
            if (HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, sU32Address, *(uint32_t *)(decrypted_chunk + i)) == HAL_OK)
            {
                status = FLASH_WaitForLastOperation(50000);
                if (status == HAL_OK)
                {
                    uint32_t sU32ReadVal = *(uint32_t *)(sU32Address);
                    if (sU32ReadVal != *(uint32_t *)(decrypted_chunk + i))
                    {
                        f_close(&fil);
                        InAppFontBmpProgramFailed(CHECK_APP_DATA_ERROR);
                    }
                }
            }
            sU32Address += 4;
        }

        totalBytesRead += bytesRead; // 记录读取的字节数，正确跟踪文件位置
        
        // 显示进度
        if (sU8OldPrecent != totalBytesRead * 100 / encryptedDataSize)
        {
            HAL_GPIO_TogglePin(GPIOE, GPIO_PIN_6);
            sU8OldPrecent = totalBytesRead * 100 / encryptedDataSize;
            snprintf(str, sizeof(str), "Programming: %d%%", totalBytesRead * 100 / encryptedDataSize);
            DisplayStr(str, 0);
        }
    }

    f_close(&fil);
    
    // 9. 验证Flash内容
   // DisplayStr("Verifying flash content...", 1);
    fileStatus = validate_flash_file();
    if (fileStatus != FILE_OK)
    {
        InAppFontBmpProgramFailed(FILE_VERIFY_ERROR);
    }   

    g_U32ETick = HAL_GetTick();
    g_U32ProgramTick = g_U32ETick - g_U32BTick;

    HAL_FLASH_Lock();

    snprintf(str, sizeof(str), "Programming App Time = %.1f s", ((float)g_U32ProgramTick) / 1000);
    DisplayStr(str, 1);

    DisplayStr("Programming and Verification Completed!", 1);
}

static  uint8_t g_u8WBuf[4096];
static  uint8_t g_u8RBuf[4096];

#define TEST_FLASH  0

#if (TEST_FLASH == 1)
uint32_t sU32TotalReadTimes = 0;
uint32_t sU32ErrorTimes = 0;
#endif

EWRITEERRORTYPE InFontOrBmpLibProgram(void)
{  
    uint32_t sU32TotalWriteTimes = 0;
    uint8_t sU8WriteTimes = 0;
    uint8_t sU8ErrorTimes = 0;
    uint8_t sU8OldPrecent = 0xff;
    FRESULT result;
    FIL fil;  
    uint32_t sU32Addr = 0, sU32Idx = 0, sU32Len = 0, sU32WriteLen = 0, sU32TotalLen = 0;// u32Address,  
    char str[64];

#if (TEST_FLASH == 1)
    for (int i = 0; i < 4096; i++)
    {
        g_u8WBuf[i] = (i & 0xff);
    }
    sU32Addr = SPI_FLASH_FONT_BMP_ADDR;
    QSPI_WriteBuff(g_u8WBuf, sU32Addr, 4096);
    while (1)
    {
        //HAL_Delay(10);
        QSPI_ReadBuff(g_u8RBuf, sU32Addr, 4096);
        sU32TotalReadTimes++;
        for (sU32Idx = 0; sU32Idx < 4096; sU32Idx++)
        {
            if ((sU32Idx & 0xff) != g_u8RBuf[sU32Idx])
            {
                sU32ErrorTimes++;
                break;
//                sprintf(str, "Read Tatal Times = %d,Error Times=%d,Rate=%.1f%%", sU32TotalReadTimes, sU32ErrorTimes, (float)sU32ErrorTimes * 100 / sU32TotalReadTimes);
//                DisplayStr(str, 1);   
            }
        }        
    }
#else    
    
    g_U32BTick = HAL_GetTick();
    result = f_open(&fil, g_pI8FileName[1], FA_OPEN_EXISTING | FA_READ);
    
    if (result != FR_OK)
    {
        return OPEN_BMPFONT_FILE_ERROR;
    }    
    
    sU32TotalLen = f_size(&fil);
    snprintf(str, sizeof(str), "File Size = %1.f K", ((float)sU32TotalLen) / 1024);
    DisplayStr(str, 1);
  
    sU32Addr = SPI_FLASH_FONT_BMP_ADDR;
    while (1)
    {
        result = f_read(&fil, g_u8WBuf, 4096, &sU32Len);
        if (result != FR_OK)
        {
            InAppFontBmpProgramFailed(READ_BMPFONT_FILE_ERROR);      
        }
        
        sU32TotalWriteTimes++;
//        SPI_FLASH_SectorErase(sU32Addr);
        SPI_FLASH_BufferWrite(g_u8WBuf, sU32Addr, sU32Len);
        loop:        
        SPI_FLASH_BufferRead(g_u8RBuf, sU32Addr, 4096);
        for (sU32Idx = 0; sU32Idx < sU32Len; sU32Idx++) //之前出现写SPI Flash会出错的情况
        {
            if (g_u8RBuf[sU32Idx] != g_u8WBuf[sU32Idx])
            {
                sU8ErrorTimes++;
                if (++sU8WriteTimes < 10)
                {
                    g_U32ETick = HAL_GetTick();
                    goto loop;
                }
                snprintf(str, sizeof(str), "ID=0x%08x,TatT=%d,ErrT=%d,WLen=%d,Rate=%.1f%%", g_U32FlashID, sU32TotalWriteTimes, sU8ErrorTimes, sU32Len, (float)sU8ErrorTimes * 100 / sU32TotalWriteTimes);
                DisplayStr(str, 1);
                return CHECK_BMPFONT_DATA_ERROR;
            }
        }
        sU8WriteTimes = 0;
        sU32WriteLen += sU32Len;
        sU32Addr += 4096;
        if(sU32Len < 4096 || sU32WriteLen == sU32TotalLen)
        {            
            #if (LCD_TYPE == LCD_28_TFT)
             DisplayStr("Font And Bmp done and verify OK.", 1);
             #else
             DisplayStr("Programming Font And Bmp done and verify OK.", 1);
             #endif
            break;
        }
        if (sU8OldPrecent != sU32WriteLen * 100 / sU32TotalLen)
        {
            HAL_GPIO_TogglePin(GPIOE, GPIO_PIN_6);      //蓝灯闪烁             
            sU8OldPrecent = sU32WriteLen * 100 / sU32TotalLen;
            snprintf(str, sizeof(str), "Progress:%d%%", sU32WriteLen * 100 / sU32TotalLen);
            DisplayStr(str, 0);            
        }                 
    }
    g_U32ETick = HAL_GetTick();
    g_U32ProgramTick = g_U32ETick - g_U32BTick;
//    DisplayStr((type == 0) ? "Programming Font done and verify OK." : "Programming Bmp Lib done and verify OK.", 1);            
    //sprintf(str, "Programming %s Time = %.1f s", ((type == 0) ? "Font" : "Bmp Lib"), ((float)g_U32ProgramTick) / 1000); 
    #if (LCD_TYPE == LCD_28_TFT)
        snprintf(str, sizeof(str), "Font And Bmp Time = %.1f s", ((float)g_U32ProgramTick) / 1000);        
        DisplayStr(str, 1);    
        snprintf(str, sizeof(str), "Write Tatal Times = %d", sU32TotalWriteTimes);
        DisplayStr(str, 1);
        snprintf(str, sizeof(str), "Error Times=%d", sU8ErrorTimes);
        DisplayStr(str, 1);
        snprintf(str, sizeof(str), "Rate=%.1f%%", (float)sU8ErrorTimes * 100 / sU32TotalWriteTimes);
        DisplayStr(str, 1);
    #else
        snprintf(str, sizeof(str), "Programming Font And Bmp Time = %.1f s", ((float)g_U32ProgramTick) / 1000);        
        DisplayStr(str, 1);    
        snprintf(str, sizeof(str), "Write Tatal Times = %d,Error Times=%d,Rate=%.1f%%", sU32TotalWriteTimes, sU8ErrorTimes, (float)sU8ErrorTimes * 100 / sU32TotalWriteTimes);
        DisplayStr(str, 1);
    #endif    
#endif
    return NONE_WRITE_ERROR;
}

// HEX文件解析辅助函数
static uint8_t HexChar2Value(char c)
{
    if (c >= '0' && c <= '9')
        return c - '0';
    if (c >= 'A' && c <= 'F')
        return c - 'A' + 10;
    if (c >= 'a' && c <= 'f')
        return c - 'a' + 10;
    return 0;
}

static uint8_t HexStr2Value(const char *str)
{
    return (HexChar2Value(str[0]) << 4) | HexChar2Value(str[1]);
}

static uint16_t HexStr2Value16(const char *str)
{
    return (HexChar2Value(str[0]) << 12) | 
           (HexChar2Value(str[1]) << 8) | 
           (HexChar2Value(str[2]) << 4) | 
            HexChar2Value(str[3]);
}

static uint8_t CalculateChecksum(const char *line, uint8_t len)
{
    uint8_t sum = 0;
    uint8_t i;
    
    for (i = 0; i < len; i += 2) {
        sum += HexStr2Value(&line[i]);
    }
    
    return (uint8_t)(0x100 - sum);
}

EWRITEERRORTYPE InConfigProgram(void)
{
    FRESULT result;
    FIL fil;
    char line[256];
    uint32_t flashAddr = FLASH_CONFIG_ADDR;
//    char str[64];
    uint32_t configSize = 0;
    uint8_t data[128];
    uint8_t verifyData[128];
    HAL_StatusTypeDef status;
    FLASH_EraseInitTypeDef EraseInitStruct;
    uint32_t sectorError;
    uint8_t lineType, dataLen;
    uint16_t address;
    uint8_t checksum, calculatedChecksum;
    uint8_t i, j;
    
    // 打开配置文件
   // DisplayStr("Opening config file...", 1);
    result = f_open(&fil, CONFIG_FILE, FA_READ);
    if (result != FR_OK)
    {
        return OPEN_CONFIG_FILE_ERROR;
    }
    
    g_U32BTick = HAL_GetTick();
    
    // 擦除配置文件存储区
    //DisplayStr("Erasing config flash area...", 1);
    HAL_FLASH_Unlock();
    __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_OPERR | FLASH_FLAG_WRPERR | 
                          FLASH_FLAG_PGAERR | FLASH_FLAG_PGPERR | FLASH_FLAG_PGSERR);
    
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_SECTORS;
    EraseInitStruct.Sector = FLASH_SECTOR_11; //GetSector(FLASH_CONFIG_ADDR - 0x8000000);
    EraseInitStruct.NbSectors = 1;
    EraseInitStruct.VoltageRange = FLASH_VOLTAGE_RANGE_3;
    
    status = HAL_FLASHEx_Erase(&EraseInitStruct, &sectorError);
    if (status != HAL_OK)
    {
        HAL_FLASH_Lock();
        return WRITE_CONFIG_ERROR;
    }
    
  //  DisplayStr("Reading and writing config...", 1);
    
    // 解析HEX文件并写入Flash
    while (f_gets(line, sizeof(line), &fil))
    {
        // 检查HEX文件格式
        if (line[0] != ':')
        {
            continue;  // 跳过非HEX记录行
        }
        
        // 解析记录
        dataLen = HexStr2Value(&line[1]);
        address = HexStr2Value16(&line[3]);
        lineType = HexStr2Value(&line[7]);
        
        // 检查校验和
        calculatedChecksum = CalculateChecksum(&line[1], (dataLen * 2) + 8);
        checksum = HexStr2Value(&line[(dataLen * 2) + 9]);
        
        if (calculatedChecksum != checksum)
        {
            f_close(&fil);
            HAL_FLASH_Lock();
            return PARSE_CONFIG_FILE_ERROR;
        }
        
        // 处理数据记录
        if (lineType == 0)  // 数据记录
        {
            // 提取数据
            for (i = 0, j = 9; i < dataLen; i++, j += 2)
            {
                data[i] = HexStr2Value(&line[j]);
            }
            
            // 写入数据到Flash
            for (i = 0; i < dataLen; i += 4)
            {
                if (i + 4 <= dataLen)
                {
                    uint32_t word = (data[i+3] << 24) | (data[i+2] << 16) | (data[i+1] << 8) | data[i];
                    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, flashAddr + address + i, word);
                    if (status != HAL_OK)
                    {
                        f_close(&fil);
                        HAL_FLASH_Lock();
                        return WRITE_CONFIG_ERROR;
                    }
                    
                    FLASH_WaitForLastOperation(50000);
                    configSize += 4;
                }
                else
                {
                    // 处理不是4字节对齐的剩余数据
                    uint32_t word = 0xFFFFFFFF;
                    uint8_t bytesLeft = dataLen - i;
                    
                    for (uint8_t k = 0; k < bytesLeft; k++)
                    {
                        word = (word & ~(0xFF << (k * 8))) | (data[i+k] << (k * 8));
                    }
                    
                    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, flashAddr + address + i, word);
                    if (status != HAL_OK)
                    {
                        f_close(&fil);
                        HAL_FLASH_Lock();
                        return WRITE_CONFIG_ERROR;
                    }
                    
                    FLASH_WaitForLastOperation(50000);
                    configSize += bytesLeft;
                }
            }
            
            HAL_GPIO_TogglePin(GPIOE, GPIO_PIN_6); // 闪烁LED显示进度
        }
        else if (lineType == 1)  // 文件结束记录
        {
            break;
        }
    }
    
    f_close(&fil);
    
    // 验证写入的数据
  //  DisplayStr("Verifying config data...", 1);
    
    result = f_open(&fil, CONFIG_FILE, FA_READ);
    if (result != FR_OK)
    {
        HAL_FLASH_Lock();
        return OPEN_CONFIG_FILE_ERROR;
    }
    
    while (f_gets(line, sizeof(line), &fil))
    {
        if (line[0] != ':')
        {
            continue;
        }
        
        dataLen = HexStr2Value(&line[1]);
        address = HexStr2Value16(&line[3]);
        lineType = HexStr2Value(&line[7]);
        
        if (lineType == 0)
        {
            // 读取写入的数据进行验证
            memcpy(verifyData, (uint8_t*)(flashAddr + address), dataLen);
            
            // 提取HEX文件中的数据
            for (i = 0, j = 9; i < dataLen; i++, j += 2)
            {
                data[i] = HexStr2Value(&line[j]);
            }
            
            // 比较数据
            for (i = 0; i < dataLen; i++)
            {
                if (data[i] != verifyData[i])
                {
                    f_close(&fil);
                    HAL_FLASH_Lock();
                    return CHECK_CONFIG_DATA_ERROR;
                }
            }
        }
        else if (lineType == 1)
        {
            break;
        }
    }
    
    f_close(&fil);
    HAL_FLASH_Lock();
    
    g_U32ETick = HAL_GetTick();
    g_U32ProgramTick = g_U32ETick - g_U32BTick;
    
  //  snprintf(str, sizeof(str), "Config Size: %lu bytes", configSize);
  //  DisplayStr(str, 1);
    
   // snprintf(str, sizeof(str), "Config Time: %.1f s", ((float)g_U32ProgramTick) / 1000);
   // DisplayStr(str, 1);
    
    DisplayStr("Config programming successful!", 1);
    
    return NONE_WRITE_ERROR;
}

void IapModeProc(void)
{
    EWRITEERRORTYPE EWriteErrorType = NONE_WRITE_ERROR;
    char str[32];
    InitFont();
  
#if (LCD_TYPE == LCD_5_TFT)
    Init_SSD1963();
#elif (LCD_TYPE == LCD_35_TFT)    
    Init_9488();
#elif (LCD_TYPE == LCD_28_TFT)
    Init_9488();
#endif    
    
    LCD_ClrScr(CL_BLUE);
    SetLcdBrightness(5);
    
    snprintf(str, sizeof(str), "%s", "Bootloader running...");
    DisplayStr(str, 1);
    
    switch (g_UpdateFileFlag)
    {
        case 1:
            DisplayStr("Have found the App!", 1);             
            break;
        case 2:
            #if (LCD_TYPE == LCD_28_TFT)
            DisplayStr("Have found the Font And", 1);
            DisplayStr("BMP Lib!", 1);
            #else
            DisplayStr("Have found the Font And BMP Lib!", 1);
            #endif
            break;
        case 4:
            DisplayStr("Have found the Config file!", 1); 
            break;
        case 3:
            #if (LCD_TYPE == LCD_28_TFT)
            DisplayStr("Have found the App,Font And", 1);
            DisplayStr("Bmp Lib!", 1);;
            #else
            DisplayStr("Have found the App,Font And Bmp Lib!", 1);
            #endif
            break;
        case 5:
            DisplayStr("Have found the App,Config file!", 1); 
            break;
        case 6:
            DisplayStr("Have found the Font And BMP Lib,Config file!", 1); 
            break;
        case 7:
            #if (LCD_TYPE == LCD_28_TFT)
            DisplayStr("Have found the App,Font And", 1); 
            DisplayStr("BMP Lib,Config file!", 1); 
            #else
            DisplayStr("Have found the App,Font And BMP Lib,Config file!", 1); 
            #endif
            break;
        default:
            break;
    }
    //先烧写config配置文件，加密APP依赖配置文件解密。
    if (g_UpdateFileFlag & 0x04)
    {
       // DisplayStr("Install Config file...", 1); 
        EWriteErrorType = InConfigProgram();
        if (EWriteErrorType)
        {
            InAppFontBmpProgramFailed(EWriteErrorType);
        }
    }                   
    if (g_UpdateFileFlag & 0x01)
    {
        DisplayStr("Install App...", 1); 
        InAppProgram();
    }
    if (g_UpdateFileFlag & 0x02)
    {
        DisplayStr("Install Font And BMP Lib...", 1); 
        EWriteErrorType = InFontOrBmpLibProgram();
        if (EWriteErrorType)
        {
            InAppFontBmpProgramFailed(EWriteErrorType);
        }
    }


    DisplayStr("System resetting...", 1); 

    HAL_Delay(3000);
    
	DISABLE_INT(); 

    HAL_RCC_DeInit();
   
    SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;
    __set_FAULTMASK(1);
    
	/* 使能全局中断 */
	ENABLE_INT();   
    
    __set_CONTROL(0);

    NVIC_SystemReset(); //软复位，以便重新进行程序引导
}

void CheckUpdate(void)
{
    FATFS fs;
    FIL fil;
    FRESULT result;
    uint8_t sU8State = 0, sU8Idx, i;
    uint8_t sU8AntiShakeFlag = 0;
    uint32_t sU32Tick = HAL_GetTick();
    
    //printf("bootloader7\r\n");

    /* 判断飞梭按键是否按和SD卡是否插上 */
    if (HAL_GPIO_ReadPin(ENTER_KEY_GPIO_Port, ENTER_KEY_Pin) == 0)
    {
        while (HAL_GPIO_ReadPin(ENTER_KEY_GPIO_Port, ENTER_KEY_Pin) == 0 && HAL_GPIO_ReadPin(SD_CD_GPIO_Port, SD_CD_Pin) == 0) 
        {
            if ((HAL_GetTick() - sU32Tick) > 10)
            {
                sU8AntiShakeFlag = 1;
                break;
            }
        }
        //printf("bootloader8 %d\r\n", sU8AntiShakeFlag);

        if (sU8AntiShakeFlag)
        {
            //挂载SD卡
            for (i = 0; i < 3; i++)     //最多挂载3次，失败则退出
            {
                result = f_mount(&fs, "0:", 1);
                if (result == FR_OK)
                {
                    break;
                }
            }            
            if (result != FR_OK)
            {        
                f_mount(NULL, USERPath, 0);                   
            }    
            else
            {
                for (sU8Idx = 0; sU8Idx < 3; sU8Idx++)
                {
                    result = f_open(&fil, g_pI8FileName[sU8Idx], FA_READ);
                    if (result == FR_OK)
                    {
                        g_UpdateFileFlag |= (1 << sU8Idx);         //bit 0:App 1:Font 2:Config
                    }
                    f_close(&fil);
                }
                
                if (g_UpdateFileFlag == 0)
                {                        
                    f_mount(NULL, USERPath, 0);
                }     
                else 
                {                
                    sU8State = 3; //按键按下，且SD卡存在升级文件，进入在线更新模式
                }
            }
        }
    }       
    //printf("bootloader9\r\n");

    switch (sU8State)
    {
//        case 2: //工程模式
//            //EngineerModuleProc();
//            break;
        case 3: //在线更新模式
            IapModeProc();
            break;
        case 0: //正常启动模式
        default:
            JumpToApp();
            break;
    }
}
