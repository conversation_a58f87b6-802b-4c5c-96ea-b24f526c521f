/*********************************************************************
*                                                                    *
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
*                                                                    *
**********************************************************************
*                                                                    *
* C-file generated by:                                               *
*                                                                    *
*        GUI_Builder for emWin version 5.32                          *
*        Compiled Oct  8 2015, 11:59:02                              *
*        (c) 2015 Segger Microcontroller GmbH & Co. KG               *
*                                                                    *
**********************************************************************
*                                                                    *
*        Internet: www.segger.com  Support: <EMAIL>       *
*                                                                    *
**********************************************************************
*/

// USER START (Optionally insert additional includes)
#include "StaterBar.h"
#include "Key.h"
#include "GlobalVariable.h"
#include "WifiSelectedScreen.h"
#include "WifiUartModule.h"
#include "ConfigSave.h"
#include "AppBMP.h"
#include "stack.h"
#include "WifiSelectedScreen.h"
// USER END

#include "DIALOG.h"

/*********************************************************************
*
*       Defines
*
**********************************************************************
*/

// USER START (Optionally insert additional defines)
static void UpdateWifiKeyBoard(WM_HWIN hItem, const char* Str_KeyBordValue[]);
static void UpdatePassword(uint8_t ID);
static void RepaintWifiSSIDSettingDialog(void);

static uint8_t g_sU8IsLowerCaseLetters = FALSE;
static uint8_t g_sU8IsNumKeyBoard = FALSE;
static char acPassWord[MAX_WIFI_PWD_LEN + 1];
static char acPassWordNew[MAX_WIFI_PWD_LEN + 1];
static uint8_t g_sU8InputSymbolCnt = 0;
static uint8_t g_sU8PasswordRefresh = 0;

int8_t i8Focus = 0;
uint8_t KeyBoardFocus = 0;
uint8_t LastKeyBoardFocus = 0;

static void _cbDialog(U8 Key);

WINDOWINFO g_WifiSSIDSettingScreenWInfo = {NULL, _cbDialog, NULL, RepaintWifiSSIDSettingDialog, NULL, NULL, 0, 0, SSIDSETTING_ID};

static GUI_TIMER_HANDLE g_RefreshPassWordValueTimer = NULL;

static const char* Str_KeyBordLetter[4][32] = {{"q", "w", "e", "r", "t", "y", "u", "i", "o", "p",
                                                "a", "s", "d", "f", "g", "h", "j", "k", "l",
                                                "  ", "z", "x", "c", "v", "b", "n", "m", "  ",
                                                "?123", " ", " "},
                                                {"Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P",
                                                "A", "S", "D", "F", "G", "H", "J", "K", "L",
                                                "  ", "Z", "X", "C", "V", "B", "N", "M", " ",
                                                "?123", " ", " "},
                                                {"1", "2", "3", "4", "5", "6", "7", "8", "9", "0",
                                                "~", "!", "@", "#", "%", "^", "&", "*", "?",
                                                "Shift", "(", ")", "-", "_", ":", ";", "/", " ",
                                                "ABC", " ", " "},
                                                {"\"", "$", "'", "+", ",", ".", "<", ">", "[", "]",
                                                "\\", "`", "{", "|", "}", " ", " ", " ", " ",
                                                "Shift", " ", " ", " ", " ", " ", " ", " ", " ",
                                                "ABC", " ", " "}};


//                                                {""", "$", "'", "+", ",", ".", "<", ">", "[", "]",
//                                                "\", "`", "{", "|", "}", " ", " ", " ", " ",
//                                                "Shift", "(", ")", "-", "_", ":", ";", "/", " ",
//                                                "ABC", " ", " "}};

#if (LCD_TYPE == LCD_5_TFT)
uint16_t PassWordRect[4] = {65, 24+STATEBAR_TITLE_HEIGHT, 160+65, 33+STATEBAR_TITLE_HEIGHT+24};
uint16_t PassWordx0 = 226 ;
uint16_t PassWordy0 = 24+STATEBAR_TITLE_HEIGHT;
uint16_t PassWordx1 = 226+491;
uint16_t PassWordy1 = 24+33+STATEBAR_TITLE_HEIGHT;
#else
    #if (LCD_TYPE == LCD_28_TFT)
      uint16_t PassWordRect[4] = {5, 16+STATEBAR_TITLE_HEIGHT, 80+5, 22+STATEBAR_TITLE_HEIGHT+16};
      uint16_t PassWordx0 = 90;
      uint16_t PassWordy0 = 16+STATEBAR_TITLE_HEIGHT;
      uint16_t PassWordx1 = 90+150;
      uint16_t PassWordy1 = 16+22+STATEBAR_TITLE_HEIGHT;
    #else
      uint16_t PassWordRect[4] = {30, 16+STATEBAR_TITLE_HEIGHT, 106+30, 22+STATEBAR_TITLE_HEIGHT+16};
      uint16_t  PassWordx0 = 135 ;
      uint16_t  PassWordy0 = 16+STATEBAR_TITLE_HEIGHT;
      uint16_t  PassWordx1 = 135+294;
      uint16_t  PassWordy1 = 16+22+STATEBAR_TITLE_HEIGHT;
    #endif
#endif

#if (LCD_TYPE == LCD_5_TFT)
static const uint16_t Axis_KeyBordLetter[][4] ={{ 70, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 137, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 204, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 271, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 338, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 405, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 472, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 539, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 606, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 673, 75+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 84, 142+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 155, 142+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 226, 142+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 297, 142+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 368, 142+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 439, 142+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 510, 142+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 581, 142+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 652, 142+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 71, 208+STATEBAR_TITLE_HEIGHT, 70, 55},
      { 155, 208+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 226, 208+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 297, 208+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 368, 208+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 439, 208+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 510, 208+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 581, 208+STATEBAR_TITLE_HEIGHT, 57, 55},
      { 652, 208+STATEBAR_TITLE_HEIGHT, 70, 55},
      { 117, 275+STATEBAR_TITLE_HEIGHT, 93, 55},
      { 224, 275+STATEBAR_TITLE_HEIGHT, 286, 55},
      { 524, 275+STATEBAR_TITLE_HEIGHT, 155, 55},
      { 622, 408, 167, 66}
  };
#else
	#if (LCD_TYPE == LCD_28_TFT)
static const uint16_t Axis_KeyBordLetter[][4] ={{ 6, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 29, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 52, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 75, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 98, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 121, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 144, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 167, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 190, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 213, 50+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 16, 94+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 39, 94+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 62, 94+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 85, 94+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 108, 94+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 131, 94+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 154, 94+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 177, 94+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 200, 94+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 6, 138+STATEBAR_TITLE_HEIGHT, 30, 36},
        { 39, 138+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 62, 138+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 85, 138+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 108, 138+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 131, 138+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 154, 138+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 177, 138+STATEBAR_TITLE_HEIGHT, 20, 36},
        { 200, 138+STATEBAR_TITLE_HEIGHT, 30, 36},
        { 25, 183+STATEBAR_TITLE_HEIGHT, 50, 36},
        { 80, 183+STATEBAR_TITLE_HEIGHT, 100, 36},
        { 185, 183+STATEBAR_TITLE_HEIGHT, 48, 36},
        { 145, 224+STATEBAR_TITLE_HEIGHT, 88, 43}
    };        
    #else                                            
static const uint16_t Axis_KeyBordLetter[][4] ={{ 42, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 82, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 122, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 162, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 202, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 242, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 282, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 322, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 362, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 402, 50+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 50, 94+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 93, 94+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 136, 94+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 179, 94+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 222, 94+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 265, 94+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 308, 94+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 351, 94+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 394, 94+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 42, 138+STATEBAR_TITLE_HEIGHT, 42, 36},
        { 93, 138+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 136, 138+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 179, 138+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 222, 138+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 265, 138+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 308, 138+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 351, 138+STATEBAR_TITLE_HEIGHT, 34, 36},
        { 394, 138+STATEBAR_TITLE_HEIGHT, 42, 36},
        { 72, 183+STATEBAR_TITLE_HEIGHT, 55, 36},
        { 136, 183+STATEBAR_TITLE_HEIGHT, 171, 36},
        { 316, 183+STATEBAR_TITLE_HEIGHT, 93, 36},
        { 376, 224+STATEBAR_TITLE_HEIGHT, 96, 43}
    };        
    #endif
#endif
// USER END

/*******************************************************************************
*   函数名: DispKeyboard_ABC
*   参  数: 类别 0 小写 1 大写 2 数字
*   返  回: 无
*   功  能: 键盘显示字母
*/    
void DispKeyboard_ABC(uint8_t KBFocus,uint8_t Focus)
{
    uint8_t i;
    U16 sU16WifiBmpIdx = 0;
    GUI_RECT rect;
    for(i=0;i<32;i++)
    {
        if(i == Focus)
        {
            GUI_SetColor(WIFISSID_BTN_SELECTED_BACKCOLOR);
            GUI_FillRect(Axis_KeyBordLetter[i][0],Axis_KeyBordLetter[i][1],Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2],Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
        }
        else
        {
            GUI_SetColor(WIFISSID_BTN_BACKCOLOR);
            GUI_FillRect(Axis_KeyBordLetter[i][0],Axis_KeyBordLetter[i][1],Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2],Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
        }
            
        rect.x0 = Axis_KeyBordLetter[i][0];
        rect.y0 = Axis_KeyBordLetter[i][1];
        rect.x1 = Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2];
        rect.y1 = Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3];
        if(i == 31)
        {
            #if (LCD_TYPE == LCD_5_TFT)
                rect.x0 = Axis_KeyBordLetter[i][0] + 66 ;
            #else
                #if (LCD_TYPE == LCD_28_TFT)
                rect.x0 = Axis_KeyBordLetter[i][0] + 40 ;
                #else
                rect.x0 = Axis_KeyBordLetter[i][0] + 40 ;
                #endif
            #endif
            if(i == Focus)
            {
                 GUI_SetColor(BACKCOLOR_DEFAULT);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_FillRect(Axis_KeyBordLetter[i][0] - 66, Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #else
                    GUI_FillRect(Axis_KeyBordLetter[i][0] - 40, Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #endif
                
                #if (LCD_TYPE == LCD_28_TFT)
                DrawBMP(BMP_WIFIBACK_SELECTED, Axis_KeyBordLetter[i][0],Axis_KeyBordLetter[i][1]);  
                GUI_SetFont(FONT_18);
                GUI_SetColor(GUI_WHITE);
                GUI_DispStringInRect(GetMultiLanguageString(BACK_IDX), &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
                //画"返回"背景
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
                #else
                WifiScreen_DrawExitIcon(1);
                #endif
            }
            else
            {
                GUI_SetColor(BACKCOLOR_DEFAULT);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_FillRect(Axis_KeyBordLetter[i][0] - 66, Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #else
                    GUI_FillRect(Axis_KeyBordLetter[i][0] - 40, Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #endif
                       
                #if (LCD_TYPE == LCD_28_TFT)
                DrawBMP(BMP_WIFIBACK_DEFAULT, Axis_KeyBordLetter[i][0],Axis_KeyBordLetter[i][1]);  
                GUI_SetFont(FONT_18);
                GUI_SetColor(GUI_WHITE);
                GUI_DispStringInRect(GetMultiLanguageString(BACK_IDX), &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
                //画"返回"背景
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
                #else
                WifiScreen_DrawExitIcon(0);
                #endif
            }
        }
        else if(i == 30)
        {
            if(i == Focus)
            {
                GUI_SetColor(WIFISSID_BTN_SELECTED_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                        GUI_SetFont(FONT_18);
                    #else
                        GUI_SetFont(FONT_24);
                    #endif
                #endif
                GUI_SetColor(GUI_WHITE);
                sU16WifiBmpIdx = BMP_WIFIENTER2;
            }
            else
            {
                GUI_SetColor(WIFISSID_BTN_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                        GUI_SetFont(FONT_18);
                    #else
                        GUI_SetFont(FONT_24);
                    #endif
                #endif
                GUI_SetColor(GUI_WHITE);
                sU16WifiBmpIdx = BMP_WIFIENTER1;
            }
            
            #if (LCD_TYPE == LCD_5_TFT)
                DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[i][0]+57,Axis_KeyBordLetter[i][1]+10);
            #else
                #if (LCD_TYPE == LCD_28_TFT)
                DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[i][0]+14,Axis_KeyBordLetter[i][1]+7);
                #else
                DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[i][0]+34,Axis_KeyBordLetter[i][1]+7);
                #endif
            #endif
        }
        else if(i == 29)
        {
            if(i == Focus)
            {
                GUI_SetColor(WIFISSID_BTN_SELECTED_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                GUI_SetColor(GUI_WHITE);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+97, Axis_KeyBordLetter[i][1]+22, 97 + 4 - 1, 22 + 12 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+97, Axis_KeyBordLetter[i][1]+31, 97 + 92 - 1, 31 + 3 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+185, Axis_KeyBordLetter[i][1]+22, 185 + 4 - 1, 22 + 12 - 1);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                    GUI_SetFont(FONT_18);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+18, Axis_KeyBordLetter[i][1]+14, 18 + 3 - 1, 14 + 9 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+18, Axis_KeyBordLetter[i][1]+20, 18 + 55 - 1, 20 + 2 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+71, Axis_KeyBordLetter[i][1]+14, 71 + 3 - 1, 14 + 9 - 1);
                    #else
                    GUI_SetFont(FONT_24);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+58, Axis_KeyBordLetter[i][1]+14, 58 + 3 - 1, 14 + 9 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+58, Axis_KeyBordLetter[i][1]+20, 58 + 55 - 1, 20 + 2 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+111, Axis_KeyBordLetter[i][1]+14, 111 + 3 - 1, 14 + 9 - 1);
                    #endif
                #endif
            }
            else
            {
                GUI_SetColor(WIFISSID_BTN_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                GUI_SetColor(GUI_WHITE);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+97, Axis_KeyBordLetter[i][1]+22, 97 + 4 - 1, 22 + 12 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+97, Axis_KeyBordLetter[i][1]+31, 97 + 92 - 1, 31 + 3 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+185, Axis_KeyBordLetter[i][1]+22, 185 + 4 - 1, 22 + 12 - 1);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                    GUI_SetFont(FONT_18);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+18, Axis_KeyBordLetter[i][1]+14, 18 + 3 - 1, 14 + 9 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+18, Axis_KeyBordLetter[i][1]+20, 18 + 55 - 1, 20 + 2 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+71, Axis_KeyBordLetter[i][1]+14, 71 + 3 - 1, 14 + 9 - 1);
                    #else
                    GUI_SetFont(FONT_24);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+58, Axis_KeyBordLetter[i][1]+14, 58 + 3 - 1, 14 + 9 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+58, Axis_KeyBordLetter[i][1]+20, 58 + 55 - 1, 20 + 2 - 1);
                    GUI_FillRect(Axis_KeyBordLetter[i][0]+111, Axis_KeyBordLetter[i][1]+14, 111 + 3 - 1, 14 + 9 - 1);
                    #endif
                #endif
            }
        }
        else if(i == 27)
        {
            if(i == Focus)
            {
                GUI_SetColor(WIFISSID_BTN_SELECTED_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                        GUI_SetFont(FONT_18);
                    #else
                        GUI_SetFont(FONT_24);
                    #endif
                #endif
                GUI_SetColor(GUI_WHITE);
                sU16WifiBmpIdx = BMP_WIFIDELETE2;
            }
            else
            {
                GUI_SetColor(WIFISSID_BTN_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                        GUI_SetFont(FONT_18);
                    #else
                        GUI_SetFont(FONT_24);
                    #endif
                #endif
                GUI_SetColor(GUI_WHITE);
                sU16WifiBmpIdx = BMP_WIFIDELETE1;
            }
            #if (LCD_TYPE == LCD_5_TFT)
                DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[i][0]+15,Axis_KeyBordLetter[i][1]+10);
            #else
                #if (LCD_TYPE == LCD_28_TFT)
                DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[i][0]+3,Axis_KeyBordLetter[i][1]+6);
                #else
                DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[i][0]+9,Axis_KeyBordLetter[i][1]+6);
                #endif
            #endif
        }
        else if(i == 19)
        {
            if(i == Focus)
            {
                GUI_SetColor(WIFISSID_BTN_SELECTED_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                        GUI_SetFont(FONT_18);
                    #else
                        GUI_SetFont(FONT_24);
                    #endif
                #endif
                GUI_SetColor(GUI_WHITE);
                sU16WifiBmpIdx = BMP_WIFICASESWITCH2;
            }
            else
            {
                GUI_SetColor(WIFISSID_BTN_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                        GUI_SetFont(FONT_18);
                    #else
                        GUI_SetFont(FONT_24);
                    #endif
                #endif
                GUI_SetColor(GUI_WHITE);
                sU16WifiBmpIdx = BMP_WIFICASESWITCH1;
            }
            #if (LCD_TYPE == LCD_5_TFT)
                DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[i][0]+15,Axis_KeyBordLetter[i][1]+10);
            #else
                #if (LCD_TYPE == LCD_28_TFT)
                DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[i][0]+3,Axis_KeyBordLetter[i][1]+6);
                #else
                DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[i][0]+9,Axis_KeyBordLetter[i][1]+6);
                #endif
            #endif
        }
        else
        {
            if(i == Focus)
            {
                GUI_SetColor(WIFISSID_BTN_SELECTED_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                        GUI_SetFont(FONT_18);
                    #else
                        GUI_SetFont(FONT_24);
                    #endif
                #endif
                GUI_SetColor(GUI_WHITE);
                GUI_DispStringInRect(Str_KeyBordLetter[KeyBoardFocus][i], &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
            }        
            else
            {
                GUI_SetColor(WIFISSID_BTN_BACKCOLOR);
                GUI_FillRect(Axis_KeyBordLetter[i][0], Axis_KeyBordLetter[i][1], Axis_KeyBordLetter[i][0]+Axis_KeyBordLetter[i][2], Axis_KeyBordLetter[i][1]+Axis_KeyBordLetter[i][3]);
                #if (LCD_TYPE == LCD_5_TFT)
                    GUI_SetFont(FONT_32);
                #else
                    #if (LCD_TYPE == LCD_28_TFT)
                        GUI_SetFont(FONT_18);
                    #else
                        GUI_SetFont(FONT_24);
                    #endif
                #endif
                GUI_SetColor(GUI_WHITE);
                GUI_DispStringInRect(Str_KeyBordLetter[KeyBoardFocus][i], &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);            
            }
        }
    }
}



//键盘刷新
void DispKeyboard_Spin(uint8_t LastFocus,uint8_t Focus,uint8_t flag)
{
    U16 sU16WifiBmpIdx = 0;
    GUI_RECT rect;  
    GUI_SetColor(WIFISSID_BTN_SELECTED_BACKCOLOR);
    if (Focus != 31)
    {
        GUI_FillRect(Axis_KeyBordLetter[Focus][0],Axis_KeyBordLetter[Focus][1],Axis_KeyBordLetter[Focus][0]+Axis_KeyBordLetter[Focus][2],Axis_KeyBordLetter[Focus][1]+Axis_KeyBordLetter[Focus][3]);
    }
    
    rect.x0 = Axis_KeyBordLetter[Focus][0];
    rect.y0 = Axis_KeyBordLetter[Focus][1];
    rect.x1 = Axis_KeyBordLetter[Focus][0]+Axis_KeyBordLetter[Focus][2];
    rect.y1 = Axis_KeyBordLetter[Focus][1]+Axis_KeyBordLetter[Focus][3];
    if(Focus == 31)
    {
        #if (LCD_TYPE == LCD_28_TFT)
        rect.x0 = Axis_KeyBordLetter[Focus][0]+40;
        DrawBMP(BMP_WIFIBACK_SELECTED, Axis_KeyBordLetter[Focus][0],Axis_KeyBordLetter[Focus][1]);  
        GUI_SetFont(FONT_18);
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(GetMultiLanguageString(BACK_IDX), &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
        //画"返回"背景
        GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
        #else
        WifiScreen_DrawExitIcon(1);
        #endif
    }
    else if(Focus == 30)
    {
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
                GUI_SetFont(FONT_18);
            #else
                GUI_SetFont(FONT_24);
            #endif
        #endif
        GUI_SetColor(GUI_WHITE);
        sU16WifiBmpIdx = BMP_WIFIENTER2;
        
        #if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[Focus][0]+57,Axis_KeyBordLetter[Focus][1]+10);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[Focus][0]+14,Axis_KeyBordLetter[Focus][1]+7);
            #else
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[Focus][0]+34,Axis_KeyBordLetter[Focus][1]+7);
            #endif
        #endif
    }
    else if(Focus == 29)
    {
        GUI_SetColor(GUI_WHITE);
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
            GUI_FillRect(Axis_KeyBordLetter[Focus][0]+97, Axis_KeyBordLetter[Focus][1]+22, 97 + 4 - 1, 22 + 12 - 1);
            GUI_FillRect(Axis_KeyBordLetter[Focus][0]+97, Axis_KeyBordLetter[Focus][1]+31, 97 + 92 - 1, 31 + 3 - 1);
            GUI_FillRect(Axis_KeyBordLetter[Focus][0]+185, Axis_KeyBordLetter[Focus][1]+22, 185 + 4 - 1, 22 + 12 - 1);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
            GUI_SetFont(FONT_18);
            GUI_FillRect(Axis_KeyBordLetter[Focus][0]+18, Axis_KeyBordLetter[Focus][1]+14, 18 + 3 - 1, 14 + 9 - 1);
            GUI_FillRect(Axis_KeyBordLetter[Focus][0]+18, Axis_KeyBordLetter[Focus][1]+20, 18 + 55 - 1, 20 + 2 - 1);
            GUI_FillRect(Axis_KeyBordLetter[Focus][0]+71, Axis_KeyBordLetter[Focus][1]+14, 71 + 3 - 1, 14 + 9 - 1);
            #else
            GUI_SetFont(FONT_24);
            GUI_FillRect(Axis_KeyBordLetter[Focus][0]+58, Axis_KeyBordLetter[Focus][1]+14, 58 + 3 - 1, 14 + 9 - 1);
            GUI_FillRect(Axis_KeyBordLetter[Focus][0]+58, Axis_KeyBordLetter[Focus][1]+20, 58 + 55 - 1, 20 + 2 - 1);
            GUI_FillRect(Axis_KeyBordLetter[Focus][0]+111, Axis_KeyBordLetter[Focus][1]+14, 111 + 3 - 1, 14 + 9 - 1);
            #endif
        #endif
    }
    else if(Focus == 27)
    {
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
                GUI_SetFont(FONT_18);
            #else
                GUI_SetFont(FONT_24);
            #endif
        #endif
        GUI_SetColor(GUI_WHITE);
        sU16WifiBmpIdx = BMP_WIFIDELETE2;
        
        #if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[Focus][0]+15,Axis_KeyBordLetter[Focus][1]+10);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[Focus][0]+3,Axis_KeyBordLetter[Focus][1]+6);
            #else
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[Focus][0]+9,Axis_KeyBordLetter[Focus][1]+6);
            #endif
        #endif
    }
    else if(Focus == 19)
    {
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
                GUI_SetFont(FONT_18);
            #else
                GUI_SetFont(FONT_24);
            #endif
        #endif
        
        GUI_SetColor(GUI_WHITE);
        sU16WifiBmpIdx = BMP_WIFICASESWITCH2;
            
        #if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[Focus][0]+15,Axis_KeyBordLetter[Focus][1]+10);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[Focus][0]+3,Axis_KeyBordLetter[Focus][1]+6);
            #else
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[Focus][0]+9,Axis_KeyBordLetter[Focus][1]+6);
            #endif
        #endif
    }
    else
    {
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
                GUI_SetFont(FONT_18);
            #else
                GUI_SetFont(FONT_24);
            #endif
        #endif
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(Str_KeyBordLetter[KeyBoardFocus][Focus], &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
    }    
    
    
    GUI_SetColor(WIFISSID_BTN_BACKCOLOR);
    if (LastFocus != 31)
    {
        GUI_FillRect(Axis_KeyBordLetter[LastFocus][0],Axis_KeyBordLetter[LastFocus][1],Axis_KeyBordLetter[LastFocus][0]+Axis_KeyBordLetter[LastFocus][2],Axis_KeyBordLetter[LastFocus][1]+Axis_KeyBordLetter[LastFocus][3]);
    }
    
    rect.x0 = Axis_KeyBordLetter[LastFocus][0];
    rect.y0 = Axis_KeyBordLetter[LastFocus][1];
    rect.x1 = Axis_KeyBordLetter[LastFocus][0]+Axis_KeyBordLetter[LastFocus][2];
    rect.y1 = Axis_KeyBordLetter[LastFocus][1]+Axis_KeyBordLetter[LastFocus][3];
    if(LastFocus == 31)
    {
        #if (LCD_TYPE == LCD_28_TFT)
        rect.x0 = Axis_KeyBordLetter[LastFocus][0]+40;
        DrawBMP(BMP_WIFIBACK_DEFAULT, Axis_KeyBordLetter[LastFocus][0],Axis_KeyBordLetter[LastFocus][1]);  
        GUI_SetFont(FONT_18);
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(GetMultiLanguageString(BACK_IDX), &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
        //画"返回"背景
        GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
        #else
        WifiScreen_DrawExitIcon(0);
        #endif
    }
    else if(LastFocus == 30)
    {
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
                GUI_SetFont(FONT_18);
            #else
                GUI_SetFont(FONT_24);
            #endif
        #endif
        GUI_SetColor(GUI_WHITE);
        sU16WifiBmpIdx = BMP_WIFIENTER1;
    
        #if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[LastFocus][0]+57,Axis_KeyBordLetter[LastFocus][1]+10);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[LastFocus][0]+14,Axis_KeyBordLetter[LastFocus][1]+7);
            #else
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[LastFocus][0]+34,Axis_KeyBordLetter[LastFocus][1]+7);
            #endif
        #endif
    }
    else if(LastFocus == 29)
    {
        GUI_SetColor(GUI_WHITE);
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
            GUI_FillRect(Axis_KeyBordLetter[LastFocus][0]+97, Axis_KeyBordLetter[LastFocus][1]+22, 97 + 4 - 1, 22 + 12 - 1);
            GUI_FillRect(Axis_KeyBordLetter[LastFocus][0]+97, Axis_KeyBordLetter[LastFocus][1]+31, 97 + 92 - 1, 31 + 3 - 1);
            GUI_FillRect(Axis_KeyBordLetter[LastFocus][0]+185, Axis_KeyBordLetter[LastFocus][1]+22, 185 + 4 - 1, 22 + 12 - 1);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
            GUI_SetFont(FONT_18);
            GUI_FillRect(Axis_KeyBordLetter[LastFocus][0]+18, Axis_KeyBordLetter[LastFocus][1]+14, 18 + 3 - 1, 14 + 9 - 1);
            GUI_FillRect(Axis_KeyBordLetter[LastFocus][0]+18, Axis_KeyBordLetter[LastFocus][1]+20, 18 + 55 - 1, 20 + 2 - 1);
            GUI_FillRect(Axis_KeyBordLetter[LastFocus][0]+71, Axis_KeyBordLetter[LastFocus][1]+14, 71 + 3 - 1, 14 + 9 - 1);
            #else
            GUI_SetFont(FONT_24);
            GUI_FillRect(Axis_KeyBordLetter[LastFocus][0]+58, Axis_KeyBordLetter[LastFocus][1]+14, 58 + 3 - 1, 14 + 9 - 1);
            GUI_FillRect(Axis_KeyBordLetter[LastFocus][0]+58, Axis_KeyBordLetter[LastFocus][1]+20, 58 + 55 - 1, 20 + 2 - 1);
            GUI_FillRect(Axis_KeyBordLetter[LastFocus][0]+111, Axis_KeyBordLetter[LastFocus][1]+14, 111 + 3 - 1, 14 + 9 - 1);
            #endif
        #endif
    }
    else if(LastFocus == 27)
    {
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
                GUI_SetFont(FONT_18);
            #else
                GUI_SetFont(FONT_24);
            #endif
        #endif
        GUI_SetColor(GUI_WHITE);
        sU16WifiBmpIdx = BMP_WIFIDELETE1;
        
        #if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[LastFocus][0]+15,Axis_KeyBordLetter[LastFocus][1]+10);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[LastFocus][0]+3,Axis_KeyBordLetter[LastFocus][1]+6);
            #else
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[LastFocus][0]+9,Axis_KeyBordLetter[LastFocus][1]+6);
            #endif
        #endif
    }
    else if(LastFocus == 19)
    {
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
                GUI_SetFont(FONT_18);
            #else
                GUI_SetFont(FONT_24);
            #endif
        #endif
        GUI_SetColor(GUI_WHITE);
        sU16WifiBmpIdx = BMP_WIFICASESWITCH1;
        
        #if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[LastFocus][0]+15,Axis_KeyBordLetter[LastFocus][1]+10);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[LastFocus][0]+3,Axis_KeyBordLetter[LastFocus][1]+6);
            #else
            DrawBMP(sU16WifiBmpIdx, Axis_KeyBordLetter[LastFocus][0]+9,Axis_KeyBordLetter[LastFocus][1]+6);
            #endif
        #endif
    }
    else
    {
        #if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
        #else
            #if (LCD_TYPE == LCD_28_TFT)
                GUI_SetFont(FONT_18);
            #else
                GUI_SetFont(FONT_24);
            #endif
        #endif
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRect(Str_KeyBordLetter[KeyBoardFocus][LastFocus], &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);    
    }
}
/*********************************************************************
*
*       Static data
*
**********************************************************************
*/

static void RefreshPassWordValue(void)
{
    const char* s = "*";
    uint8_t i = 0;
    char buf[MAX_WIFI_PWD_LEN + 1] = {0};
    GUI_RECT rect;
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
    #else
        #if (LCD_TYPE == LCD_28_TFT)
            GUI_SetFont(FONT_18);
        #else
            GUI_SetFont(FONT_24);
        #endif
    #endif
    GUI_TIMER_Restart(g_RefreshPassWordValueTimer);
 
    if (g_sU8InputSymbolCnt == 0 || g_sU8PasswordRefresh == 0)
    {
        return;
    }
    g_sU8PasswordRefresh = 0;
    
    rect.x0 = PassWordx0;
    rect.y0 = PassWordy0;
    rect.x1 = PassWordx1;
    rect.y1 = PassWordy1;
    
    memset(acPassWordNew, 0, sizeof(acPassWordNew)/sizeof(char)); 
    for (i = 0; i < g_sU8InputSymbolCnt; i++)
    {
        strcat(buf, s);
        strcat(acPassWordNew, s);
    }
    GUI_SetColor(BACKCOLOR_DEFAULT);
    GUI_FillRect(PassWordx0,PassWordy0,PassWordx1,PassWordy1);
    GUI_SetColor(0x002169D5);
    GUI_DispStringInRect(buf, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER); 
}

static void CreateRefreshTimer(void)
{
	GUI_TIMER_CREATE(g_RefreshPassWordValueTimer, RefreshPassWordValue, REFRESH_WIFI_PWD_VALUE_ID, 0, 0);
    GUI_TIMER_SetPeriod(g_RefreshPassWordValueTimer, (GUI_TIMER_TIME)(1000 / (1000 / OS_CFG_TICK_RATE_HZ)));
    GUI_TIMER_Restart(g_RefreshPassWordValueTimer);    
}

// USER END


/*********************************************************************
*
*       _cbDialog
*/
static void _cbDialog(U8 Key) 
{    
    switch (Key) 
    {
        case GUI_KEY_F1:
        case GUI_KEY_ENTER:
        {
            if(i8Focus == 31)
            {
                ShowWifiSSIDSettingScreen(0);
//                ShowWifiSelectedScreen(1);
                i8Focus = 0;
                KeyBoardFocus = 0;
                LastKeyBoardFocus = 0;
            }
            else if(i8Focus == 30)
            {  
					// 使用GetWifiConfig()方法获取WIFICONFIG
					const WIFICONFIG* pWifiConfig = g_ConfigSave.GetWifiConfig();
					if (strcmp(pWifiConfig->SSID, GetCurruentSelectApName()) || strcmp(pWifiConfig->PWD, acPassWord) || g_WifiUartModule.GetConnectStatus() == 0)
                {
                    g_ConfigSave.SetApNamePwd(GetCurruentSelectApName(), acPassWord);
                    g_WifiUartModule.SetModuleInfo(GetCurruentSelectApName(), acPassWord);
                }
                UpdateTipMarket(WIFIPROMPT, 1);
            }
            else if(i8Focus == 28)
            {
                if (KeyBoardFocus < 2)
                {
                    KeyBoardFocus = 2;
                }
                else if (KeyBoardFocus >= 2)
                {
                    KeyBoardFocus = 0;
                }

                LastKeyBoardFocus = KeyBoardFocus;
                DispKeyboard_ABC(KeyBoardFocus,i8Focus);
            }
            else if(i8Focus == 19)
            {
                if(KeyBoardFocus < 2)
                {
                    if(KeyBoardFocus == 0)
                    {
                        KeyBoardFocus = 1;
                    }
                    else
                    {
                        KeyBoardFocus = 0;
                    }
                    LastKeyBoardFocus = KeyBoardFocus;
                    DispKeyboard_ABC(KeyBoardFocus,i8Focus);
                }
                else if (KeyBoardFocus >= 2)
                {
                    if (KeyBoardFocus == 2)
                    {
                        KeyBoardFocus = 3;
                    }
                    else
                    {
                        KeyBoardFocus = 2;
                    }
                    LastKeyBoardFocus = KeyBoardFocus;
                    DispKeyboard_ABC(KeyBoardFocus,i8Focus);
                }
            }
            else
            {
                UpdatePassword(i8Focus);
            }
        }
        break;
        case GUI_KEY_TAB:
        {
            i8Focus = (i8Focus + 1) > 31 ? 0 : (i8Focus +  1);
            if(i8Focus == 0)
                DispKeyboard_Spin(31,i8Focus,KeyBoardFocus);
            else
                DispKeyboard_Spin(i8Focus-1,i8Focus,KeyBoardFocus);
        }
        break;
        case GUI_KEY_BACKTAB:
        {
            i8Focus = (i8Focus - 1) < 0 ? 31 : (i8Focus -  1);
            if(i8Focus == 31)
                DispKeyboard_Spin(0,i8Focus,KeyBoardFocus);
            else
                DispKeyboard_Spin(i8Focus+1,i8Focus,KeyBoardFocus);
        }
        break;
        default:
        break;
    }
}

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/
static void UpdatePassword(uint8_t ID)
{
    U8 PassWordLen = 0;
    const char* pStr;
    char s[MAX_WIFI_PWD_LEN + 1], s1[MAX_WIFI_PWD_LEN + 1], s2[MAX_WIFI_PWD_LEN + 1];
    GUI_RECT rect;

    
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
    #else
        #if (LCD_TYPE == LCD_28_TFT)
            GUI_SetFont(FONT_18);
        #else
            GUI_SetFont(FONT_24);
        #endif
    #endif
    if (ID == 27)
    {
        if (g_sU8InputSymbolCnt == 0)
        {
            return;
        }

        memcpy(s1, acPassWord, g_sU8InputSymbolCnt - 1);
        memset(acPassWord, 0, sizeof(acPassWord)/sizeof(char));
        memcpy(&acPassWord, s1, g_sU8InputSymbolCnt - 1);
        
        memcpy(s2, acPassWordNew, g_sU8InputSymbolCnt - 1);
        memset(acPassWordNew, 0, sizeof(acPassWordNew)/sizeof(char));
        memcpy(acPassWordNew, s2, g_sU8InputSymbolCnt - 1);        

        g_sU8InputSymbolCnt--;
    }
    else
    {
        pStr = Str_KeyBordLetter[KeyBoardFocus][ID];
        if (g_sU8InputSymbolCnt < MAX_WIFI_PWD_LEN)
        {
            if (KeyBoardFocus == 3 && ID >= 15 && ID <= 26)
            {
                return;
            }
            else
            {
                g_sU8InputSymbolCnt++;
                strcat(acPassWord, pStr);
                strcat(acPassWordNew, pStr);
            }
        }
        
    }
    g_sU8PasswordRefresh = 1;    
   
    GUI_SetColor(BACKCOLOR_DEFAULT);
    GUI_FillRect(PassWordx0,PassWordy0,PassWordx1,PassWordy1);
    
    rect.x0 = PassWordx0;
    rect.y0 = PassWordy0;
    rect.x1 = PassWordx1;
    rect.y1 = PassWordy1;
    
    GUI_SetColor(0x002169D5);
    GUI_DispStringInRect(acPassWordNew, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER); 
}

static void RepaintWifiSSIDSettingDialog(void)
{
    GUI_RECT rect;
    char buf[MAX_WIFI_PWD_LEN + 1] = {0};
    const char* s = "*";

    rect.x0 = PassWordRect[0];
    rect.y0 = PassWordRect[1];
    rect.x1 = PassWordRect[2];
    rect.y1 = PassWordRect[3];

    GUI_SetColor(BACKCOLOR_DEFAULT);//设置界面背景色-黑色(0, 0, 0)
    GUI_FillRect(0, STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, MAINSCREEN_HEIGHT + STATEBAR_TITLE_HEIGHT);
    GUI_SetColor(0x00FFFFFF);
    #if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
    #else
        #if (LCD_TYPE == LCD_28_TFT)
            GUI_SetFont(FONT_18);
        #else
            GUI_SetFont(FONT_24);
        #endif
    #endif
    GUI_DispStringInRect(GetMultiLanguageString(PASSWORD_IDX),&rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
    DispKeyboard_ABC(KeyBoardFocus,i8Focus);
    
    rect.x0 = PassWordx0;
    rect.y0 = PassWordy0;
    rect.x1 = PassWordx1;
    rect.y1 = PassWordy1;
    
    for (U8 i = 0; i < g_sU8InputSymbolCnt; i++)
    {
        strcat(buf, s);
    }    
    GUI_SetColor(0x002169D5);
    GUI_DispStringInRect(buf, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER); 
}

void ShowWifiSSIDSettingScreen(uint8_t Flag)
{
    if (Flag)
    {
        g_WifiSSIDSettingScreenWInfo.U8CurFocusID = 0;
        g_WifiSSIDSettingScreenWInfo.U8OldFocusID = 0;
        
        g_sU8IsLowerCaseLetters = TRUE;
        g_sU8IsNumKeyBoard = FALSE;
        g_sU8InputSymbolCnt = 0;
        memset(acPassWord, 0, sizeof(acPassWord));
        memset(acPassWordNew, 0, sizeof(acPassWordNew));

        UpdateTopStateBarMenuString(GetMultiLanguageString(WLANTITLE_IDX));
        CreateRefreshTimer(); 
        g_U8CurEditState_ForPowerON = TRUE;	
        g_WindowDrv.PushWindow(&g_WifiSSIDSettingScreenWInfo);
    }
    else
    {
        UpdateTopStateBarMenuString(GetMultiLanguageString(WLANTITLE_IDX));
		GUI_TIMER_DELETE(g_RefreshPassWordValueTimer);
        g_U8CurEditState_ForPowerON = FALSE;   
        g_WindowDrv.PopWindow(&g_WifiSSIDSettingScreenWInfo);
    }
}
// USER END

/*************************** End of file ****************************/

