#include "AutoPressModule.h"
#include "GlobalVariable.h"
#include "SignalCondition.h"
#include "ConfigSave.h"
#include "bsp_bldcm_control.h"
#include "OxiModule.h"
#include "CalcDateTime.h"
#include "bsp_pid.h"

#include "main.h"
#include "bsp_mcu_uart.h"
#define TIT_PRESS_MODULE_TIMEOUT_MAX (3000)

static int16_t m_s16CurrentPress = -1;  // 当前压力
static uint8_t m_AutoTitPressFlag = 0; // 进入自动定标标志
static const uint8_t m_u8PressArray[4] = {0, 40, 100, 200};
static const uint16_t m_u8PwmArray[4] = {0, 450, 850, 1500}; // 预估目标压力对应的PWM
static uint8_t m_u8status = 0;                               // 状态标志
static uint8_t m_u8AutoTitPressSuccessFlag = 0;              // 定标成功标志

void AddBldcmPWM(U8 Flag, U8 Value);
static void DutyAdjust(uint8_t target);
static uint8_t autopree_data_analysis(void);

U8 g_U8OxiRcvBuf[OXI_ISR_RCV_LENGTH];

uint16_t GetCurrentPress(void)
{
    return m_s16CurrentPress;
}

uint8_t GetAutoTitPressFlag(void)
{
    return m_AutoTitPressFlag;
}

uint8_t GetAutoTitPressStatus(void)
{
    return m_u8status;
}

void SetAutoTitPressStatus(uint8_t u8status)
{
    m_u8status = u8status;
}

void SetAutoTitPressSuccessFlag(uint8_t Flag)
{
    m_u8AutoTitPressSuccessFlag = Flag;
}

/**
 * @brief  自动压力定标结束
 * @note
 * @param  无
 * @retval 无
 */
void AutoPressEnd(void)
{
    if(m_AutoTitPressFlag == 1)
    {
        OS_ERR err = OS_ERR_NONE;
        m_AutoTitPressFlag = 0;
        m_u8status = 0;
    }
}

/**
 * @brief  自动压力定标
 * @note
 * @param  无
 * @retval 无
 */
void AutoPressCal(void)
{
    static uint8_t u8count = 0;

    // 如果自动定标压力标志为正在定标
    if(GetAutoTitPressFlag() == 1)
    {
        // 解析数据 得到压力
        autopree_data_analysis();
        switch(m_u8status)
        {
            case STATUS_TIT0PRESS:
                if(m_s16CurrentPress == m_u8PressArray[0])
                {
                    u8count++;
                    if(u8count == CONTINUOUS_MATCHES_NUMBER)  // 多次匹配
                    {
                        g_ConfigSave.SetParameter((ECONFIGTYPE)TIT0PRESS, (U32)GetPressCode(), 0);
                        g_ConfigSave.SetParameter((ECONFIGTYPE)TIT0PWM, GetBldcmPwm(), 0);
                        g_ConfigSave.SetParameter(TITPRESSTIME, GetCurrentDateTime());
                        m_u8status++;
                        u8count = 0;
                        m_u8AutoTitPressSuccessFlag = 10; // 定标成功标志
                    }
                }
                else
                {
                    u8count = 0;
                }
                break;
            case STATUS_TIT4PRESS:
                if(m_s16CurrentPress == m_u8PressArray[1])
                {
                    u8count++;
                    if(u8count == CONTINUOUS_MATCHES_NUMBER)
                    {
                        g_ConfigSave.SetParameter((ECONFIGTYPE)TIT4PRESS, (U32)GetPressCode(), 0);
                        g_ConfigSave.SetParameter((ECONFIGTYPE)TIT4PWM, GetBldcmPwm(), 0);
                        g_ConfigSave.SetParameter(TITPRESSTIME, GetCurrentDateTime());
                        m_u8status++;
                        u8count = 0;
                        m_u8AutoTitPressSuccessFlag = 10; // 定标成功标志
                    }
                }
                else
                {
#if (AUTOTITPRESS_ENABLE_PID == 1)
                    if(m_u8AutoTitPressSuccessFlag)  // 定标成功标志
                    {
                        set_p_i_c(8, 0.02, 0);
                        set_pid_target(m_u8PressArray[1]);
                        m_u8AutoTitPressSuccessFlag = 0; // 定标成功标志
                    }
                    SetBldcmPwm(PID_realize(m_s16CurrentPress, MIN_DUTY_CYCLE, MAX_DUTY_CYCLE));
#else
                    if(m_u8AutoTitPressSuccessFlag)
                    {
                        SetBldcmPwm(m_u8PwmArray[1]);
                        m_u8AutoTitPressSuccessFlag--;
                    }
                    else
                    {
                        DutyAdjust(m_u8PressArray[1]);
                    }
#endif
                    u8count = 0;
                }
                break;
            case STATUS_TIT10PRESS:
                if(m_s16CurrentPress == m_u8PressArray[2])
                {
                    u8count++;
                    if(u8count == CONTINUOUS_MATCHES_NUMBER)
                    {
                        g_ConfigSave.SetParameter((ECONFIGTYPE)TIT10PRESS, (U32)GetPressCode(), 0);
                        g_ConfigSave.SetParameter((ECONFIGTYPE)TIT10PWM, GetBldcmPwm(), 0);
                        g_ConfigSave.SetParameter(TITPRESSTIME, GetCurrentDateTime());
                        m_u8status++;
                        u8count = 0;
                        m_u8AutoTitPressSuccessFlag = 10; // 定标成功标志
                    }
                }
                else
                {
#if (AUTOTITPRESS_ENABLE_PID == 1)
                    if(m_u8AutoTitPressSuccessFlag)  // 定标成功标志
                    {
                        set_p_i_c(8, 0.02, 0);
                        set_pid_target(m_u8PressArray[2]);
                        m_u8AutoTitPressSuccessFlag = 0; // 定标成功标志
                    }
                    SetBldcmPwm(PID_realize(m_s16CurrentPress, MIN_DUTY_CYCLE, MAX_DUTY_CYCLE));
#else
                    if(m_u8AutoTitPressSuccessFlag)
                    {
                        SetBldcmPwm(m_u8PwmArray[2]);
                        m_u8AutoTitPressSuccessFlag--;
                    }
                    else
                    {
                        DutyAdjust(m_u8PressArray[2]);
                    }
#endif
                    u8count = 0;
                }
                break;
            case STATUS_TIT20PRESS:
                if(m_s16CurrentPress == m_u8PressArray[3])
                {
                    u8count++;
                    if(u8count == CONTINUOUS_MATCHES_NUMBER)
                    {
                        g_ConfigSave.SetParameter((ECONFIGTYPE)TIT20PRESS, (U32)GetPressCode(), 0);
                        g_ConfigSave.SetParameter((ECONFIGTYPE)TIT20PWM, GetBldcmPwm(), 0);
                        g_ConfigSave.SetParameter(TITPRESSTIME, GetCurrentDateTime());
                        m_u8status++;
                        u8count = 0;
                        m_u8AutoTitPressSuccessFlag = 10;
                    }
                }
                else
                {
#if (AUTOTITPRESS_ENABLE_PID == 1)
                    if(m_u8AutoTitPressSuccessFlag)
                    {
                        set_p_i_c(8, 0.02, 0);
                        set_pid_target(m_u8PressArray[3]);
                        m_u8AutoTitPressSuccessFlag = 0;
                    }
                    SetBldcmPwm(PID_realize(m_s16CurrentPress, MIN_DUTY_CYCLE, MAX_DUTY_CYCLE));
#else
                    if(m_u8AutoTitPressSuccessFlag)
                    {
                        SetBldcmPwm(m_u8PwmArray[3]);
                        m_u8AutoTitPressSuccessFlag--;
                    }
                    else
                    {
                        DutyAdjust(m_u8PressArray[3]);
                    }
#endif
                    u8count = 0;
                }
                break;
            case STATUS_TITSUCCESSPRESS:
                if(m_u8AutoTitPressSuccessFlag)
                {
#if (AUTOTITPRESS_ENABLE_PID == 1)
                    set_pid_target(m_u8PressArray[0]);
                    clear_history_err();
#endif
                    if(GetBldcmPwm() > MIN_DUTY_CYCLE)
                    {
                        // SetBldcmPwm(MIN_DUTY_CYCLE);
                        g_ConfigSave.StartPressCali(0);
                    }
                    m_u8AutoTitPressSuccessFlag = 0; // 防止反复设置
                }
                break;
        }
    }
}

/**
 * @brief  自动压力定标开始
 * @note
 * @param  无
 * @retval 无
 */
void AutoPressStart(void)
{
    OS_ERR err = OS_ERR_NONE;
    if(OSTimeGet(&err) > 8000)  // PC串口通信时修改波特率导致其失效
    {
        // 复位环形缓冲数组
        reset_mcu_uart_ring_buff();
#if (AUTOTITPRESS_ENABLE_PID == 1)
        PID_param_init();
#endif
        m_AutoTitPressFlag = 1;
    }
}

static void DutyAdjust(uint8_t target)
{
    if(target - m_s16CurrentPress >= 25)
    {
        AddBldcmPWM(1, 25);
    }
    else if(target - m_s16CurrentPress >= 10)
    {
        AddBldcmPWM(1, 10);
    }
    else if(target - m_s16CurrentPress >= 5)
    {
        AddBldcmPWM(1, 5);
    }
    else if(target - m_s16CurrentPress >= 1)
    {
        AddBldcmPWM(1, 1);
    }
    else if(target - m_s16CurrentPress <= -25)
    {
        AddBldcmPWM(0, 25);
    }
    else if(target - m_s16CurrentPress <= -10)
    {
        AddBldcmPWM(0, 10);
    }
    else if(target - m_s16CurrentPress <= -5)
    {
        AddBldcmPWM(0, 5);
    }
    else if(target - m_s16CurrentPress <= -1)
    {
        AddBldcmPWM(0, 1);
    }
}

/**
 * @brief 自动压力校准数据分析
 * @return int 0数据长度不够，继续轮询等待 1解析成功 0解析失败
 */
static uint8_t autopree_data_analysis(void)
{
    //记录初始时刻
    static uint32_t wExistTick = 0;

    uint8_t bAutoPressRcvBuf[AUTOTITPRESS_ISR_RCV_LENGTH] = {0};
    RingBuffer_t *pRingBuff = get_instance_mcu_rx_rb();

    // 更新环形缓存数据指针
    mcu_uart_receive_buff_poll(pRingBuff);

    //判断时间是否超时，超时代表压力仪未连接，压力仪连接时，实测约1074个tick发送一包数据
    if(time_count(tick_get(), wExistTick) > TIT_PRESS_MODULE_TIMEOUT_MAX)
    {
        m_s16CurrentPress = -1; // 超时未接收到数据，数据异常
    }

    // 自动压力校准接收帧长度
    if(ring_buffer_used_space(pRingBuff) < AUTOTITPRESS_ISR_RCV_LENGTH)
    {
        return 0;
    }

    ring_buffer_get_offset_data(pRingBuff, 0,
                    (uint8_t *)&bAutoPressRcvBuf, AUTOTITPRESS_ISR_RCV_LENGTH);

    // 数据格式为：PXXXXmmH2OAD(如压力为1 P0001mmH2OAD)
    if(bAutoPressRcvBuf[0] == 'P' && bAutoPressRcvBuf[5] == 'm' && bAutoPressRcvBuf[6] == 'm' && bAutoPressRcvBuf[7] == 'H'
                    && bAutoPressRcvBuf[8] == '2' && bAutoPressRcvBuf[9] == 'O')
    {
        m_s16CurrentPress = 0;
        m_s16CurrentPress += (bAutoPressRcvBuf[1] - '0') * 1000;
        m_s16CurrentPress += (bAutoPressRcvBuf[2] - '0') * 100;
        m_s16CurrentPress += (bAutoPressRcvBuf[3] - '0') * 10;
        m_s16CurrentPress += (bAutoPressRcvBuf[4] - '0');

        //记录接收到一包正确数据的时刻
        wExistTick = tick_get();

        ring_buffer_consume(pRingBuff, AUTOTITPRESS_ISR_RCV_LENGTH);
        return 1;
    }
    else
    {
        m_s16CurrentPress = -1; // 数据异常
        ring_buffer_consume(pRingBuff, 1);
        return 0;
    }
}
