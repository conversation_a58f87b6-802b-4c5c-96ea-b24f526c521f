{"metadata": {"format_version": "1.0", "generator": "batch_generate_tokens.py", "generated_at": "2023-05-15T10:30:00Z", "tenant_id": "SLEEPRES-DEV", "description": "设备令牌映射文件，包含设备序列号到GalenCloud令牌的映射关系"}, "tokens": {"100001": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************.SVK_2S85V9ge4VxMyw3xhwiWrcBYxqXxUVxjAkIQnF_uUEb9vQFx2LVzGf-C4YmSeTFRQc6ZQtMEA5dA9YZLyg", "created_at": "2023-05-15T10:30:00Z", "expires_at": "2028-05-14T10:30:00Z", "device_model": "SleepRes-T2", "device_status": "active"}, "100002": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************.Fj-Xq7kBqSSa6xHx1L6iZrxxVcqbRYJjnrw0ECZbdiBuGXNlkPxSUPgY-a-ywS6_QIGYcIdQA3SbsMsOaZwrDw", "created_at": "2023-05-15T10:31:15Z", "expires_at": "2028-05-14T10:31:15Z", "device_model": "SleepRes-T2", "device_status": "active"}, "100003": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************.gVpuV-msDCqMhz9hJ61y0Zs3LKPpGMTw0B4QyUhq_P5wqvTSCHkdSch1Bnm1YWaGJyKjtKcD-h1Q9rB82u8HVw", "created_at": "2023-05-15T10:32:30Z", "expires_at": "2028-05-14T10:32:30Z", "device_model": "SleepRes-T2", "device_status": "active"}, "100004": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************.QYcLhBMJ8FsFBVc1G2cRUfkj0wIOEb_Hgap5KD8LXXD6rG9UHFZv8P1dUbNYqxbfrLUx5rI5nOxC9JKpSCfzqA", "created_at": "2023-05-15T10:33:45Z", "expires_at": "2028-05-14T10:33:45Z", "device_model": "SleepRes-T2", "device_status": "inactive"}, "100005": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************.sMVWe_1a-2zZ-5BUXNrNvxQzXvTXOLDVhx05-iWxiADXNQUjWbqgWcufgxr4EQp8Ee1VVvIbJYNk4B8TFr_HRg", "created_at": "2023-05-15T10:34:50Z", "expires_at": "2028-05-14T10:34:50Z", "device_model": "SleepRes-T2", "device_status": "active"}, "200001": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************.VzpdOMeJTJmVjU9F8rDjzwS3RRGE_1r3YKGRcxwFQv4Pw6uGmjR4d2TLlqJGtUF9Vyx9LnrUvUdDQ9eVLQ5IYA", "created_at": "2023-05-16T09:00:00Z", "expires_at": "2028-05-15T09:00:00Z", "device_model": "SleepRes-T3", "device_status": "active"}, "200002": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************.WkLkx-jXnAqcQJD2tCYOBHFBUfvDjCQD7HNn2O6nUcO6OYq4UFqwAhR2xY3Q3rqy8VZl5Qzha0X_HWbZbqbnIA", "created_at": "2023-05-16T09:01:15Z", "expires_at": "2028-05-15T09:01:15Z", "device_model": "SleepRes-T3", "device_status": "active"}}}