#include "stack.h"

CWindowDrv::CWindowDrv()
{
    m_pCurrWindow = NULL;
}

CWindowDrv::~CWindowDrv()
{
}

PWINDOWINFO CWindowDrv::GetTopWindow()
{ 
	if (m_Stack.Count() > 0)
	{		
		return (PWINDOWINFO)m_Stack.Top();
	}	
	return m_pCurrWindow;
}

void CWindowDrv::ProcessKey(U8 Key)
{
    if (m_pCurrWindow->ProcessKey)
    {
        m_pCurrWindow->ProcessKey(Key);
    }
    else
    {
        if(m_pCurrWindow->U8WindowID == DEBUGSETOFFSET_ID)
        {
            if(GetFocus(&Key))
            {
                GUI_SendKeyMsg(Key, 1);
            }
        }
        else
        {
            GUI_SendKeyMsg(Key, 1);
        }        
    }    
}

bool CWindowDrv::PushWindow(PWINDOWINFO pWind, U8 ForceRepaint)
{
    if (m_pCurrWindow != pWind)
    {
        if (m_pCurrWindow && m_pCurrWindow->OtherProcess)
        {
            m_pCurrWindow->OtherProcess();
        }            
        m_pCurrWindow = pWind;
        m_Stack.Push(pWind);
        if (m_pCurrWindow->Repaint)
        {
            m_pCurrWindow->Repaint();
        }
        return true;
    }
    else if (ForceRepaint)
    {
        if (m_pCurrWindow->Repaint)
        {
            m_pCurrWindow->Repaint();
        }
    }
    return false;
}

bool CWindowDrv::PopWindow(PWINDOWINFO pWind)
{
    if (m_pCurrWindow == pWind)
    {
        if (m_pCurrWindow->Exit)
        {
            m_pCurrWindow->Exit();
        }
        m_Stack.Pop();
        m_pCurrWindow = (PWINDOWINFO)m_Stack.Top();
        if (m_pCurrWindow->ShowProcess)
        {
            m_pCurrWindow->ShowProcess();
        }
        if (m_pCurrWindow->Repaint)
        {
            m_pCurrWindow->Repaint();
        }
        return true;
    }
    return false;
}

bool CWindowDrv::PopWindowNoRepaint(PWINDOWINFO pWind)
{
    if (m_pCurrWindow == pWind)
    {
        if (m_pCurrWindow->Exit)
        {
            m_pCurrWindow->Exit();
        }
        m_Stack.Pop();
        m_pCurrWindow = (PWINDOWINFO)m_Stack.Top();
        return true;
    }
    return false;
}

bool CWindowDrv::ReturnMainWindow()
{
    uint8_t i = 0;
    uint8_t u8WindowsCount = 0;

    if (m_Stack.Count() > 1)
    {
        u8WindowsCount = m_Stack.Count() - 1;
        for (i = 0; i < u8WindowsCount; i++)
        {
            if (m_pCurrWindow->U8WindowID == MONITORVIEW_ID || m_pCurrWindow->U8WindowID == WAVEVIEW_ID)
            {
                m_pCurrWindow->U8CurFocusID = 0;
                m_pCurrWindow->U8OldFocusID = 1;
            }
            else
            {
                m_pCurrWindow->U8CurFocusID = 0;
                m_pCurrWindow->U8OldFocusID = 0;
            }
            PopWindowNoRepaint(m_pCurrWindow);
        }

		if (m_pCurrWindow->ShowProcess)
		{
			m_pCurrWindow->ShowProcess();
		}
		if (m_pCurrWindow->Repaint)
		{
			m_pCurrWindow->Repaint();
		}
	return TRUE;
    }
    return FALSE;
}

void CWindowDrv::SetCurWindow(PWINDOWINFO pWind)
{
    m_pCurrWindow = pWind;
}

bool CWindowDrv::ShowIsSysSetting()
{
    return m_pCurrWindow->U8WindowID == SYSSETTING_ID;
}

bool CWindowDrv::ShowIsMainScreen()
{
    return m_pCurrWindow->U8WindowID == MAINSCREEN_ID;
}

/*debug 界面判断*/
bool CWindowDrv::ShowIsDebugScreen()
{
	if(m_pCurrWindow->U8WindowID >= DEBUG_ID 
		&& m_pCurrWindow->U8WindowID <= AUTORPRESCALI_ID)
		return true;
	else
		return false;
}

bool CWindowDrv::ShowIsWaveMonitor()
{
    return m_pCurrWindow->U8WindowID == WAVEVIEW_ID;
}

bool CWindowDrv::ShowIsAlarm()
{
    return m_pCurrWindow->U8WindowID == ALARMPOPUP_ID;
}

bool CWindowDrv::ShowHaveWaveMonitorAndQuickSetting()
{
    if (m_Stack.m_I8Top < 2)
    {
        return false;
    }
    if (((PWINDOWINFO)(m_Stack.m_Item[1]))->U8WindowID == WAVEVIEW_ID && ((PWINDOWINFO)(m_Stack.m_Item[2]))->U8WindowID == QUICKSETTING_ID)
    {
        return true;
    }
    return false;
}

bool CWindowDrv::ShowHaveWaveMonitor()
{
    I8 i;
    bool sBoolResult = false;
    if (m_pCurrWindow->U8WindowID == WAVEVIEW_ID)
    {
        return true;
    }
    for (i = 0; i <= m_Stack.m_I8Top; i++)
    {
        if (((PWINDOWINFO)(m_Stack.m_Item[i]))->U8WindowID == WAVEVIEW_ID)
        {
            if (m_Stack.m_I8Top < STACK_SIZE - 1 && ((PWINDOWINFO)(m_Stack.m_Item[i + 1]))->U8WindowID == QUICKSETTING_ID)//从波形显示界面进入快捷菜单后触发弹出提示框会显示异常
            {
                sBoolResult = false;
            }
            else
            {
                sBoolResult = true;
            }
            break;
        }
    }
    if (!sBoolResult)
    {
        return false;
    }
    if (m_pCurrWindow->U8WindowID != ALARMPOPUP_ID)
    {
        return false;
    }        
    
    return true;
}

bool CWindowDrv::ShowHaveMonitorView()
{
    I8 i;
    bool sBoolResult = false;
    if (m_pCurrWindow->U8WindowID == MONITORVIEW_ID)
    {
        return true;
    }    
    for (i = 0; i <= m_Stack.m_I8Top; i++)
    {
        if (((PWINDOWINFO)(m_Stack.m_Item[i]))->U8WindowID == MONITORVIEW_ID)
        {
            if (m_Stack.m_I8Top < STACK_SIZE - 1 && ((PWINDOWINFO)(m_Stack.m_Item[i + 1]))->U8WindowID == QUICKSETTING_ID)
            {
                sBoolResult = false;
            }
            else
            {
                sBoolResult = true;
            }
            break;
        }
    }
    if (!sBoolResult)
    {
        return false;
    }
#if (LCD_TYPE == LCD_28_TFT)
    if (m_pCurrWindow->U8WindowID == ALARMPOPUP_ID || m_pCurrWindow->U8WindowID == SYSRESETPOPUP_ID)
    {
        return false;
    }
#else    
    if (m_pCurrWindow->U8WindowID != ALARMPOPUP_ID)
    {
        return false;
    }
#endif
    return true;
}

bool CWindowDrv::ShowIsMonitorView()
{
    return m_pCurrWindow->U8WindowID == MONITORVIEW_ID;
}

bool CWindowDrv::ShowIsRetestWindow()
{
    return m_pCurrWindow->U8WindowID == RETEST_ID;
}

bool CWindowDrv::CheckRepaintParameterSetting()
{
    U8 sU8Flag = 0;
    I8 i;
    for (i = m_Stack.m_I8Top; i >= 0; i--)
    {
        if ((((PWINDOWINFO)(m_Stack.m_Item[i]))->U8WindowID == PARAMETERSETTING_ID)
            || (((PWINDOWINFO)(m_Stack.m_Item[i]))->U8WindowID == QUICKSETTING_ID)
            || (((PWINDOWINFO)(m_Stack.m_Item[i]))->U8WindowID == MONITORVIEW_ID)
            || (((PWINDOWINFO)(m_Stack.m_Item[i]))->U8WindowID == WAVEVIEW_ID))
        {
            if (((PWINDOWINFO)(m_Stack.m_Item[i]))->OtherProcess)
            {
                ((PWINDOWINFO)(m_Stack.m_Item[i]))->OtherProcess();
            }
            if (((PWINDOWINFO)(m_Stack.m_Item[i]))->Repaint)
            {
                ((PWINDOWINFO)(m_Stack.m_Item[i]))->Repaint();
            }
            sU8Flag = 1;
            break;
        }
    }
    return sU8Flag;
}

bool CWindowDrv::ShowIsDebugOrFlowCaliOrPresCali()
{
    return m_pCurrWindow->U8WindowID == DEBUG_ID 
            || m_pCurrWindow->U8WindowID == FLOWCALI_ID 
            || m_pCurrWindow->U8WindowID == AUTO_FLOWCALI_ID
            || m_pCurrWindow->U8WindowID == PRESSCALI_ID
            || m_pCurrWindow->U8WindowID == ADMIN_PRESSCALI_ID
            || m_pCurrWindow->U8WindowID == ADMIN_FLOWCALI_ID;
}


CWindowDrv g_WindowDrv;

