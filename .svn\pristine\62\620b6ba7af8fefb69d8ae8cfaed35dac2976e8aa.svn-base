#ifndef __SDCARDDATASAVE_H__
#define __SDCARDDATASAVE_H__

#include "DataType.h"
#include "fatfs.h"
#include "AlarmManager.h"
#include "MutexObj.h"

#define SDCARD_CONNECT        1
#define SDCARD_NOT_CONNECT    0

#define REAL_DATA_SIZE      4096
#define EVENT_DATA_SIZE     256       //255是因为二代采用5个字节为一组事件数据
#define BASE_DATA_SIZE      256

#define MAX_DATETIME_MODIDFY_CNT    64 //最大存储64组,2K空间日期时间修改记录

#if (SDCARD_PROTOCAL == 1)
typedef enum EEventDataType
{
    ID_LOGEVENT = 21,
    ID_SERIAL_LOGEVENT = 22,
    DATE_LOGEVENT = 23,
    TIME_LOGEVENT = 24,
    POWEROFF_LOGEVENT = 25,
    HYPOVENTILATION_LOGEVENT = 26,   //低通气
    SNORE_LOGEVENT = 27,             //鼾声
    FLOWLIMIT_LOGEVENT = 28,         //气流受限
    PARA_CHANGED_LOGEVENT = 29,      //参数更改
    OBSTRUCTIVEAPNEA_LOGEVENT = 30,  //阻塞型
    CENTRALAPNEA_LOGEVENT = 31,      //中枢型
    MIXAPNEA_LOGEVENT = 32,          //混合型
//    SPO2REDUCE_LOGEVENT = 33,        //氧减
    BASEPRESS_LOGEVENT = 34,         //压力基值
    FLOWLEAK_LOGEVENT = 35,          //漏气
    
    HIGHPRESS_LOGEVENT = 38,         //高吸气压
    LOWPRESS_LOGEVENT = 39,          //低吸气压    
    LOWMV_LOGEVENT = 40,             //低分钟通气量
//    LOWVT_LOGEVENT = 41,             //低潮气量    
    HIGHLEAKAGE_LOGEVENT = 42,       //高漏气

    HUMIFAILURE_LOGEVENT = 44,       //加湿器失效
//加热管路失效

    AIRWAYOBSTRUCTION_LOGEVENT = 45,         //气道阻塞
    MOTORFAULT1_LOGEVENT = 46,       //电机异常,温度过高
    HUMI_SHORTCIRCUIT_LOGEVENT = 47,      //加湿器短路
    MOTORFAULT3_LOGEVENT = 48,       //电机异常,堵转
    REPLACEFILTER_LOGEVENT = 49,     //更换过滤棉
    REPLACETUBE_LOGEVENT = 50,       //更换管路
    REPLACEMASK_LOGEVENT = 51,       //更换面罩
    
    PRESSURE_ABNORMAL_LOGEVENT = 52,   //压力传感器异常
        
//    SPO2TOOLOW_LOGEVENT = 52,        //血氧过低
//    SPO2TOOHIGH_LOGEVENT = 53,       //血氧过高
//    PRTOOLOW_LOGEVENT = 54,          //脉率过低
//    PRTOOHIGH_LOGEVENT = 55,         //脉率过高
//    SPO2DISCONNECT_LOGEVENT = 56,    //血氧模块断开
    LOWVOLTAGE_LOGEVENT = 57,        //低电压 
    SDFULL_LOGEVENT = 58,            //SD卡存满
    
    HEATPIPEFAIL_LOGEVENT = 59,      //加热管路失效

    SNORE_LOGEVENT_ANOTHER = 60,             //鼾声
    FALLASLEEP_LOGEVENT = 61,       //入睡事件
    NULL_PERIOD_EVENT = 62,         //空窗期事件
    APP_SOFT_VERSION = 63           //软件版本号
} EEVENTDATATYPE;

typedef enum ERealTimeDataType
{
    ID1_REALTIME = 1,
    IDSERIAL_REALTIME,
    DATE_REALTIME,
    TIME_REALTIME,
   
    PRESS_FLOW_REALTIME,
    INSPTIMELEAK_REALTIME,
    EXPTIMETV_REALTIME,    
    
    INSPTVPEAK_REALTIME,
    SPO2_AND_PR_REALTIME,
    BPM_AND_MV_REALTIME,
    R12VALUE_AND_FLOWBASE_REALTIME,
    FLOW_ADD_OFFSET_REALTIME,
#if (ENABLE_SAVE_DEBUG_SNORE_DATA == 1)
    TEST_SNORE1_REALTIME,
    TEST_SNORE2_REALTIME,
#endif
}EREALTIMEDATATYPE;

#elif (SDCARD_PROTOCAL == 0)

typedef enum EEventDataType
{
    ID_SERIAL_LOGEVENT = 1,
    DATE_LOGEVENT,
    TIME_LOGEVENT,
    POWERON_LOGEVENT,
    POWEROFF_LOGEVENT,
    SNORE_LOGEVENT,
    FLOWLIMIT_LOGEVENT,
    PARA_CHANGED_LOGEVENT,
    OBSTRUCTIVEAPNEA_LOGEVENT,
    CENTRALAPNEA_LOGEVENT,
    MIXAPNEA_LOGEVENT,
    SYSPARA1_LOGEVENT,
    SYSPARA2_LOGEVENT,
    SYSPARA3_LOGEVENT,
    SYSPARA4_LOGEVENT,
    BASEPRESS_LOGEVENT,
    FLOWLEAK_LOGEVENT,
    ID_LOGEVENT,
    HYPOVENTILATION_LOGEVENT,
    SYSPARA5_LOGEVENT,
    SYSPARA6_LOGEVENT,
    SYSPARA7_LOGEVENT,
    HIGHPRESS_LOGEVENT,
    LOWPRESS_LOGEVENT,
    HIGHLEAKAGE_LOGEVENT,
    AIRWAYOBSTRUCTION_LOGEVENT,
    LOWVT_LOGEVENT,
    LOWMV_LOGEVENT,
    REPLACEFILTER_ALARM_LOGEVENT,
    SDFULL_ALARM_LOGEVENT,
    HUMIFAILURE_ALARM_LOGEVENT,
    MOTORFAULT_ALARM_LOGEVENT,
    
    AIRWAYOBSTRUCTION_LOGEVENT,
    SPO2REDUCE_LOGEVENT,
    ASPHYXIA_LOGEVENT,
    
    HUMIFAILURE_LOGEVENT,       //加湿器失效
    MOTORFAULT1_LOGEVENT,       //电机异常,温度过高
    MOTORFAULT2_LOGEVENT,       //电机异常,过流
    MOTORFAULT3_LOGEVENT,       //电机异常,堵转    
    REPLACEFILTER_LOGEVENT,     //更换过滤棉
    SDFULL_LOGEVENT,            //SD卡存满
    
    MASKOFF_LOGEVENT,           //面罩脱落
    SPO2TOOHIGH_LOGEVENT,       //血氧过高
    SPO2TOOLOW_LOGEVENT,        //血氧过低
    PRTOOHIGH_LOGEVENT,         //脉率过高
    PRTOOLOW_LOGEVENT,          //脉率过低
    SPO2DISCONNECT_LOGEVENT,    //血氧模块断开
    FINGEROFFLINE_LOGEVENT,     //手指断开    
    LOWVOLTAGE_LOGEVENT,        //低电压
    
    SYSPARA8_LOGEVENT = 107,    
    SYSPARA9_LOGEVENT = 108,    
    SYSPARA10_LOGEVENT = 109,    
    SYSPARA11_LOGEVENT = 110,    
    SYSPARA12_LOGEVENT = 111,      
} EEVENTDATATYPE;

typedef enum ERealTimeDataType
{
    PRESS_FLOW_REALTIME = 1,
    TIME_REALTIME,
    DATE_REALTIME,
    ID1_REALTIME,
   
    INSPTIMELEAK_REALTIME,
    EXPTIMETV_REALTIME,    
    INSPTVPEAK_REALTIME,
    
    IDSERIAL_REALTIME,
    SPO2_AND_PR_REALTIME,
    BPM_AND_MV_REALTIME,
}EREALTIMEDATATYPE;

#endif

typedef enum EFileIdx
{
    RT_DATA_IDX = 0,
    EVENT_DATA_IDX,
    BASE_DATA_IDX,
    OTHER_DATA_IDX,
#if (WIFI_NET_DEBUG == 1)
    WIFI_DEBUG_SEND_IDX,
    WIFI_DEBUG_RCV_IDX,
#endif    
    //SETTING_DATA_IDX,
    DATETIME_SET_IDX,
    CATCH_BMP_IDX,
    DEV_CALIB_DATA_IDX,
    FILE_HANDLE_NUM_MAX
}EFILEIDX;

typedef enum EWirteEventType
{
    CREATE_FILE = 0x01,
    WRITE_RT_DATA,
    WRITE_EVENT_DATA,
    WRITE_BASE_DATA,
#if (WIFI_NET_DEBUG == 1)    
    WRITE_NET_DEBUG_SEND_DATA,
    WRITE_NET_DEBUG_RCV_DATA,
#endif    
    CLOSE_FILE,
	EDF_TIMER_SAVE,
	EDF_CLOSE_SAVE,
	TIME_UPDATEDF,
    WIFI_INFODATA_SAVE,
    WIFI_REALDATA_SAVE,
    EXPORT_PARAMETER_CONFIG,
    CATCH_BMP_PICTURE,
    SD_CARD_MOUNT,
    SD_CARD_UNMOUNT,
    DATETIME_MODIFY_SAVE,
    WRITE_DEV_CALIBRATE_DATA,
}EWRITEEVENTTYPE;

#if (SDCARD_PROTOCAL == 1) 
typedef union URealTimeData
{
    struct
    {
        U32 DataType : 6;
        U32 IDBit31_5 : 26;
    } ID;
    struct
    {
        U8 DataType : 6;
        U8 Reserved : 2;

        U8 Year;
        U8 Month;        
        U8 Day;        
    } Date;
    struct
    {
        U8 DataType : 6;
        U8 Reserved : 2;

        U8 Hour;        
        U8 Min;        
        U8 Sec;        
    } Time;
    struct
    {
        U32 DataType : 6;   
        U32 Data18_6 : 13;
        U32 Data31_19 : 13;
    } BasicData;
}UREALTIMEDATA, *PUREALTIMEDATA;

typedef union UEventData
{
    struct
    {
        U32 DataType : 6;
        U32 IDBit31_6 : 26;
        U16 ReserveBit47_32 : 16;
    } ID;
    struct
    {
        U32 DataType : 6;
        U32 IDBit23_6 : 18;
        U32 ReserveBit31_24 : 8;
        U16 ReserveBit47_32 : 16;
    } PowerOff;    
    struct
    {
        U8 DataType : 6;
        U8 Reserved : 2;

        U8 Year;
        U8 Month;        
        U8 Day;    
        
        U8 Reserve[2];
    } Date;
    struct
    {
        U8 DataType : 6;
        U8 Reserved : 2;

        U8 Hour;        
        U8 Min;        
        U8 Sec;     

        U8 Reserve[2];
    } Time;
    struct   
    {      
        U32 DataType : 6;
        U32 Data23_6 : 18;
        U32 Data31_24 : 8;       
        U16 ReserveBit47_32 : 16;
    } EventData; 
    struct   
    {      
        U32 DataType : 6;
        U32 Data22_6 : 17;
        U32 Data31_23 : 9;               
        U16 ReserveBit47_32 : 16;
    } EventData2;   //压力基值，漏气值   
    struct
    {
        U32 DataType : 6;
        U32 Data23_6 : 18;       //开机时间
        U32 Data31_24 : 8;      //报警设置值低8位
        U16 Data35_32 : 4;      //报警设置值高4位
        U16 Data47_36 : 12;      //当前值  
    } EventData3;    
    struct   
    {      
        U32 DataType : 6;
        U32 Data15_6 : 10;          //参数ID
        U32 Data30_16 : 15;         //参数值   
        U32 Data31 : 1;
        U16 DataMotorOnTime : 16;   //Data31为最低位与DataMotorOnTime,合并为17位,表示开机时长
    } ParameterData; 
}UEVENT_BASE_DATA, *PUEVENT_BASE_DATA;

#elif (SDCARD_PROTOCAL == 0)
typedef union URealTimeData
{
    struct
    {
        uint8_t DataType : 4;
        uint8_t IDBit23_21 : 3;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t IDBit20_14 : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t IDBit13_7 : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t IDBit6_0 : 7;
        uint8_t ErrorCheckBit3 : 1;
    } ID;
    struct
    {
        uint8_t DataType : 4;
        uint8_t IDSerialBit23_21 : 3;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t IDSerialBit20_14 : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t IDSerialBit13_7 : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t IDSerialBit6_0 : 7;
        uint8_t ErrorCheckBit3 : 1;
    } IDSerial;
    struct
    {
        uint8_t DataType : 4;
        uint8_t Reserved : 3;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t year : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t month : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t day : 7;
        uint8_t ErrorCheckBit3 : 1;
    } Date;
    struct
    {
        uint8_t DataType : 4;
        uint8_t Reserved : 3;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t hour : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t min : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t sec : 7;
        uint8_t ErrorCheckBit3 : 1;
    } Time;
    struct
    {
        uint8_t DataType : 4;
        uint8_t Data1Bit11_9 : 3;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t Data1Bit8_2 : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t Data2Bit11_7 : 5;
        uint8_t Data1Bit1_0 : 2;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t Data2Bit6_0 : 7;
        uint8_t ErrorCheckBit3 : 1;
    } BasicData;
}UREALTIMEDATA, *PUREALTIMEDATA;

typedef union UEventData
{
    struct
    {
        uint8_t Reserved1 : 2;
        uint8_t DataType : 5;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t IDBit23_21 : 3;
        uint8_t Reserved2 : 4;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t IDBit20_14 : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t IDBit13_7 : 7;
        uint8_t ErrorCheckBit3 : 1;

        uint8_t IDBit6_0 : 7;
        uint8_t ErrorCheckBit4 : 1;
    } ID;
    struct
    {
        uint8_t Reserved1 : 2;
        uint8_t DataType : 5;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t IDSerialBit23_21 : 3;
        uint8_t Reserved2 : 4;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t IDSerialBit20_14 : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t IDSerialBit13_7 : 7;
        uint8_t ErrorCheckBit3 : 1;

        uint8_t IDSerialBit6_0 : 7;
        uint8_t ErrorCheckBit4 : 1;
    } IDSerial;
    struct
    {
        uint8_t Reserved1 : 2;
        uint8_t DataType : 5;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t Reserved2 : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t year : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t month : 7;
        uint8_t ErrorCheckBit3 : 1;

        uint8_t day : 7;
        uint8_t ErrorCheckBit4 : 1;
    } Date;
    struct
    {
        uint8_t Reserved1 : 2;
        uint8_t DataType : 5;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t Reserved2 : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t hour : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t min : 7;
        uint8_t ErrorCheckBit3 : 1;

        uint8_t sec : 7;
        uint8_t ErrorCheckBit4 : 1;
    } Time;
    struct   //低通气、鼾声、气流受限、参数改变、阻塞性Apnea、中枢性Apnea、混合性Apnea事件
    {
        uint8_t TimeBit15_14 : 2;
        uint8_t DataType : 5;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t TimeBit13_7 : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t TimeBit6_0 : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t Duration : 7;
        uint8_t ErrorCheckBit3 : 1;

        uint8_t Reserved : 7;
        uint8_t ErrorCheckBit4: 1;
    } BreathEventData;
    struct   //关机事件
    {
        uint8_t TimeBit29_28 : 2;
        uint8_t DataType : 5;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t TimeBit27_21 : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t TimeBit20_14 : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t TimeBit13_7 : 7;
        uint8_t ErrorCheckBit3 : 1;

        uint8_t TimeBit6_0 : 7;
        uint8_t ErrorCheckBit4: 1;
    } PowerOffEventData;
    struct   //参数事件、开机事件
    {
        uint8_t SysParaVal1Bit9_8 : 2;
        uint8_t DataType : 5;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t SysParaVal1Bit7_1 : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t SysParaVal2Bit9_4 : 6;
        uint8_t SysParaVal1Bit0 : 1;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t SysParaVal3Bit9_7 : 3;
        uint8_t SysParaVal2Bit3_0 : 4;
        uint8_t ErrorCheckBit3 : 1;

        uint8_t SysParaVal3Bit6_0 : 7;
        uint8_t ErrorCheckBit4 : 1;
    } SysParaEventData;
    struct   //压力、漏气量数据
    {
        uint8_t TimeBit15_14 : 2;
        uint8_t DataType : 5;
        uint8_t ErrorCheckBit0 : 1;

        uint8_t TimeBit13_7 : 7;
        uint8_t ErrorCheckBit1 : 1;

        uint8_t TimeBit6_0 : 7;
        uint8_t ErrorCheckBit2 : 1;

        uint8_t DataBit11_7 : 5;
        uint8_t Reserved : 2;
        uint8_t ErrorCheckBit3 : 1;

        uint8_t DataBit6_0 : 7;
        uint8_t ErrorCheckBit4 : 1;
    } PressAndLeakEventData;
} UEVENT_BASE_DATA, *PUEVENT_BASE_DATA;
#endif

#pragma pack(1)
typedef struct TDateTime
{
    U8 Sec; //秒
    U8 Min; //分
    U8 Hour; //时
    U8 Day; //日
    U8 Mon; //月
    U8 Week; //周
    U8 Year; //年
}DATETIME, *PDATETIME;

typedef struct TDataSaveInfo
{
    U32 ID;                         //实时数据ID(日志数据共用)
    U16 Poweroff;
    U16 PowerOnTime;                //日志数据中的关机事件中开机时长,单位:s
    DATETIME DateTime;  //保存数据的起始日期和时间    
}DATASAVEINFO, *PDATASAVEINFO;

//14byte
typedef struct
{
	char cfType[2];         /* 文件类型, 必须为 "BM" (0x4D42)*/
	unsigned long cfSize;         /* 文件的大小(字节) */
	char cfReserved[4];     /* 保留, 必须为 0 */
	unsigned long cfoffBits;      /* 位图阵列相对于文件头的偏移量(字节)*/
}BITMAPFILEHEADER;       /* 文件头结构 */

//40byte
typedef struct
{
	unsigned long ciSize;         /* size of BITMAPINFOHEADER */
	unsigned long ciWidth;        /* 位图宽度(像素) */
	unsigned long ciHeight;       /* 位图高度(像素) */
	unsigned short ciPlanes;       /* 目标设备的位平面数, 必须置为1 */
	unsigned short ciBitCount;     /* 每个像素的位数, 1,4,8或24 */
	unsigned long ciCompress;     /* 位图阵列的压缩方法,0=不压缩 */
	unsigned long ciSizeImage;    /* 图像大小(字节) */
	unsigned long ciXPelsPerMeter;/* 目标设备水平每米像素个数 */
	unsigned long ciYPelsPerMeter;/* 目标设备垂直每米像素个数 */
	unsigned long ciClrUsed;      /* 位图实际使用的颜色表的颜色数 */
	unsigned long ciClrImportant; /* 重要颜色索引的个数 */
}BITMAPINFOHEADER;       /* 位图信息头结构 */
#pragma pack()

class CSdCardDataSave: public CMutexObj
{
public:
    CSdCardDataSave();
    ~CSdCardDataSave();

public:
    void RunTask(const PARAMETERCONFIG* pParameterConfig);
protected:
    void Reset();
public:
    U8 Mount(U8 SDInsertFlag);
    U8 ExsitDebugFile();
    void CreateRealAndEventFile();
    void WriteRealDataFile(U8 BaseIndex, U32 Len);
    void WriteEventDataFile(U8 BaseIndex, U32 Len);
    void WriteBaseDataFile(U8 BaseIndex, U32 Len);
#if (WIFI_NET_DEBUG == 1)
    void WriteNetDebugSendDataFile(U8 BaseIndex, U32 Len);
    void WriteNetDebugRcvDataFile(U8 BaseIndex, U32 Len);
#endif
    void CloseRealAndEventFile();
    void ExportEDFFile();
#if (ENABLE_SD_CHANGE_PARAMETER == 1)
    void ExportParameterConfig();
    U8 ImportParameterConfig(U8* pBuf);
#endif
    U8 GetSdCardConnectStatus();

	U8 GetFreeCapacityPercent();
    void PostAddDataEvent(EWRITEEVENTTYPE Event);

    void WriteDateTimeModifyRecord();
    void SaveDevCalibrateData();
protected:
    bool CanSaveDataToSDCard();
public:
    void AddRealData();
    U8 AddEventData(U8* pData, U8 Len);
#if (WIFI_NET_DEBUG == 1)
    U8 AddNetSendData(U8* pData, U16 Len);
    U8 AddNetRcvData(U8* pData, U16 Len);
#endif

protected:
    void DealRealDataBufferFull();
    void DealEventDataBufferFull(U8 *pData, U8 Len);
    void DealBaseDataBufferFull();
protected: //添加实时数据
    void AddId1RealData(U32 ID);
    void AddId2RealData(U32 ID);
    void AddDateRealData(U8 Year, U8 Month, U8 Day);
    void AddTimeRealData(U8 Hour, U8 Minute, U8 Second);

    void AddFileHead(U8 Type);      //0:Real Data 1:Event
    void AddRealDataHead();
    
public:
    void AddRealData(EREALTIMEDATATYPE Type, U16 Value1, U16 Value2);

protected: //添加事件数据
    void AddId1Event(U32 ID);
    void AddSerialEvent(U32 SN);
    void AddDateEvent(U8 Year, U8 Month, U8 Day);
    void AddTimeEvent(U8 Hour, U8 Minute, U8 Second);
    void AddEventHead();
    
    void AddEventChangeWorkMode(U8 WorkMode, U32 MotorOnTime);
protected://添加基线数据，包括流量基线与压力控制值
    void AddBaseHead();

public:
    void AddEvent(EEVENTDATATYPE EventType, U32 Value1, U32 Value2 = 0, U32 Value3 = 0, U8 First = 1);
    void AddBaseData(EEVENTDATATYPE BaseType, U32 Value1, U32 Value2 = 0, U32 Value3 = 0);
    void AddBaseData(U8* pData, U8 Len);
#if (ENABLE_CATCH_PICTURE == 1)
protected:
    void WriteBmp(FIL* pFil);
    void CatchBMPPicture();
#endif
    void SaveDateTimeModifyInfo();
protected:
    char m_EventFileName[64];
    const PARAMETERCONFIG* m_pParameterConfig;
	U16 m_U16AddDataEventValue[10];
	U8  m_U8AddDataEventIdx;

    DATETIME m_FileSavePath;        //文件目录用的时间，主要是用于考虑12：00前，存储至上一天
    DATASAVEINFO m_DataSaveInfo;

    U8 m_U8SdCardConnectFlag;	
    U8 m_U8SdCardIsFull;
    U8 m_U8CanSaveFlag;

    U8 m_U8DebugFile;               //是否有快速进入DEBUG调试界面文件

    FATFS m_FsHandle;
#if (WIFI_NET_DEBUG == 1)
    FIL m_FileHandle[FILE_HANDLE_NUM_MAX];
#else
    FIL m_FileHandle[FILE_HANDLE_NUM_MAX];
#endif

    UREALTIMEDATA m_RealTimeData;
    U32 m_U8RTDataSerialID;
    U8 m_U8RTDataBaseIndex;
    U16 m_U16RTDataInIndex;
    U8 m_U8RTDataBuf[2][REAL_DATA_SIZE]; //实时数据的两级缓冲区
    
#if (WIFI_NET_DEBUG == 1)
    U8 m_U8WifiDebugSendDataBaseIndex;
    U16 m_U16WifiDebugSendDataInIndex[2];
    U8 m_U8WifiDebugSendDataBuf[2][REAL_DATA_SIZE]; //网络数据发送的两级缓冲区
    
    U8 m_U8WifiDebugRcvDataBaseIndex;
    U16 m_U16WifiDebugRcvDataInIndex[2];
    U8 m_U8WifiDebugRcvDataBuf[2][REAL_DATA_SIZE]; //网络数据接收的两级缓冲区
#endif

    UEVENT_BASE_DATA m_EventData;
    U32 m_EventDataSerialID;
    U8 m_U8EventDataBaseIndex;
    U16 m_U16EventDataInIndex;
    U16 m_U16WriteEventLen;                    //本次写入的事件数据长度
    U8 m_U8EventDataBuf[2][EVENT_DATA_SIZE]; //日志数据的两级缓冲区    
    
    UEVENT_BASE_DATA m_BaseData;
    U32 m_BaseDataSerialID;
    U8 m_U8BaseDataBaseIndex;
    U16 m_U16BaseDataInIndex;
    U8 m_U8BaseDataBuf[2][BASE_DATA_SIZE]; //基线数据的两级缓冲区    
    
    U8 m_U8WifiFlowPresRealDataTicket;                 //流量压力每4个实时数据才发送一个,5HZ
    U8 m_U8WifiSpO2PRRealDataTicket;                   //血氧脉率每4个实时数据才发送一个,5HZ
    U8 m_U8R12BaseFlowRealDataTicket;                  //呼吸事件R12和流量基值加偏移每4个实时数据才发送一个,5HZ
    U8 m_U8FlowAddOffsetRealDataTicket;                //实时流量加偏移每4个实时数据才发送一个,5HZ
    U8 m_U8InspTimeLeakRealDataTicket;                 //进入非正常漏气高漏气后每4个实时数据才发送一个,5HZ
};

extern CSdCardDataSave g_SdCardDataSave;

extern OS_TCB   g_AppTaskSDCardDataSaveTCB;
extern CPU_STK  g_AppTaskSDCardDataSaveStk[APP_CFG_TASK_SDCARD_DATA_STK_SIZE];

void PostCatchScreenEvent(void);
void ViewRootDir(void);
void CreateNewFile(void);
#endif

