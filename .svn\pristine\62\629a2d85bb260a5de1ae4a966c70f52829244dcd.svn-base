#include "MonitorViewScreen.h"
#include "MainScreen.h"
#include "Led.h"
#include <stdio.h>
#include "Key.h"
#include "ConfigSave.h"
#include "MultiLanguage.h"
#include "GlobalVariable.h"
#include "StaterBar.h"
#include "QuickSettingsScreen.h"
#include "bsp_bldcm_control.h"
#include "WaveMonitorScreen.h"
#include "OxiModule.h"
#include "AppBMP.h"
#include "stack.h"

void RepaintMonitorViewScreen(void);
static void ChangeFocusMonitorViewScreen(void);
static void DrawMonitorViewScreen(void);
static void DrawBPAPMonitorViewScreen(void);
static void DrawCPAPAPAPMonitorViewScreen(void);
static void DrawHFMonitorViewScreen(void);
static void OnEnterClick_MonitorViewScreen(U8 ShortPress);
void MonitorViewScreenProcess(U8 Key);
static void ExitMonitorTimer();

WINDOWINFO g_MonitorViewScreenWInfo = {NULL, MonitorViewScreenProcess, ExitMonitorTimer, RepaintMonitorViewScreen, NULL, NULL, 0, 1, MONITORVIEW_ID};

static GUI_TIMER_HANDLE g_DrawRunningTimer = NULL;
static GUI_TIMER_HANDLE g_UnlockProgressUpdateTimer = NULL;

#if (LCD_TYPE == LCD_28_TFT)
    static uint8_t g_sU8OldMonitorViewScreenChange = 0;//�ı�
    uint8_t g_sU8MonitorViewScreenMidFocus = 0;//�м�
#else
    static uint8_t g_sU8MonitorViewScreenFocus  = 0;
#endif

static uint8_t g_sU8RunningIdx = 0;

#if (LCD_TYPE == LCD_5_TFT)
const GUI_RECT g_CPAPMonitorRect[9] =
{
    {55 + 0 * 280, 45 + STATEBAR_TITLE_HEIGHT, 200 + 0 * 280 - 1, 120 - 1 + STATEBAR_TITLE_HEIGHT},
    {45 + 1 * 280 - 2, 65 + STATEBAR_TITLE_HEIGHT, 200 + 1 * 280 - 1 + 2, 100 - 1 + STATEBAR_TITLE_HEIGHT},
    {55 + 2 * 280, 45 + STATEBAR_TITLE_HEIGHT, 200 + 2 * 280 - 1, 120 - 1 + STATEBAR_TITLE_HEIGHT},
    {55 + 0 * 280, 125 + STATEBAR_TITLE_HEIGHT, 200 + 0 * 280 - 1, 205 - 1 + STATEBAR_TITLE_HEIGHT},
    {25 + 1 * 280, 115 + STATEBAR_TITLE_HEIGHT, 210 + 1 * 280 - 1, 205 - 1 + STATEBAR_TITLE_HEIGHT},
    {55 + 2 * 280, 125 + STATEBAR_TITLE_HEIGHT, 200 + 2 * 280 - 1, 205 - 1 + STATEBAR_TITLE_HEIGHT},
    {55 + 0 * 280, 210 + STATEBAR_TITLE_HEIGHT, 200 + 0 * 280 - 1, 245 - 1 + STATEBAR_TITLE_HEIGHT},
    {45 + 1 * 280, 210 + STATEBAR_TITLE_HEIGHT, 200 + 1 * 280 - 1, 245 - 1 + STATEBAR_TITLE_HEIGHT},
    {55 + 2 * 280, 210 + STATEBAR_TITLE_HEIGHT, 200 + 2 * 280 - 1, 245 - 1 + STATEBAR_TITLE_HEIGHT}
};

static GUI_RECT g_sMenuRect = {4, 316 + STATEBAR_TITLE_HEIGHT, 195, 405 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sMenuStrMonitorRect = {80, 316 + STATEBAR_TITLE_HEIGHT, 195, 405 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sQuickMenuRect = {208, 316 + STATEBAR_TITLE_HEIGHT, 395, 405 + STATEBAR_TITLE_HEIGHT};

static GUI_RECT g_sCPAPMenuRect = {4, 316 + STATEBAR_TITLE_HEIGHT, 155, 405 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sCPAPMenuStrRect = {50, 316 + STATEBAR_TITLE_HEIGHT, 165, 405 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sCPAPQuickMenuRect = {164, 316 + STATEBAR_TITLE_HEIGHT, 315, 405 + STATEBAR_TITLE_HEIGHT};

static GUI_RECT g_sHFMenuRect = {4, 316 + STATEBAR_TITLE_HEIGHT, 395, 405 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sHFMenuStrRect = {170, 316 + STATEBAR_TITLE_HEIGHT, 285, 405 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sHFQuickMenuRect = {408, 316 + STATEBAR_TITLE_HEIGHT, 795, 405 + STATEBAR_TITLE_HEIGHT};
#else
const GUI_RECT g_CPAPMonitorRect[9] =
{
    {33 + 0 * 168 - 20, 26 + STATEBAR_TITLE_HEIGHT, 120 + 0 * 168 - 1, 80 - 1 + STATEBAR_TITLE_HEIGHT},
    {17 + 1 * 168, 43 + STATEBAR_TITLE_HEIGHT, 130 + 1 * 168 - 1, 66 - 1 + STATEBAR_TITLE_HEIGHT},
    {33 + 2 * 168, 26 + STATEBAR_TITLE_HEIGHT, 100 + 2 * 168 - 1, 80 - 1 + STATEBAR_TITLE_HEIGHT},
    {33 + 0 * 168 - 20, 83 + STATEBAR_TITLE_HEIGHT, 120 + 0 * 168 - 1, 136 - 1 + STATEBAR_TITLE_HEIGHT},
    {21 + 1 * 168, 76 + STATEBAR_TITLE_HEIGHT, 120 + 1 * 168 - 1, 136 - 1 + STATEBAR_TITLE_HEIGHT},
    {33 + 2 * 168 - 20, 83 + STATEBAR_TITLE_HEIGHT, 120 + 2 * 168 - 1, 136 - 1 + STATEBAR_TITLE_HEIGHT},
    {33 + 0 * 168, 140 + STATEBAR_TITLE_HEIGHT, 100 + 0 * 168 - 1, 163 - 1 + STATEBAR_TITLE_HEIGHT},
    {34 + 1 * 168, 140 + STATEBAR_TITLE_HEIGHT, 112 + 1 * 168 - 1, 163 - 1 + STATEBAR_TITLE_HEIGHT},
    {33 + 2 * 168, 140 + STATEBAR_TITLE_HEIGHT, 100 + 2 * 168 - 1, 163 - 1 + STATEBAR_TITLE_HEIGHT}
};

#if (LCD_TYPE != LCD_28_TFT)
static GUI_RECT g_sMenuRect = {2, 210 + STATEBAR_TITLE_HEIGHT, 117, 270 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sMenuStrMonitorRect = {48, 210 + STATEBAR_TITLE_HEIGHT, 117, 270 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sQuickMenuRect = {124, 210 + STATEBAR_TITLE_HEIGHT, 237, 270 + STATEBAR_TITLE_HEIGHT};

static GUI_RECT g_sCPAPMenuRect = {2, 210 + STATEBAR_TITLE_HEIGHT, 93, 270 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sCPAPMenuStrRect = {30, 210 + STATEBAR_TITLE_HEIGHT, 99, 270 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sCPAPQuickMenuRect = {98, 210 + STATEBAR_TITLE_HEIGHT, 192, 270 + STATEBAR_TITLE_HEIGHT};

static GUI_RECT g_sHFMenuRect = {2, 210 + STATEBAR_TITLE_HEIGHT, 237, 270 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sHFMenuStrRect = {102, 210 + STATEBAR_TITLE_HEIGHT, 171, 270 + STATEBAR_TITLE_HEIGHT};
static GUI_RECT g_sHFQuickMenuRect = {244, 210 + STATEBAR_TITLE_HEIGHT, 477, 270 + STATEBAR_TITLE_HEIGHT};
#endif
#endif

const ESTRINGIDX g_IdType_MonitorView[][12] =
{
    {
        PRESSUER_IDX, FLOW_IDX, LEAKAGE_IDX, IE_IDX, VT_IDX, BPM_IDX,
        INSPTIME_IDX, MV_IDX, SPO2_IDX, PR_IDX, MENU_IDX, QUICKSET_IDX
    },
    {
        BLANK_IDX, L_MIN_IDX, L_MIN_IDX, BLANK_IDX, ML_IDX, BPMUNIT_IDX,
        SEC_E_IDX, L_IDX, PERCENTAGE_IDX, BPMUNIT_IDX, BLANK_IDX, BLANK_IDX
    }
};

const ESTRINGIDX g_IdType_CPAPMonitorView[][6] =
{
    {LEAKAGE_IDX, PRESSUER_IDX, FLOW_IDX, BPM_IDX, SPO2_IDX, PR_IDX},
    {L_MIN_IDX, BLANK_IDX, L_MIN_IDX, BLANK_IDX, BLANK_IDX, BLANK_IDX}
};

#if (LCD_TYPE == LCD_28_TFT)
const ESTRINGIDX MonitorViewText_HF[][3] = {{SPO2_IDX, MONITOR_TEMP_IDX, PR_IDX}};//������ʾ

const ESTRINGIDX MonitorViewText_BPAP[][3] = {{FLOW_IDX, LEAKAGE_IDX, IE_IDX}//������ʾ
    , {VT_IDX, BPM_IDX,  INSPTIME_IDX}
    , {MV_2_IDX, SPO2_IDX,  PR_IDX}
};

const ESTRINGIDX MonitorViewText_CPAP[][3] = {{FLOW_IDX, LEAKAGE_IDX, BPM_IDX}//������ʾ
    , {SPO2_IDX, PR_IDX}
};

const ESTRINGIDX MonitorViewUnit_HF[][3] = {{PERCENTAGE_IDX, DEBUG_TEMPERATUREUNIT_IDX, BPMUNIT_IDX}};//��λ��ʾ

const ESTRINGIDX MonitorViewUnit_BPAP[][3] = {{L_MIN_IDX, L_MIN_IDX, BLANK_IDX}//��λ��ʾ
    , {ML_IDX, BPMUNIT_IDX, S_IDX}
    , {L_IDX, PERCENTAGE_IDX, BPMUNIT_IDX}
};

const ESTRINGIDX MonitorViewUnit_CPAP[][3] = {{L_MIN_IDX, L_MIN_IDX, BPMUNIT_IDX}//��λ��ʾ
    , {PERCENTAGE_IDX, BPMUNIT_IDX}
};

const U8 HopeMonitorViewScreenParameter[][11] = {{FLOW_VALUE, SPO2_VALUE, PIPELINE_TEMP_VALUE, PR_VALUE}//������ʾ
    , {PRESSURE_VALUE, FLOW_VALUE, LEAK_VALUE, RR_VALUE, SPO2_VALUE, PR_VALUE}
    , {PRESSURE_VALUE, FLOW_VALUE, LEAK_VALUE, IE_RATE_VALUE, TV_VALUE, RR_VALUE, INSPTIME_VALUE, MV_VALUE, SPO2_VALUE, PR_VALUE}
};
#else
const ESTRINGIDX g_IdType_HFMonitorView[] = {FLOW_IDX, SPO2_IDX, MONITOR_TEMP_IDX, PR_IDX, MONITOR_MODE_IDX};
const ESTRINGIDX g_IdType_HFMonitorViewUnit[] = {L_MIN_IDX, PERCENTAGE_IDX, DEBUG_TEMPERATUREUNIT_IDX, BPMUNIT_IDX, BLANK_IDX};
#endif

void MonitorViewScreenProcess(U8 Key)
{
    switch(Key)
    {
        case GUI_KEY_BACKTAB:
            g_MonitorViewScreenWInfo.U8OldFocusID = g_MonitorViewScreenWInfo.U8CurFocusID;
#if (LCD_TYPE == LCD_28_TFT)
            if(g_MonitorViewScreenWInfo.U8CurFocusID > 0)
            {
                g_MonitorViewScreenWInfo.U8CurFocusID--;
            }
            else
            {
                g_MonitorViewScreenWInfo.U8CurFocusID = 2;
            }
#else
            g_MonitorViewScreenWInfo.U8CurFocusID = !g_MonitorViewScreenWInfo.U8CurFocusID;
#endif
            ChangeFocusMonitorViewScreen();
            break;
        case GUI_KEY_TAB:
            g_MonitorViewScreenWInfo.U8OldFocusID = g_MonitorViewScreenWInfo.U8CurFocusID;
#if (LCD_TYPE == LCD_28_TFT)
            if(g_MonitorViewScreenWInfo.U8CurFocusID < 2)
            {
                g_MonitorViewScreenWInfo.U8CurFocusID++;
            }
            else
            {
                g_MonitorViewScreenWInfo.U8CurFocusID = 0;
            }
#else
            g_MonitorViewScreenWInfo.U8CurFocusID = !g_MonitorViewScreenWInfo.U8CurFocusID;
#endif
            ChangeFocusMonitorViewScreen();
            break;
        case GUI_KEY_ENTER:
#if (LCD_TYPE == LCD_28_TFT)
            if(g_MonitorViewScreenWInfo.U8CurFocusID == 1)
            {
                if(g_sU8MonitorViewScreenMidFocus > 0)
                {
                    g_sU8MonitorViewScreenMidFocus --;
                    if(g_sU8MonitorViewScreenMidFocus == 0)
                    {
                        g_sU8OldMonitorViewScreenChange = 1;
                    }
                }
                else
                {
                    if(!g_U8IsSleepVentilator && g_ConfigSave.GetParameter(WORKMODE) != SYS_WM_HIGHFLOW)
                    {
                        g_sU8MonitorViewScreenMidFocus = 4;
                    }
                    else
                    {
                        g_sU8MonitorViewScreenMidFocus = 2;
                    }
                    g_sU8OldMonitorViewScreenChange = 1;
                }
            }
            if(g_MonitorViewScreenWInfo.U8CurFocusID == 2)
            {
                if(!g_U8IsSleepVentilator && g_ConfigSave.GetParameter(WORKMODE) != SYS_WM_HIGHFLOW)
                {
                    if(g_sU8MonitorViewScreenMidFocus < 4)
                    {
                        g_sU8MonitorViewScreenMidFocus ++;
                        if(g_sU8MonitorViewScreenMidFocus == 1 || g_sU8MonitorViewScreenMidFocus == 4)
                        {
                            g_sU8OldMonitorViewScreenChange = 1;
                        }
                    }
                    else
                    {
                        g_sU8MonitorViewScreenMidFocus = 0;
                        g_sU8OldMonitorViewScreenChange = 1;
                    }
                }
                else
                {
                    if(g_sU8MonitorViewScreenMidFocus < 2)
                    {
                        g_sU8MonitorViewScreenMidFocus ++;
                        if(g_sU8MonitorViewScreenMidFocus == 1 || g_sU8MonitorViewScreenMidFocus == 2)
                        {
                            g_sU8OldMonitorViewScreenChange = 1;
                        }
                    }
                    else
                    {
                        g_sU8MonitorViewScreenMidFocus = 0;
                        g_sU8OldMonitorViewScreenChange = 1;
                    }
                }
            }
            if(g_MonitorViewScreenWInfo.U8CurFocusID == 0)
            {
                OnEnterClick_MonitorViewScreen(1);
            }
            else
            {
                if(g_sU8OldMonitorViewScreenChange != 1)
                {
                    g_sU8OldMonitorViewScreenChange = 2;
                }
                ChangeFocusMonitorViewScreen();
            }
#else
            OnEnterClick_MonitorViewScreen(1);
#endif
            break;
        case GUI_KEY_F1:
            OnEnterClick_MonitorViewScreen(0);
            break;
        default:
            break;
    }
}

static void ExitMonitorTimer()
{
    if(g_U8IsSleepVentilator == FALSE)
    {
        GUI_TIMER_DELETE(g_DrawRunningTimer);
    }
    GUI_TIMER_DELETE(g_UnlockProgressUpdateTimer);
}

#if (LCD_TYPE == LCD_28_TFT)
static void DrawRunning(void)
{
    GUI_TIMER_Restart(g_DrawRunningTimer);
    if(!g_WindowDrv.ShowHaveMonitorView())
    {
        return;
    }
    if(GetBldcmStatus() != MOTOR_NORMAL_RUN_STATE || g_sU8MonitorViewScreenMidFocus != 0)
    {
        WM_SelectWindow(WM_HBKWIN);
        if(g_sU8MonitorViewScreenMidFocus == 0)
        {
            GUI_SetColor(FONTCOLOR_DEFAULT);//���ñ����ɫ
            GUI_AA_FillRoundedRect(208, 46 + STATEBAR_TITLE_HEIGHT, 215, 146 + STATEBAR_TITLE_HEIGHT, 2); //ѹ����
        }
    }
    else
    {
        WM_SelectWindow(WM_HBKWIN);
        g_sU8RunningIdx = (g_sU8RunningIdx + 1) % 10;
        GUI_SetColor(FONTCOLOR_DEFAULT);//���ñ����ɫ
        GUI_AA_FillRoundedRect(208, 46 + STATEBAR_TITLE_HEIGHT, 215, 146 + STATEBAR_TITLE_HEIGHT, 2); //ѹ����
        GUI_SetColor(GREEN);//���ñ����ɫ
        GUI_AA_FillRoundedRect(208, 146 - (g_sU8RunningIdx + 1) * 10 + STATEBAR_TITLE_HEIGHT, 215, 146 + STATEBAR_TITLE_HEIGHT,
                        2);
    }
    if(g_ConfigSave.GetParameter(WORKMODE) >= SYS_WM_S)
    {
        RefreshCPAPParameterValue(IE_STATUS_VALUE);
    }
}
#else
static void DrawRunning(void)
{
    U16 sU16Running1BmpIdx = BMP_RUNNING1;
    U16 sU16Running2BmpIdx = BMP_RUNNING2;
    U8 i;
    GUI_TIMER_Restart(g_DrawRunningTimer);
    if(GetBldcmStatus() != MOTOR_NORMAL_RUN_STATE || g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_HIGHFLOW)
    {
        return;
    }
    if(!g_WindowDrv.ShowHaveMonitorView())
    {
        return;
    }
    WM_SelectWindow(WM_HBKWIN);
    g_sU8RunningIdx = (g_sU8RunningIdx + 1) % 10;
    for(i = 0; i < 12; i++)
    {
        if((i == g_sU8RunningIdx) || (i == ((g_sU8RunningIdx + 1) % 12)) || (i == ((g_sU8RunningIdx + 2) % 12)))
        {
#if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16Running1BmpIdx, 45 + i * 25, 311 - 65 + STATEBAR_TITLE_HEIGHT);
#else
            DrawBMP(sU16Running1BmpIdx, 27 + i * (13 + 2), 207 - 43 + STATEBAR_TITLE_HEIGHT);
#endif
        }
        else
        {
#if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16Running2BmpIdx, 45 + i * 25, 311 - 65 + STATEBAR_TITLE_HEIGHT);
#else
            DrawBMP(sU16Running2BmpIdx, 27 + i * (13 + 2), 207 - 43 + STATEBAR_TITLE_HEIGHT);
#endif
        }
    }
}
#endif
static void UnlockProgressUpdateTask(void)
{
    static U8 sU8OldPrecent = 0;
    U8 sU8CurPrecent = 0;
#if (LCD_TYPE != LCD_28_TFT)
    U16 sU16OldX = 0;
    U16 sU16NewX = 0;
    GUI_RECT rect;
#endif
    GUI_TIMER_Restart(g_UnlockProgressUpdateTimer);
#if (LCD_TYPE == LCD_28_TFT)
    if(g_MonitorViewScreenWInfo.U8CurFocusID != 0 || g_WindowDrv.ShowIsMonitorView() == false
                    || (g_ConfigSave.GetMarketAreaType() == MARKET_AREA_FDA))
    {
        sU8OldPrecent = 0;
        clear_key_long_pressed();
#if (LCD_TYPE == LCD_28_TFT)
        if(GetAllTipMarker() == 0)
        {
            GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
            GUI_FillRect(60, Return_Y - 32 + STATEBAR_TITLE_HEIGHT, 60 + 120 * LONG_PRESS_TIME / LONG_PRESS_TIME,
                            Return_Y - 27 + STATEBAR_TITLE_HEIGHT);
        }
#endif
        return;
    }
#else
    if(g_MonitorViewScreenWInfo.U8CurFocusID  != 1 || g_WindowDrv.ShowIsMonitorView() == false
                    || (g_ConfigSave.GetMarketAreaType() == MARKET_AREA_FDA))
    {
        sU8OldPrecent = 0;
        return;
    }
#endif
    sU8CurPrecent = 100 * Get_KeyLongPressed() / LONG_PRESS_TIME;
    if(sU8OldPrecent != sU8CurPrecent)
    {
        WM_SelectWindow(WM_HBKWIN);
        GUI_SetTextMode(GUI_TM_TRANS);  //���ñ���͸��
#if (LCD_TYPE == LCD_28_TFT)
        GUI_SetColor(UNLOCK_PROGRESS_BACKCOLOR);
        GUI_FillRect(60, Return_Y - 32 + STATEBAR_TITLE_HEIGHT, 60 + 120 * Get_KeyLongPressed() / LONG_PRESS_TIME,
                        Return_Y - 27 + STATEBAR_TITLE_HEIGHT);
#else
#if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
#else
        GUI_SetFont(FONT_24);
#endif
        if(g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_HIGHFLOW)
        {
            rect = g_sHFQuickMenuRect;
        }
        else
        {
            if(g_U8IsSleepVentilator)
            {
                rect = g_sCPAPQuickMenuRect;
            }
            else
            {
                rect = g_sQuickMenuRect;
            }
        }
        GUI_MEMDEV_Handle hMem = GUI_MEMDEV_Create(rect.x0, rect.y0, rect.x1 - rect.x0, rect.y1 - rect.y0);
        GUI_MEMDEV_Select(hMem);
        if(sU8CurPrecent == 0)
        {
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);//���ñ����ɫMAINSCREEN_MENU_BACKCOLOR_DEFAULT
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
        }
        else
        {
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);//���ñ����ɫMAINSCREEN_MENU_BACKCOLOR_DEFAULT
            GUI_FillRect(rect.x0, rect.y0, rect.x1, rect.y1);
            sU16OldX = rect.x0 + sU8OldPrecent * (rect.x1 - rect.x0) / 100;
            sU16NewX = sU16OldX + (sU8CurPrecent - sU8OldPrecent) * (rect.x1 - rect.x0) / 100;
            GUI_SetColor(UNLOCK_PROGRESS_BACKCOLOR);//���ñ����ɫMAINSCREEN_MENU_BACKCOLOR_DEFAULT
//            GUI_FillRect(sU16OldX, rect.y0, sU16NewX, rect.y1);
            GUI_FillRect(rect.x0, rect.y0, sU16NewX, rect.y1);
        }
        sU8OldPrecent = sU8CurPrecent;
        GUI_SetColor(GUI_WHITE);
        GUI_DispStringInRectWrap(GetMultiLanguageString(QUICKSET_IDX), &rect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                        GUI_WRAPMODE_WORD);
        GUI_MEMDEV_Select(0);
        GUI_MEMDEV_CopyToLCDAt(hMem, rect.x0, rect.y0);
        GUI_MEMDEV_Delete(hMem);
        sU8OldPrecent = sU8CurPrecent;
#endif
    }
}

static void CreateRunningTimer(void)
{
    GUI_TIMER_CREATE(g_DrawRunningTimer, DrawRunning, 20000, 0, 0);
    GUI_TIMER_SetPeriod(g_DrawRunningTimer, (GUI_TIMER_TIME)(500 / (1000 / OS_CFG_TICK_RATE_HZ)));
    GUI_TIMER_Restart(g_DrawRunningTimer);
}

static void CreateUnlockTimer(void)
{
    GUI_TIMER_CREATE(g_UnlockProgressUpdateTimer, UnlockProgressUpdateTask, 20001, 0, 0);
    GUI_TIMER_SetPeriod(g_UnlockProgressUpdateTimer, (GUI_TIMER_TIME)(100 / (1000 / OS_CFG_TICK_RATE_HZ)));
    GUI_TIMER_Restart(g_UnlockProgressUpdateTimer);
}

void RepaintMonitorViewScreen(void)
{
    DrawMonitorViewScreen();
    SetShowParameterType(
                    SHOW_VIEW_MONITOR);    //��ShowMonitorViewScreen�����е��ã��ƶ����ػ溯�������ñ�־���Ǳ��ⴰ���ڿ����л�ʱ�����¶�ʱ�������½����ȵ��ã����½�����в�Ӱ
}

static void DrawMonitorViewScreen(void)
{
    WM_SelectWindow(WM_HBKWIN);
#if (LCD_TYPE == LCD_28_TFT)
    GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);//���ý��汳��ɫ
#else
    GUI_SetColor(BACKCOLOR_DEFAULT);//���ý��汳��ɫ-��ɫ(0, 0, 0)
#endif
    GUI_FillRect(0, STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH, MAINSCREEN_HEIGHT + STATEBAR_TITLE_HEIGHT);
    GUI_SetTextMode(GUI_TM_TRANS);  //���ñ���͸��
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_32);
#else
    GUI_SetFont(FONT_24);
#endif
    if(g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_HIGHFLOW)
    {
        DrawHFMonitorViewScreen();
    }
    else
    {
        if(g_U8IsSleepVentilator)
        {
            DrawCPAPAPAPMonitorViewScreen();
        }
        else
        {
            DrawBPAPMonitorViewScreen();
        }
    }
}

#if (LCD_TYPE == LCD_28_TFT)
static void DrawHFMonitorViewScreen(void)
{
    GUI_RECT rect;
    uint8_t i;
    char str[10];
    if(g_sU8MonitorViewScreenMidFocus == 0)
    {
        GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);//���ñ����ɫ
        GUI_AA_FillRoundedRect(10, 10 + STATEBAR_TITLE_HEIGHT, 230, 180 + STATEBAR_TITLE_HEIGHT, 30);
        GUI_SetColor(FONTCOLOR_DEFAULT);//��ɫ
        rect.x0 = 20;
        rect.y0 = 20 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 110;
        rect.y1 = 50 + STATEBAR_TITLE_HEIGHT;
        GUI_DispStringInRectWrap(GetMultiLanguageString(FLOW_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
        rect.x0 = 110;
        rect.y0 = 150 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 190;
        rect.y1 = 180 + STATEBAR_TITLE_HEIGHT;
        GUI_DispStringInRectWrap(GetMultiLanguageString(L_MIN_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
        GUI_AA_FillRoundedRect(208, 46 + STATEBAR_TITLE_HEIGHT, 215, 146 + STATEBAR_TITLE_HEIGHT, 2);
        GUI_SetFont(FONT_50);
        rect.x0 = 60;
        rect.y0 = 60 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 180;
        rect.y1 = 140 + STATEBAR_TITLE_HEIGHT;
        GetValueStr(HopeMonitorViewScreenParameter[0][0], str);
        GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
    }
    else if(g_sU8MonitorViewScreenMidFocus == 1)
    {
        GUI_SetFont(FONT_18);
        for(i = 0; i < 3; i++)
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
            if((i == 0 || i == 2) && g_OxiModule.GetConnectStatus() == 0)
            {
                GUI_SetColor(GUI_GRAY);
            }
            else
            {
                GUI_SetColor(GUI_WHITE);
            }
            rect.x0 = 15;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 90;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_HF[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 180;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 225;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_HF[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 100;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 140;
            rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GetValueStr(HopeMonitorViewScreenParameter[0][1 + i], str);
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
    }
    else
    {
        GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
        GUI_AA_FillRoundedRect(5, 10 + STATEBAR_TITLE_HEIGHT, 235, 60 + STATEBAR_TITLE_HEIGHT, 20);
        SetUiItemText(15
                        , 28 + STATEBAR_TITLE_HEIGHT
                        , MODE_IDX
                        , GUI_WHITE
                        , FONT_18
                        , FONT_18
                        , FALSE, FALSE);
    }
    DrawPageCircle(g_sU8MonitorViewScreenMidFocus, 3);
    for(i = 0; i < 3; i++)
    {
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
        GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 22);
        if(i == g_MonitorViewScreenWInfo.U8CurFocusID)
        {
            GUI_SetColor(GREEN);
            GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 20);
            if(i == 0)
            {
                DrawBMP(BMP_MENUFOCU + 2 * i, 65 + 45 * i, Return_Y - 7 + STATEBAR_TITLE_HEIGHT);
            }
            else
            {
                DrawBMP(BMP_MENUFOCU + 2 * i, 68 + 45 * i, Return_Y - 11 + STATEBAR_TITLE_HEIGHT);
            }
        }
        else
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 20);
            if(i == 0)
            {
                DrawBMP(BMP_MENU + 2 * i, 65 + 45 * i, Return_Y - 7 + STATEBAR_TITLE_HEIGHT);
            }
            else
            {
                DrawBMP(BMP_MENU + 2 * i, 68 + 45 * i, Return_Y - 11 + STATEBAR_TITLE_HEIGHT);
            }
        }
    }
}
#else
static void DrawHFMonitorViewScreen(void)
{
    static const U8 MonitorViewScreenParameter[] = {FLOW_VALUE, SPO2_VALUE, PIPELINE_TEMP_VALUE, PR_VALUE, WORK_MODE_VALUE};
    uint8_t sU8Focus[2] = {0};
    GUI_RECT rect;
    uint8_t i;
    char str[10];
    GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
#if (LCD_TYPE == LCD_5_TFT)
    GUI_FillRect(0, 378 - TOP_STATERBAR_HEIGHT + STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH,
                    378 - TOP_STATERBAR_HEIGHT + 4 - 1 + STATEBAR_TITLE_HEIGHT);
    GUI_FillRect(SCREEN_WIDTH >> 1, ((378 - TOP_STATERBAR_HEIGHT) >> 1) + STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH,
                    ((378 - TOP_STATERBAR_HEIGHT) >> 1) + 4 - 1 + STATEBAR_TITLE_HEIGHT);
    for(i = 0; i < 2; i++)
    {
        GUI_FillRect(SCREEN_WIDTH / 4 * (3 - i), 0 + STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH / 4 * (3 - i) + 4 - 1,
                        (378 - TOP_STATERBAR_HEIGHT) + (SCREEN_HEIGHT - 378) * i - 1 + STATEBAR_TITLE_HEIGHT);
    }
#else
    GUI_FillRect(0, 252 - TOP_STATERBAR_HEIGHT + STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH,
                    252 - TOP_STATERBAR_HEIGHT + 2 - 1 + STATEBAR_TITLE_HEIGHT);
    GUI_FillRect(SCREEN_WIDTH >> 1, ((252 - TOP_STATERBAR_HEIGHT) >> 1) + STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH,
                    ((252 - TOP_STATERBAR_HEIGHT) >> 1) + 2 - 1 + STATEBAR_TITLE_HEIGHT);
    for(i = 0; i < 2; i++)
    {
        GUI_FillRect(SCREEN_WIDTH / 4 * (3 - i), 0 + STATEBAR_TITLE_HEIGHT, SCREEN_WIDTH / 4 * (3 - i) + 2 - 1,
                        (252 - TOP_STATERBAR_HEIGHT) + (SCREEN_HEIGHT - 252) * i - 1 + STATEBAR_TITLE_HEIGHT);
    }
#endif
    sU8Focus[0] = g_MonitorViewScreenWInfo.U8CurFocusID ;
    sU8Focus[1] = g_MonitorViewScreenWInfo.U8OldFocusID;
    for(i = 0; i < 2; i++)
    {
        if(i == 0)
        {
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);    //���ñ����ɫ
        }
        else
        {
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_DEFAULT);
        }
        if(sU8Focus[i] == 0)
        {
            GUI_FillRect(g_sHFMenuRect.x0, g_sHFMenuRect.y0, g_sHFMenuRect.x1, g_sHFMenuRect.y1);
        }
        else
        {
            GUI_FillRect(g_sHFQuickMenuRect.x0, g_sHFQuickMenuRect.y0, g_sHFQuickMenuRect.x1, g_sHFQuickMenuRect.y1);
        }
    }
    GUI_SetColor(GUI_WHITE);
    GUI_DispStringInRectWrap(GetMultiLanguageString(MENU_IDX), &g_sHFMenuStrRect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                    GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
    DrawMenuIcon(146, 416 - 70 + STATEBAR_TITLE_HEIGHT);
#else
    DrawMenuIcon(87, 277 - 46 + STATEBAR_TITLE_HEIGHT);
#endif
    GUI_DispStringInRectWrap(GetMultiLanguageString(QUICKSET_IDX), &g_sHFQuickMenuRect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                    GUI_WRAPMODE_CHAR);
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_24);
#else
    GUI_SetFont(FONT_18);
#endif
    for(i = 0; i < 5; i++)
    {
#if (LCD_TYPE == LCD_5_TFT)
        if(i == 0)
        {
            rect.x0 = 800 / 4 * i + 4 + 4;
            rect.y0 = 0 + 5 + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 800 / 4 * (i + 2) - 4 - 1 - 20;
            rect.y1 = (378 - 70) - 1 - 50 + STATEBAR_TITLE_HEIGHT;
        }
        else if(i >= 1 && i <= 2)
        {
            rect.x0 = 800 / 4 * (i + 1) + 4 + 4;
            //rect.y0 = 0 + 5 + (378 - 70) / 2 * i;
            rect.y0 = 0 + 5 + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 800 / 4 * (i + 2) - 4 - 1 - 20;
            //rect.y1 = (378 - 70) / 2 - 1 - 20 + (378 - 70) / 2 * i;
            rect.y1 = (378 - 70) / 2 - 1 - 20 + STATEBAR_TITLE_HEIGHT;
        }
        else
        {
            rect.x0 = 800 / 4 * (i - 1) + 4 + 4;
            rect.y0 = 0 + 5 + (378 - 70) / 2 + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 800 / 4 * i - 4 - 1 - 20;
            rect.y1 = 378 - 70 - 1 - 20 + STATEBAR_TITLE_HEIGHT;
        }
#else
        if(i == 0)
        {
            rect.x0 = 480 / 4 * i + 2 + 2;
            rect.y0 = 0 + 3 + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 480 / 4 * (i + 2) - 2 - 1 - 12;
            rect.y1 = (252 - 46) - 1 - 33 + STATEBAR_TITLE_HEIGHT;
        }
        else if(i >= 1 && i <= 2)
        {
            rect.x0 = 480 / 4 * (i + 1) + 2 + 2;
            rect.y0 = 0 + 3 + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 480 / 4 * (i + 2) - 2 - 1 - 12;
            rect.y1 = (252 - 46) / 2 - 1 - 13 + STATEBAR_TITLE_HEIGHT;
        }
        else
        {
            rect.x0 = 480 / 4 * (i - 1) + 2 + 2;
            rect.y0 = 0 + 3 + (252 - 46) / 2 + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 480 / 4 * i - 2 - 1 - 12;
            rect.y1 = 252 - 46 - 1 - 13 + STATEBAR_TITLE_HEIGHT;
        }
#endif
        if(((g_IdType_HFMonitorView[i] == SPO2_IDX) || (g_IdType_HFMonitorView[i] == PR_IDX))
                        && g_OxiModule.GetConnectStatus() == 0)
        {
            GUI_SetColor(GUI_GRAY);
        }
        else
        {
            GUI_SetColor(GUI_WHITE);
        }
#if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
#else
        GUI_SetFont(FONT_24);
#endif
        GUI_DispStringInRect(GetMultiLanguageString(g_IdType_HFMonitorView[i])
                        , &rect, GUI_TA_LEFT | GUI_TA_TOP);
        //if (g_IdType_HFMonitorView[i] != FLOW_IDX || g_IdType_HFMonitorView[i] != MONITOR_MODE_IDX)
        if(i >= 1 && i < 4)
        {
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_24);
#else
            GUI_SetFont(FONT_18);
#endif
            GUI_DispStringInRectWrap(GetMultiLanguageString(g_IdType_HFMonitorViewUnit[i]), &rect, GUI_TA_RIGHT | GUI_TA_TOP,
                            GUI_WRAPMODE_WORD);
        }
        GUI_SetFont(FONT_38);
        GetValueStr(MonitorViewScreenParameter[i], str);
        //SetUiNumberText(str, 100, 10);
        if(i == 0)
        {
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_100);
            rect.y0 += 50;
#else
            GUI_SetFont(FONT_100);
            rect.y0 += 33;
#endif
            //��ʾ��ֵ
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
#else
            GUI_SetFont(FONT_18);
            rect.y0 += (70 - 33);
            rect.y1 += 20;
#endif
            //��ʾ��λ L/min
            GUI_DispStringInRectWrap(GetMultiLanguageString(L_MIN_IDX), &rect, GUI_TA_RIGHT | GUI_TA_BOTTOM, GUI_WRAPMODE_WORD);
        }
        else if(i == 4)
        {
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_38);
#else
            GUI_SetFont(FONT_24);
#endif
#if (LCD_TYPE == LCD_35_TFT)
            rect.y0 += 25;
#endif
            GUI_DispStringInRect(GetMultiLanguageString(IdType_WorkMode[g_ConfigSave.GetParameter(WORKMODE)])
                            , &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
        }
        else
        {
            rect.x0 -= 2;
            if(g_ConfigSave.GetParameter(LANGUAGE) > LANG_ENGLISH)
            {
#if (LCD_TYPE == LCD_35_TFT)
                rect.y0 += 50;
#elif (LCD_TYPE == LCD_5_TFT)
                rect.y0 += 75;
#endif
            }
            else
            {
#if (LCD_TYPE == LCD_35_TFT)
                rect.y0 += 25;
#elif (LCD_TYPE == LCD_5_TFT)
                rect.y0 += 50;
#endif
            }
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);//��ʾ��ֵ
        }
    }
}
#endif

#if (LCD_TYPE == LCD_28_TFT)
static void DrawBPAPMonitorViewScreen(void)
{
    GUI_RECT rect;
    uint8_t i;
    char str[10];
    if(g_sU8MonitorViewScreenMidFocus == 0)
    {
        GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);//���ñ����ɫ
        GUI_AA_FillRoundedRect(10, 10 + STATEBAR_TITLE_HEIGHT, 230, 180 + STATEBAR_TITLE_HEIGHT, 30);
        GUI_SetColor(FONTCOLOR_DEFAULT);//���ñ����ɫ
        rect.x0 = 20;
        rect.y0 = 20 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 110;
        rect.y1 = 50 + STATEBAR_TITLE_HEIGHT;
        GUI_DispStringInRectWrap(GetMultiLanguageString(PRESSUER_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
        rect.x0 = 110;
        rect.y0 = 150 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 190;
        rect.y1 = 180 + STATEBAR_TITLE_HEIGHT;
        GUI_DispStringInRectWrap(GetMultiLanguageString(IdType_PressureUnits[g_ConfigSave.GetParameter(PRESSUNIT)]), &rect,
                        GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
        GUI_AA_FillRoundedRect(208, 46 + STATEBAR_TITLE_HEIGHT, 215, 146 + STATEBAR_TITLE_HEIGHT, 2);
        GUI_SetFont(FONT_50);
        rect.x0 = 60;
        rect.y0 = 60 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 180;
        rect.y1 = 140 + STATEBAR_TITLE_HEIGHT;
        GetValueStr(HopeMonitorViewScreenParameter[2][0], str);
        GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
    }
    else if(g_sU8MonitorViewScreenMidFocus == 1)
    {
        GUI_SetFont(FONT_18);
        for(i = 0; i < 3; i++)
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
            GUI_SetColor(FONTCOLOR_DEFAULT);
            rect.x0 = 15;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 90;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 180;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 225;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 100;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 140;
            rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
            if(HopeMonitorViewScreenParameter[2][1 + i] == LEAK_VALUE && RealDispLeakOverThreshold())
            {
                GUI_SetColor(GUI_RED);
            }
            else
            {
                GUI_SetColor(GUI_WHITE);
            }
            GetValueStr(HopeMonitorViewScreenParameter[2][1 + i], str);
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
    }
    else if(g_sU8MonitorViewScreenMidFocus == 2)
    {
        for(i = 0; i < 3; i++)
        {
            GUI_SetFont(FONT_18);
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
            GUI_SetColor(FONTCOLOR_DEFAULT);
            rect.x0 = 15;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 90;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 180;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 225;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 100;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 140;
            rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GetValueStr(HopeMonitorViewScreenParameter[2][4 + i], str);
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
    }
    else if(g_sU8MonitorViewScreenMidFocus == 3)
    {
        for(i = 0; i < 3; i++)
        {
            GUI_SetFont(FONT_18);
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
            if((i == 2 || i == 1) && g_OxiModule.GetConnectStatus() == 0)
            {
                GUI_SetColor(GUI_GRAY);
            }
            else
            {
                GUI_SetColor(GUI_WHITE);
            }
            rect.x0 = 15;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 90;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 180;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 225;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 100;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 140;
            rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GetValueStr(HopeMonitorViewScreenParameter[2][7 + i], str);
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
    }
    else
    {
        GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
        GUI_AA_FillRoundedRect(5, 10 + STATEBAR_TITLE_HEIGHT, 235, 60 + STATEBAR_TITLE_HEIGHT, 20);
        SetUiItemText(15
                        , 28 + STATEBAR_TITLE_HEIGHT
                        , MODE_IDX
                        , GUI_WHITE
                        , FONT_18
                        , FONT_18
                        , FALSE, FALSE);
    }
    DrawPageCircle(g_sU8MonitorViewScreenMidFocus, 5);
    for(i = 0; i < 3; i++)
    {
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
        GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 22);
        if(i == g_MonitorViewScreenWInfo.U8CurFocusID)
        {
            GUI_SetColor(GREEN);
            GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 20);
            if(i == 0)
            {
                DrawBMP(BMP_MENUFOCU + 2 * i, 65 + 45 * i, Return_Y - 7 + STATEBAR_TITLE_HEIGHT);
            }
            else
            {
                DrawBMP(BMP_MENUFOCU + 2 * i, 68 + 45 * i, Return_Y - 11 + STATEBAR_TITLE_HEIGHT);
            }
        }
        else
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 20);
            if(i == 0)
            {
                DrawBMP(BMP_MENU + 2 * i, 65 + 45 * i, Return_Y - 7 + STATEBAR_TITLE_HEIGHT);
            }
            else
            {
                DrawBMP(BMP_MENU + 2 * i, 68 + 45 * i, Return_Y - 11 + STATEBAR_TITLE_HEIGHT);
            }
        }
    }
}

static void DrawCPAPAPAPMonitorViewScreen(void)
{
    GUI_RECT rect;
    uint8_t i;
    char str[10];
    if(g_sU8MonitorViewScreenMidFocus == 0)
    {
        GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);//���ñ����ɫ
        GUI_AA_FillRoundedRect(10, 10 + STATEBAR_TITLE_HEIGHT, 230, 180 + STATEBAR_TITLE_HEIGHT, 30);
        GUI_SetColor(FONTCOLOR_DEFAULT);//���ñ����ɫ
        rect.x0 = 20;
        rect.y0 = 20 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 110;
        rect.y1 = 50 + STATEBAR_TITLE_HEIGHT;
        GUI_DispStringInRectWrap(GetMultiLanguageString(PRESSUER_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
        rect.x0 = 110;
        rect.y0 = 150 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 190;
        rect.y1 = 180 + STATEBAR_TITLE_HEIGHT;
        GUI_DispStringInRectWrap(GetMultiLanguageString(IdType_PressureUnits[g_ConfigSave.GetParameter(PRESSUNIT)]), &rect,
                        GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
        GUI_AA_FillRoundedRect(208, 46 + STATEBAR_TITLE_HEIGHT, 215, 146 + STATEBAR_TITLE_HEIGHT, 2);
        GUI_SetFont(FONT_50);
        rect.x0 = 60;
        rect.y0 = 60 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 180;
        rect.y1 = 140 + STATEBAR_TITLE_HEIGHT;
        GetValueStr(HopeMonitorViewScreenParameter[1][0], str);
        GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
    }
    else if(g_sU8MonitorViewScreenMidFocus == 1)
    {
        GUI_SetFont(FONT_18);
        for(i = 0; i < 3; i++)
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
            GUI_SetColor(FONTCOLOR_DEFAULT);
            rect.x0 = 15;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 90;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_CPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 180;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 225;
            rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
            GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_CPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                            GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            rect.x0 = 100;
            rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 140;
            rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
            if(HopeMonitorViewScreenParameter[1][1 + i] == LEAK_VALUE && RealDispLeakOverThreshold())
            {
                GUI_SetColor(GUI_RED);
            }
            else
            {
                GUI_SetColor(GUI_WHITE);
            }
            GetValueStr(HopeMonitorViewScreenParameter[1][1 + i], str);
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
    }
    else
    {
        GUI_SetFont(FONT_18);
        for(i = 0; i < 3; i++)
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
            if(i < 2)
            {
                if((i == 0 || i == 1) && g_OxiModule.GetConnectStatus() == 0)
                {
                    GUI_SetColor(GUI_GRAY);
                }
                else
                {
                    GUI_SetColor(GUI_WHITE);
                }
                rect.x0 = 15;
                rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                rect.x1 = 90;
                rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_CPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                rect.x0 = 180;
                rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                rect.x1 = 225;
                rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_CPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                rect.x0 = 100;
                rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                rect.x1 = 140;
                rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
                GetValueStr(HopeMonitorViewScreenParameter[1][4 + i], str);
                GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
            }
            else
            {
                SetUiItemText(15
                                , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                , MODE_IDX
                                , GUI_WHITE
                                , FONT_18
                                , FONT_18
                                , FALSE, FALSE);
            }
        }
    }
    if(g_U8IsSleepVentilator)
    {
        DrawPageCircle(g_sU8MonitorViewScreenMidFocus, 3);
    }
    else
    {
        DrawPageCircle(g_sU8MonitorViewScreenMidFocus, 5);
    }
    for(i = 0; i < 3; i++)
    {
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
        GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 22);
        if(i == g_MonitorViewScreenWInfo.U8CurFocusID)
        {
            GUI_SetColor(GREEN);
            GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 20);
            if(i == 0)
            {
                DrawBMP(BMP_MENUFOCU + 2 * i, 65 + 45 * i, Return_Y - 7 + STATEBAR_TITLE_HEIGHT);
            }
            else
            {
                DrawBMP(BMP_MENUFOCU + 2 * i, 68 + 45 * i, Return_Y - 11 + STATEBAR_TITLE_HEIGHT);
            }
        }
        else
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 20);
            if(i == 0)
            {
                DrawBMP(BMP_MENU + 2 * i, 65 + 45 * i, Return_Y - 7 + STATEBAR_TITLE_HEIGHT);
            }
            else
            {
                DrawBMP(BMP_MENU + 2 * i, 68 + 45 * i, Return_Y - 11 + STATEBAR_TITLE_HEIGHT);
            }
        }
    }
}

static void ChangeFocusMonitorViewScreen(void)
{
    uint8_t i;
    uint8_t View_Type = 0;
    GUI_RECT rect;
    char str_Para[10];
    WM_SelectWindow(WM_HBKWIN);
    GUI_SetTextMode(GUI_TM_TRANS);  //���ñ���͸��
    if(g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_HIGHFLOW)
    {
        View_Type = 0;
    }
    else
    {
        if(g_U8IsSleepVentilator)
        {
            View_Type = 1;
        }
        else
        {
            View_Type = 2;
        }
    }
    if(g_sU8OldMonitorViewScreenChange)
    {
        if(g_sU8OldMonitorViewScreenChange == 1)
        {
            GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);//ˢ��
            GUI_FillRect(5, 5 + STATEBAR_TITLE_HEIGHT, 240, 210 + STATEBAR_TITLE_HEIGHT);
        }
        g_sU8OldMonitorViewScreenChange = 0;
        DrawPageCircle(g_sU8MonitorViewScreenMidFocus, 3);
        if(g_sU8MonitorViewScreenMidFocus == 0)
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);//���ñ����ɫ
            GUI_AA_FillRoundedRect(10, 10 + STATEBAR_TITLE_HEIGHT, 230, 180 + STATEBAR_TITLE_HEIGHT, 30);
            GUI_SetColor(FONTCOLOR_DEFAULT);//���������ɫ
            rect.x0 = 20;
            rect.y0 = 20 + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 110;
            rect.y1 = 50 + STATEBAR_TITLE_HEIGHT;
            if(View_Type == 0)
            {
                GUI_DispStringInRectWrap(GetMultiLanguageString(FLOW_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            }
            else
            {
                GUI_DispStringInRectWrap(GetMultiLanguageString(PRESSUER_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            }
            rect.x0 = 110;
            rect.y0 = 150 + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 190;
            rect.y1 = 180 + STATEBAR_TITLE_HEIGHT;
            if(View_Type == 0)
            {
                GUI_DispStringInRectWrap(GetMultiLanguageString(L_MIN_IDX), &rect, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            }
            else
            {
                GUI_DispStringInRectWrap(GetMultiLanguageString(IdType_PressureUnits[g_ConfigSave.GetParameter(PRESSUNIT)]), &rect,
                                GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            }
            GUI_AA_FillRoundedRect(208, 46 + STATEBAR_TITLE_HEIGHT, 215, 146 + STATEBAR_TITLE_HEIGHT, 2);
            GUI_SetFont(FONT_50);
            rect.x0 = 60;
            rect.y0 = 60 + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 180;
            rect.y1 = 140 + STATEBAR_TITLE_HEIGHT;
            GetValueStr(HopeMonitorViewScreenParameter[View_Type][0], str_Para);
            GUI_DispStringInRectWrap(str_Para, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
        else if(g_sU8MonitorViewScreenMidFocus == 1)
        {
            GUI_SetFont(FONT_18);
            for(i = 0; i < 3; i++)
            {
                GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
                if(View_Type == 0 && g_OxiModule.GetConnectStatus() == 0 && (i == 0 || i == 2))
                {
                    GUI_SetColor(GUI_GRAY);
                }
                else
                {
                    GUI_SetColor(GUI_WHITE);
                }
                rect.x0 = 15;
                rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                rect.x1 = 90;
                rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                if(View_Type == 0)//������ʾ
                {
                    GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_HF[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                    GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                }
                else if(View_Type == 1)
                {
                    GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_CPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                    GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                }
                else
                {
                    GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                    GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                }
                rect.x0 = 180;
                rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                rect.x1 = 225;
                rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                if(View_Type == 0)//��λ��ʾ
                {
                    GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_HF[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                    GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                }
                else if(View_Type == 1)
                {
                    GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_CPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                    GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                }
                else
                {
                    GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                    GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                }
                if((View_Type == 1 && HopeMonitorViewScreenParameter[1][1 + i] == LEAK_VALUE && RealDispLeakOverThreshold())
                                || (View_Type == 2 && HopeMonitorViewScreenParameter[2][1 + i] == LEAK_VALUE && RealDispLeakOverThreshold()))
                {
                    GUI_SetColor(GUI_RED);
                }
                else if(View_Type == 0 && g_OxiModule.GetConnectStatus() == 0 && (i == 0 || i == 2))
                {
                    GUI_SetColor(GUI_GRAY);
                }
                else
                {
                    GUI_SetColor(GUI_WHITE);
                }
                rect.x0 = 100;
                rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                rect.x1 = 140;
                rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
                GetValueStr(HopeMonitorViewScreenParameter[View_Type][1 + i], str_Para);
                GUI_DispStringInRectWrap(str_Para, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
            }
        }
        else if(g_sU8MonitorViewScreenMidFocus == 2)
        {
            GUI_SetFont(FONT_18);
            if(View_Type == 0)
            {
                GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                GUI_AA_FillRoundedRect(5, 10 + STATEBAR_TITLE_HEIGHT, 235, 60 + STATEBAR_TITLE_HEIGHT, 20);
                SetUiItemText(15
                                , 28 + STATEBAR_TITLE_HEIGHT
                                , MODE_IDX
                                , GUI_WHITE
                                , FONT_18
                                , FONT_18
                                , FALSE, FALSE);
            }
            else if(View_Type == 1)
            {
                for(i = 0; i < 3; i++)
                {
                    GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                    GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
                    if(i < 2)
                    {
                        if((i == 0 || i == 1) && g_OxiModule.GetConnectStatus() == 0)
                        {
                            GUI_SetColor(GUI_GRAY);
                        }
                        else
                        {
                            GUI_SetColor(GUI_WHITE);
                        }
                        rect.x0 = 15;
                        rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                        rect.x1 = 90;
                        rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                        GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_CPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                        GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                        rect.x0 = 180;
                        rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                        rect.x1 = 225;
                        rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                        GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_CPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                        GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                        rect.x0 = 100;
                        rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                        rect.x1 = 140;
                        rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
                        GetValueStr(HopeMonitorViewScreenParameter[View_Type][4 + i], str_Para);
                        GUI_DispStringInRectWrap(str_Para, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
                    }
                    else
                    {
                        SetUiItemText(15
                                        , 28 + i * EACHLIST_HEIGHT + STATEBAR_TITLE_HEIGHT
                                        , MODE_IDX
                                        , GUI_WHITE
                                        , FONT_18
                                        , FONT_18
                                        , FALSE, FALSE);
                    }
                }
            }
            else
            {
                for(i = 0; i < 3; i++)
                {
                    GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                    GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
                    GUI_SetColor(FONTCOLOR_DEFAULT);
                    rect.x0 = 15;
                    rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = 90;
                    rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                    GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                    GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                    rect.x0 = 180;
                    rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = 225;
                    rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                    GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                    GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                    rect.x0 = 100;
                    rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                    rect.x1 = 140;
                    rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
                    GetValueStr(HopeMonitorViewScreenParameter[View_Type][4 + i], str_Para);
                    GUI_DispStringInRectWrap(str_Para, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
                }
            }
        }
        else if(g_sU8MonitorViewScreenMidFocus == 3)
        {
            for(i = 0; i < 3; i++)
            {
                GUI_SetFont(FONT_18);
                GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
                GUI_AA_FillRoundedRect(5, 10 + 58 * i + STATEBAR_TITLE_HEIGHT, 235, 60 + 58 * i + STATEBAR_TITLE_HEIGHT, ROUNDED);
                if((i == 2 || i == 1) && g_OxiModule.GetConnectStatus() == 0)
                {
                    GUI_SetColor(GUI_GRAY);
                }
                else
                {
                    GUI_SetColor(GUI_WHITE);
                }
                rect.x0 = 15;
                rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                rect.x1 = 90;
                rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewText_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                rect.x0 = 180;
                rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                rect.x1 = 225;
                rect.y1 = 58 + 58 * i + STATEBAR_TITLE_HEIGHT;
                GUI_DispStringInRectWrap(GetMultiLanguageString(MonitorViewUnit_BPAP[g_sU8MonitorViewScreenMidFocus - 1][i]), &rect,
                                GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                rect.x0 = 100;
                rect.y0 = 28 + 58 * i + STATEBAR_TITLE_HEIGHT;
                rect.x1 = 140;
                rect.y1 = 48 + 58 * i + STATEBAR_TITLE_HEIGHT;
                GetValueStr(HopeMonitorViewScreenParameter[View_Type][7 + i], str_Para);
                GUI_DispStringInRectWrap(str_Para, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
            }
        }
        else
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillRoundedRect(5, 10 + STATEBAR_TITLE_HEIGHT, 235, 60 + STATEBAR_TITLE_HEIGHT, 20);
            SetUiItemText(15
                            , 28 + STATEBAR_TITLE_HEIGHT
                            , MODE_IDX
                            , GUI_WHITE
                            , FONT_18
                            , FONT_18
                            , FALSE, FALSE);
        }
        if(View_Type == 2)
        {
            DrawPageCircle(g_sU8MonitorViewScreenMidFocus, 5);
        }
        else
        {
            DrawPageCircle(g_sU8MonitorViewScreenMidFocus, 3);
        }
    }
    else
    {
    }
    for(i = 0; i < 3; i++)
    {
        GUI_SetColor(DEFAULT_MAIN_BACKCOLOR);
        GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 22);
        if(i == g_MonitorViewScreenWInfo.U8CurFocusID)
        {
            GUI_SetColor(GREEN);
            GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 20);
            if(i == 0)
            {
                DrawBMP(BMP_MENUFOCU + 2 * i, 65 + 45 * i, Return_Y - 7 + STATEBAR_TITLE_HEIGHT);
            }
            else
            {
                DrawBMP(BMP_MENUFOCU + 2 * i, 68 + 45 * i, Return_Y - 11 + STATEBAR_TITLE_HEIGHT);
            }
        }
        else
        {
            GUI_SetColor(DEFAULT_MENU_FOCUSCOLOR);
            GUI_AA_FillCircle(75 + 45 * i, Return_Y + STATEBAR_TITLE_HEIGHT, 20);
            if(i == 0)
            {
                DrawBMP(BMP_MENU + 2 * i, 65 + 45 * i, Return_Y - 7 + STATEBAR_TITLE_HEIGHT);
            }
            else
            {
                DrawBMP(BMP_MENU + 2 * i, 68 + 45 * i, Return_Y - 11 + STATEBAR_TITLE_HEIGHT);
            }
        }
    }
//    g_U8OldMonitorViewScreenFocus = g_U8MonitorViewScreenFocus;
}
#else
static void DrawBPAPMonitorViewScreen(void)
{
    static const U8 MonitorViewScreenParameter[] = {FLOW_VALUE, LEAK_VALUE, IE_RATE_VALUE, TV_VALUE, RR_VALUE, INSPTIME_VALUE, MV_VALUE, SPO2_VALUE, PR_VALUE, WORK_MODE_VALUE, PRESSURE_VALUE};
    uint8_t i, j;
    uint8_t sU8Focus[2] = {0};
    char str[10];
    GUI_RECT rect;
    U16 sU16Running1BmpIdx = BMP_RUNNING1;
    U16 sU16Running2BmpIdx = BMP_RUNNING2;
    GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
#if (LCD_TYPE == LCD_5_TFT)
    GUI_FillRect(0, 410 - 102 + STATEBAR_TITLE_HEIGHT, 800, 410 - 102 + 4 - 1 + STATEBAR_TITLE_HEIGHT);
    for(i = 1; i < 4; i++)
    {
        if(i < 2)
        {
            GUI_FillRect(i * 800 / 4, 310 + STATEBAR_TITLE_HEIGHT, i * 800 / 4 + 4 - 1, 410 + STATEBAR_TITLE_HEIGHT);
        }
        else
        {
            GUI_FillRect(i * 800 / 4, 0 + STATEBAR_TITLE_HEIGHT, i * 800 / 4 + 4 - 1, 410 + STATEBAR_TITLE_HEIGHT);
        }
    }
    for(i = 1; i < 4; i++)
    {
        if(i == 2)
        {
            GUI_FillRect(800 / 2, (310 / 4) * i + STATEBAR_TITLE_HEIGHT, 800 / 4 * 4,
                            (310 / 4) * i + 4 - 1 + STATEBAR_TITLE_HEIGHT);
        }
        else
        {
            GUI_FillRect(800 / 2, (310 / 4) * i + STATEBAR_TITLE_HEIGHT, 800 / 4 * 3,
                            (310 / 4) * i + 4 - 1 + STATEBAR_TITLE_HEIGHT);
        }
    }
#else
    GUI_FillRect(0, 273 - 68 + STATEBAR_TITLE_HEIGHT, 480, 273 - 68 + 2 - 1 + STATEBAR_TITLE_HEIGHT);
    for(i = 1; i < 4; i++)
    {
        if(i < 2)
        {
            GUI_FillRect(i * 480 / 4, 206 + STATEBAR_TITLE_HEIGHT, i * 480 / 4 + 2 - 1, 273 + STATEBAR_TITLE_HEIGHT);
        }
        else
        {
            GUI_FillRect(i * 480 / 4, 0 + STATEBAR_TITLE_HEIGHT, i * 480 / 4 + 2 - 1, 273 + STATEBAR_TITLE_HEIGHT);
        }
    }
    for(i = 1; i < 4; i++)
    {
        if(i == 2)
        {
            GUI_FillRect(480 / 2, (206 / 4) * i + STATEBAR_TITLE_HEIGHT, 480 / 4 * 4,
                            (206 / 4) * i + 2 - 1 + STATEBAR_TITLE_HEIGHT);
        }
        else
        {
            GUI_FillRect(480 / 2, (206 / 4) * i + STATEBAR_TITLE_HEIGHT, 480 / 4 * 3,
                            (206 / 4) * i + 2 - 1 + STATEBAR_TITLE_HEIGHT);
        }
    }
#endif
    sU8Focus[0] = g_MonitorViewScreenWInfo.U8CurFocusID;
    sU8Focus[1] = g_MonitorViewScreenWInfo.U8OldFocusID;
    for(i = 0; i < 2; i++)
    {
        if(i == 0)
        {
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);    //���ñ����ɫ
        }
        else
        {
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_DEFAULT);
        }
        if(sU8Focus[i] == 0)
        {
            GUI_FillRect(g_sMenuRect.x0, g_sMenuRect.y0, g_sMenuRect.x1, g_sMenuRect.y1);
        }
        else
        {
            GUI_FillRect(g_sQuickMenuRect.x0, g_sQuickMenuRect.y0, g_sQuickMenuRect.x1, g_sQuickMenuRect.y1);
        }
    }
    GUI_SetColor(GUI_WHITE);
    GUI_DispStringInRectWrap(GetMultiLanguageString(MENU_IDX), &g_sMenuStrMonitorRect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                    GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
    DrawMenuIcon(46, 416 - 70 + STATEBAR_TITLE_HEIGHT);
#else
    DrawMenuIcon(27, 277 - 46 + STATEBAR_TITLE_HEIGHT);
#endif
    GUI_DispStringInRectWrap(GetMultiLanguageString(QUICKSET_IDX), &g_sQuickMenuRect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                    GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_24);
#else
    GUI_SetFont(FONT_18);
#endif
    for(j = 0; j < 2; j++)
    {
        for(i = 0; i < 5; i++)
        {
            if(j == 1 && i == 4)
            {
                continue;
            }
#if (LCD_TYPE == LCD_5_TFT)
            rect.x0 = 800 / 4 * (2 + j) + 4 + 8;
            rect.y0 = 310 / 4 * i + 4 + 6 + 6 * j + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 800 / 4 * (3 + j) - 4 - 1;
            rect.y1 = 310 / 4 * (i + 1) - 4 - 1 + STATEBAR_TITLE_HEIGHT;
#else
            rect.x0 = 480 / 4 * (2 + j) + 2 + 4;
            rect.y0 = 206 / 4 * i + 2 + 3 + 3 * j + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 480 / 4 * (3 + j) - 2 - 1;
            rect.y1 = 206 / 4 * (i + 1) - 2 - 1 + STATEBAR_TITLE_HEIGHT;
#endif
            if(j == 1)
            {
#if (LCD_TYPE == LCD_5_TFT)
                rect.y0 = rect.y0 - 6;
#else
                rect.y0 = rect.y0 - 3;
#endif
            }
            if(((g_IdType_MonitorView[0][i + 1 + j * 5] == SPO2_IDX) || (g_IdType_MonitorView[0][i + 1 + j * 5] == PR_IDX))
                            && g_OxiModule.GetConnectStatus() == 0)
            {
                GUI_SetColor(GUI_GRAY);
            }
            else
            {
                GUI_SetColor(GUI_WHITE);
            }
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_24);
#else
            GUI_SetFont(FONT_18);
#endif
            rect.x1 = rect.x1 - 45;
            GUI_DispStringInRectWrap(GetMultiLanguageString(g_IdType_MonitorView[0][i + 1 + j * 5])
                            , &rect, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_18);
#else
            GUI_SetFont(FONT_16);
#endif
            if(j == 0 && i == 2)
            {
                continue;
            }
            rect.x0 = rect.x1;
            rect.x1 = rect.x1 + 45;
            GUI_DispStringInRectWrap(GetMultiLanguageString(g_IdType_MonitorView[1][i + 1 + j * 5])
                            , &rect, GUI_TA_RIGHT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
        }
    }
    GUI_SetColor(GUI_WHITE);
#if (LCD_TYPE == LCD_5_TFT)
    rect.x0 = 800 / 4 * 3 + 4;
    rect.y0 = 410 - 102 + 4 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = 800 / 4 * 4 - 4 - 1;
    rect.y1 = 410 - 1 + STATEBAR_TITLE_HEIGHT;
#else
    rect.x0 = 480 / 4 * 3 + 2;
    rect.y0 = 273 - 68 + 2 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = 480 / 4 * 4 - 2 - 1;
    rect.y1 = 273 - 1 + STATEBAR_TITLE_HEIGHT;
#endif
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_38);
#else
    GUI_SetFont(FONT_24);
#endif
    GUI_DispStringInRect(GetMultiLanguageString(IdType_WorkMode[g_ConfigSave.GetParameter(WORKMODE)])
                    , &rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_32);
    GUI_DispStringAt(GetMultiLanguageString(PRESSUER_IDX), 20, 25 + STATEBAR_TITLE_HEIGHT);
#else
    GUI_SetFont(FONT_24);
    GUI_DispStringAt(GetMultiLanguageString(PRESSUER_IDX), 12, 16 + STATEBAR_TITLE_HEIGHT);
#endif
    if(g_ConfigSave.GetParameter(WORKMODE) >= SYS_WM_S)
    {
#if (LCD_TYPE == LCD_5_TFT)
        rect.x0 = 329;
        rect.y0 = 27 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 370;
        rect.y1 = 60 + STATEBAR_TITLE_HEIGHT;
#else
        rect.x0 = 197;
        rect.y0 = 18 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 222;
        rect.y1 = 40 + STATEBAR_TITLE_HEIGHT;
#endif
        GetValueStr(IE_STATUS_VALUE, str);
        GUI_DispStringInRect(str, &rect, GUI_TA_LEFT | GUI_TA_TOP);
    }
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_24);
    GUI_DispStringAt(GetMultiLanguageString(IdType_PressureUnits[g_ConfigSave.GetParameter(PRESSUNIT)]), 280,
                    265 - 70 + STATEBAR_TITLE_HEIGHT);
    rect.x0 = 0;
    rect.y0 = 58 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = 800 / 4 * 2 - 4 - 1;
    rect.y1 = 190 + STATEBAR_TITLE_HEIGHT;
    GUI_SetFont(FONT_100);
#else
    GUI_SetFont(FONT_18);
    GUI_DispStringAt(GetMultiLanguageString(IdType_PressureUnits[g_ConfigSave.GetParameter(PRESSUNIT)]), 168,
                    176 - 41 + STATEBAR_TITLE_HEIGHT);
    rect.x0 = 0;
    rect.y0 = 38 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = 480 / 4 * 2 - 2 - 1;
    rect.y1 = 131 + STATEBAR_TITLE_HEIGHT;
    GUI_SetFont(FONT_80);
#endif
    GetValueStr(MonitorViewScreenParameter[10], str);
    GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_38);
#else
    GUI_SetFont(FONT_24);
#endif
    for(j = 0; j < 2; j++)
    {
        for(i = 0; i < 5; i++)
        {
            if(j == 1 && i == 4)
            {
                continue;
            }
#if (LCD_TYPE == LCD_5_TFT)
            rect.x0 = 800 / 4 * (2 + j) + 4 + 8 + 80;
            rect.y0 = 310 / 4 * i + 30 + 6 * j + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 800 / 4 * (3 + j) - 4 - 1;
            rect.y1 = 310 / 4 * (i + 1) - 4 - 1 + STATEBAR_TITLE_HEIGHT;
#else
            rect.x0 = 480 / 4 * (2 + j) + 2 + 4 + 48;
            rect.y0 = 206 / 4 * i + 20 + 3 * j + STATEBAR_TITLE_HEIGHT;
            rect.x1 = 480 / 4 * (3 + j) - 2 - 1;
            rect.y1 = 206 / 4 * (i + 1) - 2 - 1 + STATEBAR_TITLE_HEIGHT;
#endif
            GetValueStr(MonitorViewScreenParameter[j * 5 + i], str);
            if(((MonitorViewScreenParameter[j * 5 + i] == SPO2_VALUE) || (MonitorViewScreenParameter[j * 5 + i] == PR_VALUE))
                            && g_OxiModule.GetConnectStatus() == 0)
            {
                GUI_SetColor(GUI_GRAY);
            }
            else
            {
                if(MonitorViewScreenParameter[j * 5 + i] == LEAK_VALUE && RealDispLeakOverThreshold())
                {
                    GUI_SetColor(GUI_RED);
                }
                else
                {
                    GUI_SetColor(GUI_WHITE);
                }
            }
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_RIGHT | GUI_TA_BOTTOM, GUI_WRAPMODE_WORD);
        }
    }
    for(i = 0; i < 12; i++)
    {
        if(i == g_sU8RunningIdx || i == (g_sU8RunningIdx + 1) % 12 || i == (g_sU8RunningIdx + 2) % 12)
        {
            //GUI_DrawBitmap(&bmRunning1, 45 + i * 25, 311 - 65);
#if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16Running1BmpIdx, 45 + i * 25, 311 - 65 + STATEBAR_TITLE_HEIGHT);
#else
            DrawBMP(sU16Running1BmpIdx, 27 + i * (13 + 2), 207 - 43 + STATEBAR_TITLE_HEIGHT);
#endif
        }
        else
        {
            //GUI_DrawBitmap(&bmRunning2, 45 + i * 25, 311 - 65);
#if (LCD_TYPE == LCD_5_TFT)
            DrawBMP(sU16Running2BmpIdx, 45 + i * 25, 311 - 65 + STATEBAR_TITLE_HEIGHT);
#else
            DrawBMP(sU16Running2BmpIdx, 27 + i * (13 + 2), 207 - 43 + STATEBAR_TITLE_HEIGHT);
#endif
        }
    }
}

static void DrawCPAPAPAPMonitorViewScreen(void)
{
    static const U8 CPAPMonitorViewScreenParameter[] = {LEAK_VALUE, PRESSURE_VALUE, FLOW_VALUE, WORK_MODE_VALUE, RR_VALUE, SPO2_VALUE, PR_VALUE};
    uint8_t i;
    uint8_t sU8Focus[2] = {0};
    char str[10];
    GUI_RECT rect;
    GUI_SetColor(PAGE_CIRCLE_COLOR_DEFAULT);
#if (LCD_TYPE == LCD_5_TFT)
    GUI_FillRect(0, 410 - 102 + STATEBAR_TITLE_HEIGHT, 800, 410 - 102 + 4 - 1 + STATEBAR_TITLE_HEIGHT);
    for(i = 1; i < 5; i++)
    {
        GUI_FillRect(i * 800 / 5, 310 + STATEBAR_TITLE_HEIGHT, i * 800 / 5 + 4 - 1, 410 + STATEBAR_TITLE_HEIGHT);
    }
#else
    GUI_FillRect(0, 273 - 68 + STATEBAR_TITLE_HEIGHT, 480, 273 - 68 + 2 - 1 + STATEBAR_TITLE_HEIGHT);
    for(i = 1; i < 5; i++)
    {
        GUI_FillRect(i * 480 / 5, 206 + STATEBAR_TITLE_HEIGHT, i * 480 / 5 + 2 - 1, 273 + STATEBAR_TITLE_HEIGHT);
    }
#endif
    sU8Focus[0] = g_MonitorViewScreenWInfo.U8CurFocusID;
    sU8Focus[1] = g_MonitorViewScreenWInfo.U8OldFocusID;
    for(i = 0; i < 2; i++)
    {
        if(i == 0)
        {
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);    //���ñ����ɫ
        }
        else
        {
            GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_DEFAULT);
        }
        if(sU8Focus[i] == 0)
        {
            GUI_FillRect(g_sCPAPMenuRect.x0, g_sCPAPMenuRect.y0, g_sCPAPMenuRect.x1, g_sCPAPMenuRect.y1);
        }
        else
        {
            GUI_FillRect(g_sCPAPQuickMenuRect.x0, g_sCPAPQuickMenuRect.y0, g_sCPAPQuickMenuRect.x1, g_sCPAPQuickMenuRect.y1);
        }
    }
    GUI_SetColor(GUI_WHITE);
    GUI_DispStringInRectWrap(GetMultiLanguageString(MENU_IDX), &g_sCPAPMenuStrRect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                    GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
    DrawMenuIcon(26, 416 - 70 + STATEBAR_TITLE_HEIGHT);
#else
    DrawMenuIcon(5, 277 - 46 + STATEBAR_TITLE_HEIGHT);
#endif
    GUI_DispStringInRectWrap(GetMultiLanguageString(QUICKSET_IDX), &g_sCPAPQuickMenuRect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                    GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_24);
#else
    GUI_SetFont(FONT_18);
#endif
    for(i = 0; i < 3; i++)
    {
#if (LCD_TYPE == LCD_5_TFT)
        rect.x0 = 800 / 5 * (i + 2) + 4 + 4;
        rect.y0 = 310 + 5 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 800 / 5 * (i + 3) - 4 - 1 - 20;
        rect.y1 = 410 - 1 - 20 + STATEBAR_TITLE_HEIGHT;
#else
        rect.x0 = 480 / 5 * (i + 2) + 2 + 2;
        rect.y0 = 206 + 3 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 480 / 5 * (i + 3) - 2 - 1 - 12;
        rect.y1 = 273 - 1 - 12 + STATEBAR_TITLE_HEIGHT;
#endif
        if(((g_IdType_CPAPMonitorView[0][i + 3] == SPO2_IDX) || (g_IdType_CPAPMonitorView[0][i + 3] == PR_IDX))
                        && g_OxiModule.GetConnectStatus() == 0)
        {
            GUI_SetColor(GUI_GRAY);
        }
        else
        {
            GUI_SetColor(GUI_WHITE);
        }
#if (LCD_TYPE == LCD_5_TFT)
        GUI_SetFont(FONT_32);
#else
        GUI_SetFont(FONT_18);
#endif
        GUI_DispStringInRectWrap(GetMultiLanguageString(g_IdType_CPAPMonitorView[0][i + 3])
                        , &rect, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
        rect.y0 += 40;
        GUI_SetFont(FONT_38);
#else
        rect.y0 += 24;
        GUI_SetFont(FONT_32);
#endif
        GetValueStr(CPAPMonitorViewScreenParameter[4 + i], str);
//        if (g_IdType_CPAPMonitorView[0][i + 3] == SPO2_IDX && g_OxiModule.GetConnectStatus() != 0)
//            strcat(str, "%");
        GUI_DispStringInRectWrap(str, &rect, GUI_TA_RIGHT | GUI_TA_BOTTOM, GUI_WRAPMODE_WORD);
//            GUI_SetFont(FONT_18);
//            GUI_DispStringInRect(GetMultiLanguageString(g_IdType_CPAPMonitorView[1][i + 3])
//                                , &rect, GUI_TA_RIGHT | GUI_TA_TOP);
    }
    GUI_SetColor(GUI_WHITE);
#if (LCD_TYPE == LCD_5_TFT)
    rect.x0 = 610;
    rect.y0 = 5 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = 800 - 1;
    rect.y1 = 45 - 1 + STATEBAR_TITLE_HEIGHT;
    GUI_SetFont(FONT_38);
#else
    rect.x0 = 366;
    rect.y0 = 3 + STATEBAR_TITLE_HEIGHT;
    rect.x1 = 480 - 1;
    rect.y1 = 30 - 1 + STATEBAR_TITLE_HEIGHT;
    GUI_SetFont(FONT_24);
#endif
    GUI_DispStringInRect(GetMultiLanguageString(IdType_WorkMode[g_ConfigSave.GetParameter(WORKMODE)])
                    , &rect, GUI_TA_RIGHT | GUI_TA_TOP);
    if(g_ConfigSave.GetParameter(WORKMODE) >= SYS_WM_S)
    {
#if (LCD_TYPE == LCD_5_TFT)
        rect.x0 = 500;
        rect.y0 = 5 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 520 - 1;
        rect.y1 = 45 - 1 + STATEBAR_TITLE_HEIGHT;
#else
        rect.x0 = 300;
        rect.y0 = 3 + STATEBAR_TITLE_HEIGHT;
        rect.x1 = 312 - 1;
        rect.y1 = 30 - 1 + STATEBAR_TITLE_HEIGHT;
#endif
        GetValueStr(IE_STATUS_VALUE, str);
        GUI_DispStringInRectWrap(str, &rect, GUI_TA_RIGHT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
    }
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetPenSize(13);
    GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
    GUI_AA_DrawArc(400, 155 + STATEBAR_TITLE_HEIGHT, 120, 120, 0, 360);
#else
    GUI_SetPenSize(8);
    GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);
    GUI_AA_DrawArc(240, 103 + STATEBAR_TITLE_HEIGHT, 72, 72, 0, 360);
#endif
    GUI_SetPenSize(1);
    for(i = 0; i < 3; i++)
    {
        if(i != 1)
        {
            GUI_SetColor(GUI_WHITE);
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
#else
            GUI_SetFont(FONT_24);
#endif
            rect.x0 = g_CPAPMonitorRect[i].x0;
            rect.y0 = g_CPAPMonitorRect[i].y0;
            rect.x1 = g_CPAPMonitorRect[i].x1;
            rect.y1 = g_CPAPMonitorRect[i].y1;
            GUI_DispStringInRectWrap(GetMultiLanguageString(g_IdType_CPAPMonitorView[0][i]), &rect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                            GUI_WRAPMODE_WORD);
            rect.x0 = g_CPAPMonitorRect[i + 6].x0;
            rect.y0 = g_CPAPMonitorRect[i + 6].y0;
            rect.x1 = g_CPAPMonitorRect[i + 6].x1;
            rect.y1 = g_CPAPMonitorRect[i + 6].y1;
            GUI_DispStringInRectWrap(GetMultiLanguageString(g_IdType_CPAPMonitorView[1][i]), &rect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                            GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_38);
#else
            GUI_SetFont(FONT_32);
#endif
            rect.x0 = g_CPAPMonitorRect[i + 3].x0;
            rect.y0 = g_CPAPMonitorRect[i + 3].y0;
            rect.x1 = g_CPAPMonitorRect[i + 3].x1;
            rect.y1 = g_CPAPMonitorRect[i + 3].y1;
            if(CPAPMonitorViewScreenParameter[i] == LEAK_VALUE && RealDispLeakOverThreshold())
            {
                GUI_SetColor(GUI_RED);
            }
            else
            {
                GUI_SetColor(GUI_WHITE);
            }
            GetValueStr(CPAPMonitorViewScreenParameter[i], str);
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
        else
        {
            GUI_SetColor(GUI_WHITE);
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_38);
#else
            GUI_SetFont(FONT_24);
#endif
            rect.x0 = g_CPAPMonitorRect[i].x0;
            rect.y0 = g_CPAPMonitorRect[i].y0;
            rect.x1 = g_CPAPMonitorRect[i].x1;
            rect.y1 = g_CPAPMonitorRect[i].y1;
            GUI_DispStringInRectWrap(GetMultiLanguageString(g_IdType_CPAPMonitorView[0][i]), &rect, GUI_TA_HCENTER | GUI_TA_VCENTER,
                            GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_100);
#else
            GUI_SetFont(FONT_50);
#endif
            rect.x0 = g_CPAPMonitorRect[i + 3].x0 - 5;
            rect.y0 = g_CPAPMonitorRect[i + 3].y0;
            rect.x1 = g_CPAPMonitorRect[i + 3].x1 + 5;
            rect.y1 = g_CPAPMonitorRect[i + 3].y1;
            GetValueStr(CPAPMonitorViewScreenParameter[i], str);
            GUI_DispStringInRectWrap(str, &rect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_32);
#else
            GUI_SetFont(FONT_24);
#endif
            rect.x0 = g_CPAPMonitorRect[i + 6].x0;
            rect.y0 = g_CPAPMonitorRect[i + 6].y0;
            rect.x1 = g_CPAPMonitorRect[i + 6].x1;
            rect.y1 = g_CPAPMonitorRect[i + 6].y1;
            GUI_DispStringInRectWrap(GetMultiLanguageString(IdType_PressureUnits[g_ConfigSave.GetParameter(PRESSUNIT)]), &rect,
                            GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
    }
}

static void ChangeFocusMonitorViewScreen(void)
{
    uint8_t i;
    uint8_t sU8Focus[2] = {0};
    const char *str[2];
    str[0] = GetMultiLanguageString(MENU_IDX);
    str[1] = GetMultiLanguageString(QUICKSET_IDX);
    sU8Focus[0] = g_MonitorViewScreenWInfo.U8CurFocusID;
    sU8Focus[1] = g_MonitorViewScreenWInfo.U8OldFocusID;
    WM_SelectWindow(WM_HBKWIN);
    GUI_SetTextMode(GUI_TM_TRANS);  //���ñ���͸��
#if (LCD_TYPE == LCD_5_TFT)
    GUI_SetFont(FONT_32);
#else
    GUI_SetFont(FONT_24);
#endif
    if(g_ConfigSave.GetParameter(WORKMODE) == SYS_WM_HIGHFLOW)
    {
        for(i = 0; i < 2; i++)
        {
            if(i == 0)
            {
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);    //���ñ����ɫ
            }
            else
            {
                GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_DEFAULT);
            }
            if(sU8Focus[i] == 0)
            {
                GUI_FillRect(g_sHFMenuRect.x0, g_sHFMenuRect.y0, g_sHFMenuRect.x1, g_sHFMenuRect.y1);
            }
            else
            {
                GUI_FillRect(g_sHFQuickMenuRect.x0, g_sHFQuickMenuRect.y0, g_sHFQuickMenuRect.x1, g_sHFQuickMenuRect.y1);
            }
        }
        GUI_SetColor(GUI_WHITE);
        //�˵�
        //GUI_DispStringAt(str[0], 112, 416 - 70);
        GUI_DispStringInRectWrap(str[0], &g_sHFMenuStrRect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
        DrawMenuIcon(146, 416 - 70 + STATEBAR_TITLE_HEIGHT);
#else
        DrawMenuIcon(87, 277 - 46 + STATEBAR_TITLE_HEIGHT);
#endif
        GUI_DispStringInRectWrap(str[1], &g_sHFQuickMenuRect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_CHAR);
    }
    else
    {
        if(g_U8IsSleepVentilator)
        {
            for(i = 0; i < 2; i++)
            {
                if(i == 0)
                {
                    GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);    //���ñ����ɫ
                }
                else
                {
                    GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_DEFAULT);
                }
                if(sU8Focus[i] == 0)
                {
                    GUI_FillRect(g_sCPAPMenuRect.x0, g_sCPAPMenuRect.y0, g_sCPAPMenuRect.x1, g_sCPAPMenuRect.y1);
                }
                else
                {
                    GUI_FillRect(g_sCPAPQuickMenuRect.x0, g_sCPAPQuickMenuRect.y0, g_sCPAPQuickMenuRect.x1, g_sCPAPQuickMenuRect.y1);
                }
            }
            GUI_SetColor(GUI_WHITE);
            //�˵�
            //GUI_DispStringAt(str[0], 112, 416 - 70);
            GUI_DispStringInRectWrap(str[0], &g_sCPAPMenuStrRect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
            DrawMenuIcon(26, 416 - 70 + STATEBAR_TITLE_HEIGHT);
#else
            DrawMenuIcon(5, 277 - 46 + STATEBAR_TITLE_HEIGHT);
#endif
            GUI_DispStringInRectWrap(str[1], &g_sCPAPQuickMenuRect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
        else
        {
            for(i = 0; i < 2; i++)
            {
                if(i == 0)
                {
                    GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_SELECTED);    //���ñ����ɫ
                }
                else
                {
                    GUI_SetColor(MAINSCREEN_MENU_BACKCOLOR_DEFAULT);
                }
                if(sU8Focus[i] == 0)
                {
                    GUI_FillRect(g_sMenuRect.x0, g_sMenuRect.y0, g_sMenuRect.x1, g_sMenuRect.y1);
                }
                else
                {
                    GUI_FillRect(g_sQuickMenuRect.x0, g_sQuickMenuRect.y0, g_sQuickMenuRect.x1, g_sQuickMenuRect.y1);
                }
            }
            GUI_SetColor(GUI_WHITE);
            //�˵�
            //GUI_DispStringAt(str[0], 112, 416 - 70);
            GUI_DispStringInRectWrap(str[0], &g_sMenuStrMonitorRect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
#if (LCD_TYPE == LCD_5_TFT)
            DrawMenuIcon(46, 416 - 70 + STATEBAR_TITLE_HEIGHT);
#else
            DrawMenuIcon(27, 277 - 46 + STATEBAR_TITLE_HEIGHT);
#endif
            GUI_DispStringInRectWrap(str[1], &g_sQuickMenuRect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
        }
    }
}
#endif

static void OnEnterClick_MonitorViewScreen(U8 ShortPress)
{
#if (LCD_TYPE == LCD_28_TFT)
    //�˵�
    if(g_MonitorViewScreenWInfo.U8CurFocusID == 0)
    {
        if(ShortPress == 1)
        {
            g_sU8MonitorViewScreenMidFocus = 0;
            ShowMonitorViewScreen(0);
        }
        else
        {
            UpdateIconStatus(DISPLAY_UNLOCKED_ICON_INDICATION_BIT, 1);
            g_QuickSettingsEnterType = 0;
            ShowQuickSettingsScreen(1);
        }
    }
    else
    {
        return;
    }
#else
    if(g_MonitorViewScreenWInfo.U8CurFocusID == 0)
    {
        ShowMonitorViewScreen(0);
    }
    else
    {
        if(g_ConfigSave.GetMarketAreaType() != MARKET_AREA_FDA)
        {
            if(ShortPress == 1)
            {
                return;
            }
            UpdateIconStatus(DISPLAY_UNLOCKED_ICON_INDICATION_BIT, 1);
        }
        g_QuickSettingsEnterType = 0;
        ShowQuickSettingsScreen(1);
    }
#endif
}

#if (LCD_TYPE == LCD_28_TFT)
//�ص�����
static void MonitorViewScreenBkWindow(WM_MESSAGE *pMsg)
{
    switch(pMsg->MsgId)
    {
        case WM_PAINT:    //�����ػ���Ϣ
            RepaintMonitorViewScreen();
            break;
        case WM_KEY:
            WM_SelectWindow(WM_HBKWIN);
            switch(((WM_KEY_INFO *)(pMsg->Data.p))->Key)
            {
                case GUI_KEY_BACKTAB:
                    if(g_MonitorViewScreenWInfo.U8CurFocusID  > 0)
                    {
                        g_MonitorViewScreenWInfo.U8CurFocusID  --;
                    }
                    else
                    {
                        g_MonitorViewScreenWInfo.U8CurFocusID  = 2;
                    }
                    ChangeFocusMonitorViewScreen();
                    break;
                case GUI_KEY_TAB:
                    if(g_MonitorViewScreenWInfo.U8CurFocusID  < 2)
                    {
                        g_MonitorViewScreenWInfo.U8CurFocusID  ++;
                    }
                    else
                    {
                        g_MonitorViewScreenWInfo.U8CurFocusID  = 0;
                    }
                    ChangeFocusMonitorViewScreen();
                    break;
                case GUI_KEY_ENTER:
                    if(g_MonitorViewScreenWInfo.U8CurFocusID  == 1)
                    {
                        if(g_sU8MonitorViewScreenMidFocus > 0)
                        {
                            g_sU8MonitorViewScreenMidFocus --;
                            if(g_sU8MonitorViewScreenMidFocus == 0)
                            {
                                g_sU8OldMonitorViewScreenChange = 1;
                            }
                        }
                        else
                        {
                            if(!g_U8IsSleepVentilator && g_ConfigSave.GetParameter(WORKMODE) != SYS_WM_HIGHFLOW)
                            {
                                g_sU8MonitorViewScreenMidFocus = 4;
                            }
                            else
                            {
                                g_sU8MonitorViewScreenMidFocus = 2;
                            }
                            g_sU8OldMonitorViewScreenChange = 1;
                        }
                    }
                    if(g_MonitorViewScreenWInfo.U8CurFocusID  == 2)
                    {
                        if(!g_U8IsSleepVentilator && g_ConfigSave.GetParameter(WORKMODE) != SYS_WM_HIGHFLOW)
                        {
                            if(g_sU8MonitorViewScreenMidFocus < 4)
                            {
                                g_sU8MonitorViewScreenMidFocus ++;
                                if(g_sU8MonitorViewScreenMidFocus == 1 || g_sU8MonitorViewScreenMidFocus == 4)
                                {
                                    g_sU8OldMonitorViewScreenChange = 1;
                                }
                            }
                            else
                            {
                                g_sU8MonitorViewScreenMidFocus = 0;
                                g_sU8OldMonitorViewScreenChange = 1;
                            }
                        }
                        else
                        {
                            if(g_sU8MonitorViewScreenMidFocus < 2)
                            {
                                g_sU8MonitorViewScreenMidFocus ++;
                                if(g_sU8MonitorViewScreenMidFocus == 1 || g_sU8MonitorViewScreenMidFocus == 2)
                                {
                                    g_sU8OldMonitorViewScreenChange = 1;
                                }
                            }
                            else
                            {
                                g_sU8MonitorViewScreenMidFocus = 0;
                                g_sU8OldMonitorViewScreenChange = 1;
                            }
                        }
                    }
                    if(g_MonitorViewScreenWInfo.U8CurFocusID  == 0)
                    {
                        OnEnterClick_MonitorViewScreen(1);
                    }
                    else
                    {
                        if(g_sU8OldMonitorViewScreenChange != 1)
                        {
                            g_sU8OldMonitorViewScreenChange = 2;
                        }
                        ChangeFocusMonitorViewScreen();
                    }
                    break;
                case GUI_KEY_F1:
                    OnEnterClick_MonitorViewScreen(0);
                    break;
                default:
                    break;
            }
            break;
        default:
            WM_DefaultProc(pMsg);    //Ĭ����Ϣ����
    }
}
#else

#endif

void ShowMonitorViewScreen(uint8_t Flag)
{
    if(Flag)
    {
//        g_MonitorViewScreenWInfo.U8CurFocusID = 0;
//        g_MonitorViewScreenWInfo.U8OldFocusID = 0;
        ResetRealDisplayData();
        UpdateTopStateBarMenuString(GetMultiLanguageString(MONITORVIEW_IDX));
//        WM_SetFocus(WM_HBKWIN);
//      WM_ShowWindow(WM_HBKWIN);
#if (LCD_TYPE == LCD_28_TFT)
        clear_key_long_pressed();  //清除长按进度条计数
        CreateRunningTimer();
#else
        if(g_U8IsSleepVentilator == FALSE)
        {
            CreateRunningTimer();
        }
#endif
        CreateUnlockTimer();
        g_sU8RunningIdx = 0;
//        RepaintMonitorViewScreen();
        g_WindowDrv.PushWindow(&g_MonitorViewScreenWInfo);
    }
    else
    {
        SetShowParameterType(SHOW_NONE_PRAMETER);
        UpdateTopStateBarMenuString(GetMultiLanguageString(MENU_IDX));
//        if (g_U8IsSleepVentilator == FALSE)
//        {
//            if (g_DrawRunningTimer != NULL)
//            {
//                GUI_TIMER_Delete(g_DrawRunningTimer);
//                g_DrawRunningTimer = NULL;
//            }
//        }
//        if (g_UnlockProgressUpdateTimer != NULL)
//        {
//            GUI_TIMER_Delete(g_UnlockProgressUpdateTimer);
//            g_UnlockProgressUpdateTimer = NULL;
//        }
        g_WindowDrv.PopWindow(&g_MonitorViewScreenWInfo);
    }
    //UpdateIconStatus(DISPLAY_LOCKED_ICON_INDICATION_BIT, Flag);
}


