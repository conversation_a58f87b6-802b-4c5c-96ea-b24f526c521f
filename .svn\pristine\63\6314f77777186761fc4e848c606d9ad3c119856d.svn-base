/*********************************************************************
*                                                                    *
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
*                                                                    *
**********************************************************************
*                                                                    *
* C-file generated by:                                               *
*                                                                    *
*        GUI_Builder for emWin version 5.32                          *
*        Compiled Oct  8 2015, 11:59:02                              *
*        (c) 2015 Segger Microcontroller GmbH & Co. KG               *
*                                                                    *
**********************************************************************
*                                                                    *
*        Internet: www.segger.com  Support: <EMAIL>       *
*                                                                    *
**********************************************************************
*/

// USER START (Optionally insert additional includes)
#include "StaterBar.h"
#include "Key.h"
#include "ConfigSave.h"
#include "bsp_bldcm_control.h"
#include "SignalCondition.h"
#include "GlobalVariable.h"
#include "CalcDateTime.h"
#include "Filter.h"
#include "FlowDispose.h"
#include "stack.h"
#include "FlowSensorType.h"
#include "main.h"
#include "bsp_mcu_uart.h"
// USER END
#include "DIALOG.h"
#include "AutoFlowModule.h"
#include "AutoFlowCali.h"

typedef struct
{
    bool bPWMAddOrFlag;
} FLOW_TIT;
static FLOW_TIT FlowTitFlow;
/*********************************************************************
*
*       Defines
*
**********************************************************************
*/
#define ID_WINDOW_0         (GUI_ID_USER + 0x00)
#define ID_BUTTON_0         (GUI_ID_USER + 0x01)
#define ID_BUTTON_1         (GUI_ID_USER + 0x02)
#define ID_BUTTON_2         (GUI_ID_USER + 0x03)
#define ID_BUTTON_3         (GUI_ID_USER + 0x04)
#define ID_BUTTON_4         (GUI_ID_USER + 0x05)
#define BUTTON_NUM_MAX      (0x05)
#define ID_TEXT_0           (GUI_ID_USER + 0x06)
#define ID_TEXT_1           (GUI_ID_USER + 0x07)
#define ID_PALETTLE         (GUI_ID_USER + 0x08)
#define ID_MODEINFO         (GUI_ID_USER + 0x09)

#if (LCD_TYPE == LCD_5_TFT)
    #define VALUE_INFO_NUM_MAX  (3)
    #define BUTTON_HIGHT        (40)
    #define BUTTON_WIDTH        (100)
    #define BUTTON_INTERVAL     (15)
    #define STATUSBAR_HIGHT     (70)
    #define TEXT_HIGHT          (28)
    #define TEXT_TYPE_WIDTH     (98)
    #define TEXT_VALUE_WIDTH    (95)
    #define AEStTHTIC_HIGHT     (10)
    #define VALUE_WIDTH         (150)
    #define CALIINFO_HIGHT      (70)
    #define MODEINFO_HIGHT      (100)
#elif (LCD_TYPE == LCD_28_TFT)
    #define VALUE_INFO_NUM_MAX  (2)
    #define BUTTON_HIGHT        (52)
    #define BUTTON_WIDTH        (27)
    #define BUTTON_INTERVAL     (4)
    #define STATUSBAR_HIGHT     (46)
    #define TEXT_HIGHT          (18)
    #define TEXT_TYPE_WIDTH     (72)
    #define TEXT_VALUE_WIDTH    (70)
    #define AEStTHTIC_HIGHT     (7)
    #define VALUE_WIDTH         (100)
    #define CALIINFO_HIGHT      (75)
    #define MODEINFO_HIGHT      (85)
#else
    #define VALUE_INFO_NUM_MAX  (3)
    #define BUTTON_HIGHT        (26)
    #define BUTTON_WIDTH        (60)
    #define BUTTON_INTERVAL     (15)
    #define STATUSBAR_HIGHT     (46)
    #define TEXT_HIGHT          (18)
    #define TEXT_TYPE_WIDTH     (72)
    #define TEXT_VALUE_WIDTH    (70)
    #define AEStTHTIC_HIGHT     (10)
    #define VALUE_WIDTH         (100)
    #define CALIINFO_HIGHT      (60)
    #define MODEINFO_HIGHT      (80)
#endif

// USER START (Optionally insert additional defines)
static void ShowProcess(void);
WINDOWINFO g_AutoFlowCaliWInfo = {NULL, NULL, NULL, NULL, ShowProcess, NULL, 0, 0, AUTO_FLOWCALI_ID};

WM_HWIN g_AutoFlowCaliHwin = NULL;
static GUI_TIMER_HANDLE g_RefreshTimer = NULL;
static uint32_t s_wAutoFlowCalibTotalTime = 0;
static int8_t s_sbErrorDataIndex = -1;
volatile static int16_t s_s16PwmTmp = 0;
volatile static int16_t s_s16CodeTmp = 0;
volatile static int16_t s_s16DevFlowTmp = 0;
volatile static uint16_t s_u16TsiFlowTmp = 0;
static uint8_t s_s8AutoCailFlag = 0;
/**
 * @brief  自动校准数据检查是否ok
 * @return int8_t
 */
static int8_t check_auto_calib_retest_data(void)
{
    AutoFlowCalibData_t *pCalibData = get_auto_flow_calib_tmp_data();
    //数据检查 ≤20L/min，偏差＜2L/min；＞20L/min，偏差＜10%
    for(int n = 0; n < pCalibData->bDataSize; n++)
    {
        uint16_t hCmpVal = 20;
        if(abs(pCalibData->shPhyFlow[n]) <= 400)
        {
            hCmpVal = 20;
        }
        else
        {
            hCmpVal = abs(pCalibData->shPhyFlow[n]) * 5 / 100;
        }
        if(abs(pCalibData->shOtherVal[n] - pCalibData->shPhyFlow[n]) > hCmpVal)
        {
            return n;
        }
    }
    return -1;
}

/**
 * @brief  自动流量校准完成状态检测
 *          1、自动流量校准完成后，更新定标数据
 *          2、统计自动流量校准时间
 * @note   该函数供定时器调用
 */
static void check_auto_calib_done_status(void)
{
    static uint8_t s_bCalibRefreshFlag = 0;
    static uint8_t s_bCalibCountTimeFlag = 0;
    static uint32_t s_wAutoFlowCalibTimeTick = 0;

    if(get_auto_flow_calib_status() == AUTO_FLOW_CALIB_DONE)
    {
        //校准完成更新一次定标数据
        if(!s_bCalibRefreshFlag)
        {
            s_bCalibRefreshFlag = 1;
            CalcADCodeToFlow();
        }
    }
    else
    {
        s_bCalibRefreshFlag = 0;
    }

    //统计校准时间
    if(get_auto_calib_enable() > 0)
    {
        if(!s_bCalibCountTimeFlag)
        {
            s_bCalibCountTimeFlag = 0x01;
            if(!s_s8AutoCailFlag)
            {
                s_wAutoFlowCalibTimeTick = tick_get();
            }
        }
    }
    else
    {
        if(s_bCalibCountTimeFlag > 0)
        {
            s_bCalibCountTimeFlag = 0;
            s_wAutoFlowCalibTotalTime = time_count(tick_get(), s_wAutoFlowCalibTimeTick);
        }
    }
}

/**
 * @brief   该函数供定时器调用，用于刷新当前界面
 *          1、对FlowTitFlow.bPWMAddOrFlag进行检测
 *          2、刷新定时器
 *          3、判断当前是否需要更新数据
 *          4、对当前界面进行无效化刷新
 * @note    该函数供定时器调用
 */
static void RefreshTimerTask(void)
{
    WM_HWIN hItem;
    char str[32];
    if(FlowTitFlow.bPWMAddOrFlag == TRUE || ((GetTitFlowCode() + 400) <= GetRealFlowCode()))
    {
        FlowTitFlow.bPWMAddOrFlag = FALSE;
        ClearFilterData(TIT_FLWO_CODE, GetRealFlowCode());
    }
    s_s16PwmTmp = GetBldcmPwm();
    s_s16CodeTmp = GetRealFlowCode();
    s_s16DevFlowTmp = GetFlow();
    s_u16TsiFlowTmp = get_standard_dev_flow();
    GUI_TIMER_Restart(g_RefreshTimer);
    WM_Invalidate(WM_GetDialogItem(g_AutoFlowCaliHwin, ID_PALETTLE));

    static uint32_t s_wResfreshTick = 0;
    if(!task_standby(&s_wResfreshTick, 1000))
    {
        WM_Invalidate(WM_GetDialogItem(g_AutoFlowCaliHwin, ID_TEXT_1));
        WM_Invalidate(WM_GetDialogItem(g_AutoFlowCaliHwin, ID_MODEINFO));
    }
    //更新数据刷新
    check_auto_calib_done_status();
}

/**
 * @brief   该函数供流量自动标定界面使用，用于创建一个定时器
 *          该定时器用于刷新当前界面
 * @note    该函数供流量自动标定界面使用
 */
static void CreateRefreshTimer(void)
{
    GUI_TIMER_CREATE(g_RefreshTimer, RefreshTimerTask, REFRESH_FLOW_CALI_VALUE_TIMER_ID, 0, 0);

    if(get_auto_calib_enable() > 0)
    {
        GUI_TIMER_SetPeriod(g_RefreshTimer, (GUI_TIMER_TIME)(300 / (1000 / OS_CFG_TICK_RATE_HZ)));
    }
    else
    {
        GUI_TIMER_SetPeriod(g_RefreshTimer, (GUI_TIMER_TIME)(600 / (1000 / OS_CFG_TICK_RATE_HZ)));
    }
    GUI_TIMER_Restart(g_RefreshTimer);
}
// USER END

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/

// USER START (Optionally insert additional static data)
// USER END

/*********************************************************************
*
*       _aDialogCreate
*/

static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] =
{
    { WINDOW_CreateIndirect, "Window",  ID_WINDOW_0, 0, STATUSBAR_HIGHT, SCREEN_WIDTH, SCREEN_HEIGHT - STATUSBAR_HIGHT, 0, 0x0, 0 },

    { BUTTON_CreateIndirect, "Start",   ID_BUTTON_0, SCREEN_WIDTH * 1 / (BUTTON_NUM_MAX * 2) - BUTTON_WIDTH / 2, SCREEN_HEIGHT - BUTTON_HIGHT - STATUSBAR_HIGHT - AEStTHTIC_HIGHT, BUTTON_WIDTH + BUTTON_INTERVAL, BUTTON_HIGHT, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "ReTest",  ID_BUTTON_1, SCREEN_WIDTH * 3 / (BUTTON_NUM_MAX * 2) - BUTTON_WIDTH / 2, SCREEN_HEIGHT - BUTTON_HIGHT - STATUSBAR_HIGHT - AEStTHTIC_HIGHT, BUTTON_WIDTH + BUTTON_INTERVAL, BUTTON_HIGHT, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Exit",    ID_BUTTON_2, SCREEN_WIDTH * 5 / (BUTTON_NUM_MAX * 2) - BUTTON_WIDTH / 2, SCREEN_HEIGHT - BUTTON_HIGHT - STATUSBAR_HIGHT - AEStTHTIC_HIGHT, BUTTON_WIDTH + BUTTON_INTERVAL, BUTTON_HIGHT, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Mode",    ID_BUTTON_3, SCREEN_WIDTH * 7 / (BUTTON_NUM_MAX * 2) - BUTTON_WIDTH / 2, SCREEN_HEIGHT - BUTTON_HIGHT - STATUSBAR_HIGHT - AEStTHTIC_HIGHT, BUTTON_WIDTH + BUTTON_INTERVAL, BUTTON_HIGHT, 0, 0x0, 0 },
    { BUTTON_CreateIndirect, "Debug",   ID_BUTTON_4, SCREEN_WIDTH * 9 / (BUTTON_NUM_MAX * 2) - BUTTON_WIDTH / 2, SCREEN_HEIGHT - BUTTON_HIGHT - STATUSBAR_HIGHT - AEStTHTIC_HIGHT, BUTTON_WIDTH + BUTTON_INTERVAL, BUTTON_HIGHT, 0, 0x0, 0 },
    { TEXT_CreateIndirect, "AutoCali",      ID_TEXT_0,  0, 0, SCREEN_WIDTH, TEXT_HIGHT, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "DateTime",      ID_TEXT_1,  0, SCREEN_HEIGHT - STATUSBAR_HIGHT - 3 * AEStTHTIC_HIGHT - BUTTON_HIGHT - 2 * TEXT_HIGHT, SCREEN_WIDTH, TEXT_HIGHT * 2 + AEStTHTIC_HIGHT, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "CaliInfo",      ID_PALETTLE, 0, 0, SCREEN_WIDTH, CALIINFO_HIGHT, 0, 0x64, 0 },
    { TEXT_CreateIndirect, "ModeInfo",      ID_MODEINFO, 0, CALIINFO_HIGHT, SCREEN_WIDTH, MODEINFO_HIGHT, 0, 0x64, 0 },

};
/*********************************************************************
*
*       Static code
*
**********************************************************************
*/

/**
 * @brief 该函数是一个回调函数，用于刷新流量自动标定界面
 *        该函数可以刷新当前界面
 * @param[in]  pMsg  该参数是一个WM_MESSAGE指针，用于传递WM_MESSAGE结构体
 * @return      void
 */
static void draw_info_callback(WM_MESSAGE *pMsg)
{
    switch(pMsg->MsgId)
    {
        case WM_PAINT:
        {
            int8_t sbStr[84];
            GUI_RECT rt;

            WM_GetWinRect(&rt);
            GUI_MoveRect(&rt, -rt.x0, -rt.y0);
            GUI_SetTextMode(GUI_TM_TRANS);

            GUI_SetBkColor(GUI_BLACK);
            GUI_Clear();
            GUI_SetColor(GUI_WHITE);
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_24);
#else
            GUI_SetFont(FONT_18);
#endif
            rt.y0 += AEStTHTIC_HIGHT;
            rt.x0 = 0;
            snprintf((char *)sbStr, sizeof(sbStr), "TSI:%.1f L/min", ((float)s_u16TsiFlowTmp) / 10.0f);
            GUI_DispStringInRect((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP);

            rt.x0 += SCREEN_WIDTH / VALUE_INFO_NUM_MAX;
            snprintf((char *)sbStr, sizeof(sbStr), "CODE:%d", s_s16CodeTmp);
            GUI_DispStringInRect((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP);

#if (LCD_TYPE == LCD_28_TFT)
            rt.x0 = 0;
            rt.y0 += GUI_GetFontSizeY() + AEStTHTIC_HIGHT;
#else
            rt.x0 += SCREEN_WIDTH / VALUE_INFO_NUM_MAX;
#endif
            snprintf((char *)sbStr, sizeof(sbStr), "DEV:%.1f L/min", ((float)s_s16DevFlowTmp) / 10.0f);
            GUI_DispStringInRect((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP);

            rt.x0 = 0;
            rt.y0 += GUI_GetFontSizeY() + AEStTHTIC_HIGHT;
            snprintf((char *)sbStr, sizeof(sbStr), "PWM:%d", s_s16PwmTmp);
            GUI_DispStringInRect((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP);

            rt.x0 += SCREEN_WIDTH / VALUE_INFO_NUM_MAX;
            snprintf((char *)sbStr, sizeof(sbStr), "INDEX:%d", get_auto_flow_calib_index());
            GUI_DispStringInRect((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP);

            break;
        }
        default:
        {
            TEXT_Callback(pMsg);
            break;
        }
    }
}


static void draw_calib_modeinfo_callback(WM_MESSAGE *pMsg)
{
    switch(pMsg->MsgId)
    {
        case WM_PAINT:
        {
            int8_t sbStr[84];
            GUI_RECT rt;

            WM_GetWinRect(&rt);
            GUI_MoveRect(&rt, -rt.x0, -rt.y0);
            GUI_SetTextMode(GUI_TM_TRANS);

            GUI_SetBkColor(GUI_BLACK);
            GUI_Clear();
            GUI_SetColor(GUI_WHITE);
#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_24);
#else
            GUI_SetFont(FONT_18);
#endif
            if(get_auto_calib_mode() == AUTO_CALIB_DATA_FROM_DEV)
            {
                GUI_DispStringInRect(GetMultiLanguageString(CALI_MODE_DEV_IDX), &rt, GUI_TA_LEFT | GUI_TA_TOP);
            }
            else if(get_auto_calib_mode() == AUTO_CALIB_DATA_FROM_TSI)
            {
                GUI_DispStringInRect(GetMultiLanguageString(CALI_MODE_TSI_IDX), &rt, GUI_TA_LEFT | GUI_TA_TOP);
            }
            else if(get_auto_calib_mode() == AUTO_CALIB_DATA_HOST_OUTPUT)
            {
                GUI_DispStringInRect(GetMultiLanguageString(CALI_MODE_HOST_IDX), &rt, GUI_TA_LEFT | GUI_TA_TOP);
            }

            //校准状态显示
            rt.x0 = 0;
            rt.y0 += GUI_GetFontSizeY() + AEStTHTIC_HIGHT;
            if(get_auto_flow_calib_status() == AUTO_FLOW_CALIB_DONE)
            {
                if(get_current_r2() > AUTO_FLOW_R2_STANDARD)
                {
                    set_auto_calib_exec_mode(EXEC_FLOW_RETEST, 1);
//                    s_wAutoFlowCalibTotalTime = 0;
                    FlowTitFlow.bPWMAddOrFlag = FALSE;
                    s_s8AutoCailFlag = 1;
                }
                else
                {
                    GUI_SetColor(GUI_RED);
                    snprintf((char *)sbStr, sizeof(sbStr), "%s%c%s: %ds R2: %0.5f",
                                GetMultiLanguageString(AUTOCALI_OK_IDX),
                                LCD_TYPE == LCD_28_TFT ? '\n' : ' ',
                                GetMultiLanguageString(AUTOCALI_TOTALTIME_IDX),
                                s_wAutoFlowCalibTotalTime / 1000,
                                get_current_r2());
                    GUI_DispStringInRectWrap((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                }
                
            }
            else if(get_auto_flow_calib_status() == AUTO_FLOW_CALIB_RETEST_DONE)
            {
                AutoFlowCalibData_t *pCalibData = get_auto_flow_calib_tmp_data();
                //异常数据索引
                s_sbErrorDataIndex = check_auto_calib_retest_data();
                if(s_s8AutoCailFlag)
                {
                    GUI_SetColor(GUI_GREEN);
                    snprintf((char *)sbStr, sizeof(sbStr), "%s%c%s: %ds R2: %0.5f",
                                GetMultiLanguageString(AUTOCALI_OK_IDX),
                                LCD_TYPE == LCD_28_TFT ? '\n' : ' ',
                                GetMultiLanguageString(AUTOCALI_TOTALTIME_IDX),
                                s_wAutoFlowCalibTotalTime / 1000,
                                get_current_r2());
                    GUI_DispStringInRectWrap((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
                    if(s_sbErrorDataIndex == -1)
                    {
#if LCD_TYPE == LCD_28_TFT 
                        rt.y0 += 2 * GUI_GetFontSizeY() + AEStTHTIC_HIGHT;
#else
                        rt.y0 += GUI_GetFontSizeY() + AEStTHTIC_HIGHT;
#endif
                    }
                }
                if(s_sbErrorDataIndex != -1)
                {
                    GUI_SetTextMode(GUI_TM_NORMAL);
                    GUI_SetBkColor(GUI_RED);
                    snprintf((char *)sbStr, sizeof(sbStr), "%s TSI:%0.1fL/min,%cCode:%d, DEV:%0.1fL/min",
                                    GetMultiLanguageString(AUTOCALI_RETEST_ERR_IDX),
                                    pCalibData->shPhyFlow[s_sbErrorDataIndex] / 10.0f,
                                    LCD_TYPE == LCD_28_TFT ? '\n' : ' ',
                                    pCalibData->shSensorCode[s_sbErrorDataIndex],
                                    pCalibData->shOtherVal[s_sbErrorDataIndex] / 10.0f);
                }
                else
                {
                    GUI_SetColor(GUI_GREEN);
                    snprintf((char *)sbStr, sizeof(sbStr), "%s %s: %ds",
                                    GetMultiLanguageString(AUTOCALI_RETEST_OK_IDX),
                                    GetMultiLanguageString(AUTOCALI_TOTALTIME_IDX),
                                    s_wAutoFlowCalibTotalTime / 1000);
                }
                GUI_DispStringInRectWrap((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);    
            }
            else if(get_auto_flow_calib_status() == AUTO_FLOW_CALIB_DEBUG_DONE)
            {
                snprintf((char *)sbStr, sizeof(sbStr), "Auto Flow Debug Completed! %s: %ds R2: %0.5f",
                                GetMultiLanguageString(AUTOCALI_TOTALTIME_IDX),
                                s_wAutoFlowCalibTotalTime / 1000,
                                get_current_r2());
                GUI_DispStringInRectWrap((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            }
            else if(get_auto_flow_calib_status() == AUTO_FLOW_CALIB_TOOL_UNCONNECT)
            {
                GUI_SetTextMode(GUI_TM_NORMAL);
                GUI_SetBkColor(GUI_RED);
                snprintf((char *)sbStr, sizeof(sbStr), GetMultiLanguageString(AUTOCALI_TIMEOUT_IDX));
                GUI_DispStringInRectWrap((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            }
            else if(get_auto_flow_calib_status() == AUTO_FLOW_CALIB_TIMEOUT)
            {
                GUI_SetTextMode(GUI_TM_NORMAL);
                GUI_SetBkColor(GUI_RED);
                snprintf((char *)sbStr, sizeof(sbStr), GetMultiLanguageString(AUTOCALI_MAX_FLOW_ERR_IDX));
                GUI_DispStringInRectWrap((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            }
            else if(get_auto_flow_calib_status() == AUTO_FLOW_CALIB_DATA_ERR)
            {
                GUI_SetTextMode(GUI_TM_NORMAL);
                GUI_SetBkColor(GUI_RED);
                snprintf((char *)sbStr, sizeof(sbStr), GetMultiLanguageString(AUTOCALI_DATA_ERR_IDX));
                GUI_DispStringInRectWrap((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP, GUI_WRAPMODE_WORD);
            }
            break;
        }
        default:
        {
            TEXT_Callback(pMsg);
            break;
        }
    }
}

/**
 * @brief 画面绘制回调函数
 * 该函数处理WM_PAINT消息，负责绘制AutoFlowCalib窗口中的校准日期和时间
 * @param[in] pMsg  WM_Message结构体指针
 */
static void draw_calib_datetime_callback(WM_MESSAGE *pMsg)
{
    switch(pMsg->MsgId)
    {
        case WM_PAINT:
        {
            int8_t sbStr[64];
            GUI_RECT rt;
            WM_GetWinRect(&rt);
            GUI_MoveRect(&rt, -rt.x0, -rt.y0);
            GUI_SetTextMode(GUI_TM_TRANS);

            GUI_SetBkColor(GUI_BLACK);
            GUI_Clear();
            GUI_SetColor(GUI_WHITE);

#if (LCD_TYPE == LCD_5_TFT)
            GUI_SetFont(FONT_24);
#else
            GUI_SetFont(FONT_18);
#endif
            //校准日期显示
            const AutoFlowCalibData_t *pFlowCalibData = g_ConfigSave.get_auto_flow_calib_data();
            struct tm tmV;
            tmV = Time_ConvUnixToCalendar(pFlowCalibData->wDateTime);
            snprintf((char *)sbStr, sizeof(sbStr), "%s%04d-%02d-%02d %02d:%02d:%02d",
                            GetMultiLanguageString(CALI_TIME_IDX),
                            1900 + tmV.tm_year,
                            1 + tmV.tm_mon, tmV.tm_mday, tmV.tm_hour, tmV.tm_min, tmV.tm_sec);
            GUI_DispStringInRect((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP);
            rt.y0 += GUI_GetFontDistY() + AEStTHTIC_HIGHT;
            if(pFlowCalibData->bCalibType == FLOW_CALIB_TYPE_AUTO)
            {
                snprintf((char *)sbStr, sizeof(sbStr), "Type：Auto");
            }
            else
            {
                snprintf((char *)sbStr, sizeof(sbStr), " Type：Manual");
            }
            GUI_DispStringInRect((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP);
            //R2值判断
            rt.x0 += SCREEN_WIDTH / 2 + AEStTHTIC_HIGHT;
            if(get_auto_flow_r2() > AUTO_FLOW_R2_STANDARD)
            {
                GUI_SetColor(GUI_WHITE);
            }
            else
            {
                GUI_SetColor(GUI_RED);
            }
            snprintf((char *)sbStr, sizeof(sbStr), "R2: %0.5f", get_auto_flow_r2());
            GUI_DispStringInRect((char *)sbStr, &rt, GUI_TA_LEFT | GUI_TA_TOP);
            break;
        }
        default:
        {
            TEXT_Callback(pMsg);
            break;
        }
    }
}

/*********************************************************************
*
*       _cbDialog
*/
static void _cbDialog(WM_MESSAGE *pMsg)
{
    WM_HWIN hItem;
    int     NCode;
    int     Id;
    // USER START (Optionally insert additional variables)
    U8 i;
    int8_t s8tmp = 0;
    char str[128];
    struct tm tmV;
    // USER END

    switch(pMsg->MsgId)
    {
        case WM_INIT_DIALOG:
            //
            // Initialization of 'Window'
            //
            hItem = pMsg->hWin;
            WINDOW_SetBkColor(hItem, GUI_MAKE_COLOR(0x00000000));
            //
            // Initialization of 'Start'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0);
            BUTTON_SetText(hItem, "Start");
            //
            // Initialization of 'OK'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_1);
            BUTTON_SetText(hItem, "OK");
            //
            // Initialization of 'Exit'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_2);
            BUTTON_SetText(hItem, "Exit");
            //
            // Initialization of 'Mode'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_3);
            BUTTON_SetText(hItem, "Mode");
            //
            // Initialization of 'Debug'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_BUTTON_4);
#if (LCD_TYPE == LCD_28_TFT)
            BUTTON_SetText(hItem, "De\nbug");
#endif
            //
            // Initialization of 'AutoFlowCali'
            //
            hItem = WM_GetDialogItem(pMsg->hWin, ID_TEXT_0);
            TEXT_SetText(hItem, "Auto Flow Calibration");
            TEXT_SetTextColor(hItem, GUI_MAKE_COLOR(0x00FFFFFF));
            TEXT_SetTextAlign(hItem, GUI_TA_HCENTER | GUI_TA_VCENTER);

            // USER START (Optionally insert additional code for further widget initialization)
            for(i = 0; i < BUTTON_NUM_MAX; i++)
            {
#if (LCD_TYPE == LCD_5_TFT)
                BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), FONT_24);
#else
                BUTTON_SetFont(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i), FONT_18);
#endif
                _SetButtonSkin(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0 + i));
            }

            TEXT_SetText(WM_GetDialogItem(pMsg->hWin, ID_TEXT_0), GetMultiLanguageString(CALI_AUTOCALI_IDX));
            WM_SetCallback(WM_GetDialogItem(pMsg->hWin, ID_PALETTLE), draw_info_callback);
            WM_SetCallback(WM_GetDialogItem(pMsg->hWin, ID_TEXT_1), draw_calib_datetime_callback);
            WM_SetCallback(WM_GetDialogItem(pMsg->hWin, ID_MODEINFO), draw_calib_modeinfo_callback);
            //按钮
            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_0), GetMultiLanguageString(CALI_START_AUTOCALI_IDX));
            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_1), GetMultiLanguageString(CALI_RETEST_EXPORT_DATA));
            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_2), GetMultiLanguageString(AUTOCALI_EXIT_IDX));
            BUTTON_SetText(WM_GetDialogItem(pMsg->hWin, ID_BUTTON_3), GetMultiLanguageString(CALI_MODE_CHECK_IDX));
            // USER END
            break;
        case WM_NOTIFY_PARENT:
            Id    = WM_GetId(pMsg->hWinSrc);
            NCode = pMsg->Data.v;
            switch(NCode)
            {
                case WM_NOTIFICATION_CLICKED:
                    switch(Id)
                    {
                        //Start Auto Flow Cali
                        case ID_BUTTON_0:
                            set_auto_calib_exec_mode(EXEC_FLOW_CALIBRATION, 1);
                            s_wAutoFlowCalibTotalTime = 0;
                            FlowTitFlow.bPWMAddOrFlag = TRUE;
                            s_s8AutoCailFlag = 0;
                            break;
                        //ReTest
                        case ID_BUTTON_1:
                            set_auto_calib_exec_mode(EXEC_FLOW_RETEST, 1);
                            s_wAutoFlowCalibTotalTime = 0;
                            FlowTitFlow.bPWMAddOrFlag = FALSE;
                            s_s8AutoCailFlag = 0;
                            break;
                        //Exit
                        case ID_BUTTON_2:
                            ShowAutoFlowCali(0);
                            break;
                        //Mode
                        case ID_BUTTON_3:
                        {
                            if((get_auto_calib_mode() + 1) < AUTO_CALIB_MODE_NUM_MAX)
                            {
                                set_auto_calib_mode(get_auto_calib_mode() + 1);
                            }
                            else
                            {
                                set_auto_calib_mode(AUTO_CALIB_DATA_FROM_DEV);
                            }
                            break;
                        }
                        //Debug
                        case ID_BUTTON_4:
                        {
                            set_auto_calib_exec_mode(EXEC_FLOW_DEBUG, 1);
                            s_wAutoFlowCalibTotalTime = 0;
                            FlowTitFlow.bPWMAddOrFlag = FALSE;
                            break;
                        }
                        default:
                            WM_DefaultProc(pMsg);
                            break;
                    }
                    break;
                case WM_NOTIFICATION_RELEASED:
                    break;
                default:
                    break;
            }
        // USER START (Optionally insert additional message handling)
        // USER END
        default:
            WM_DefaultProc(pMsg);
            break;
    }
}

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/

// USER START (Optionally insert additional public code)
void ShowAutoFlowCali(U8 Flag)
{
    if(Flag)
    {
#if CONFIG_DEV_CALIB_TYPE == CONFIG_DEV_AS_CALIB_SLAVE
        set_auto_calib_exec_mode(EXEC_FLOW_CALIBRATION, 0);
        set_auto_calib_mode(AUTO_CALIB_DATA_FROM_DEV);
#elif CONFIG_DEV_CALIB_TYPE == CONFIG_DEV_AS_CALIB_HOST
        set_auto_calib_exec_mode(EXEC_FLOW_CALIBRATION, 0);
        set_auto_calib_mode(AUTO_CALIB_DATA_HOST_OUTPUT);
#endif
        if(g_AutoFlowCaliHwin == NULL)
        {
            g_AutoFlowCaliHwin = GUI_CreateDialogBox(_aDialogCreate, GUI_COUNTOF(_aDialogCreate), _cbDialog, WM_HBKWIN, 0, 0);
        }
        ShowProcess();
        g_WindowDrv.PushWindow(&g_AutoFlowCaliWInfo);
        g_ConfigSave.StartFlowCali(Flag, 0);
    }
    else
    {
#if CONFIG_DEV_CALIB_TYPE == CONFIG_DEV_AS_CALIB_SLAVE
        set_auto_calib_exec_mode(EXEC_FLOW_CALIBRATION, 0);
        set_auto_calib_mode(AUTO_CALIB_DATA_FROM_DEV);
#elif CONFIG_DEV_CALIB_TYPE == CONFIG_DEV_AS_CALIB_HOST
        set_auto_calib_exec_mode(EXEC_FLOW_CALIBRATION, 0);
        set_auto_calib_mode(AUTO_CALIB_DATA_HOST_OUTPUT);
#endif
        GUI_TIMER_DELETE(g_RefreshTimer);
        WM_HideWindow(g_AutoFlowCaliHwin);
        //WM_DeleteWindow(g_AutoFlowCaliHwin);
        //g_AutoFlowCaliHwin = NULL;
        g_WindowDrv.PopWindow(&g_AutoFlowCaliWInfo);
        g_ConfigSave.StartFlowCali(Flag, 0);
    }
}

static void ShowProcess(void)
{
    WM_SetFocus(WM_GetDialogItem(g_AutoFlowCaliHwin, ID_BUTTON_0));
    WM_ShowWindow(g_AutoFlowCaliHwin);
    CreateRefreshTimer();
}
// USER END

/*************************** End of file ****************************/

